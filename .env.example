# Environment Configuration for Trading Agent
# Copy this file to .env and fill in your actual values

# Environment
ENVIRONMENT=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=trading_db
DB_USER=postgres
DB_PASSWORD=your_secure_password_here
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# API Configuration
API_HOST=0.0.0.0
API_PORT=5000
SECRET_KEY=your_very_secure_secret_key_here_at_least_32_characters
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
RATE_LIMIT=100 per hour

# Flask Configuration
FLASK_ENV=development

# Data Provider Configuration
AKSHARE_TIMEOUT=30
CACHE_TTL=300
MAX_RETRIES=3
RETRY_DELAY=1

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=logs/trading_agent.log
LOG_MAX_BYTES=10485760
LOG_BACKUP_COUNT=5

# External API Keys (if needed)
# TUSHARE_TOKEN=your_tushare_token_here
# ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# Redis Configuration (for caching)
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_DB=0
# REDIS_PASSWORD=

# Security Settings
# JWT_SECRET_KEY=your_jwt_secret_key_here
# JWT_ACCESS_TOKEN_EXPIRES=3600
# JWT_REFRESH_TOKEN_EXPIRES=86400

# Performance Settings
# MAX_WORKERS=4
# REQUEST_TIMEOUT=30
# CONNECTION_POOL_SIZE=20
