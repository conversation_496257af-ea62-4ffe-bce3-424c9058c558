# 中国股票交易分析Web应用技术架构文档

## 1. Architecture design

```mermaid
graph TD
    A[用户浏览器] --> B[React前端应用]
    B --> C[Python Flask后端]
    C --> D[akshare数据源]
    C --> E[Tushare API]
    C --> F[Redis缓存]
    C --> G[SQLite数据库]

    subgraph "前端层"
        B
    end

    subgraph "后端层"
        C
        F
    end

    subgraph "数据层"
        G
    end

    subgraph "外部数据源"
        D
        E
    end
```

## 2. Technology Description

- Frontend: React@18 + TypeScript + Tailwind CSS + Vite + Chart.js
- Backend: Python Flask + akshare + tushare + pandas + numpy
- Database: SQLite (用户数据、历史分析记录)
- Cache: Redis (股票数据缓存、计算结果缓存)
- Data Sources: akshare库 + Tushare API

## 3. Route definitions

| Route | Purpose |
|-------|---------|
| / | 首页，展示产品介绍和热门股票推荐 |
| /analysis | 股票分析页面，单只股票技术分析 |
| /screening | 股票筛选页面，批量筛选和结果展示 |
| /user | 用户中心，账户管理和历史记录 |
| /login | 用户登录页面 |
| /register | 用户注册页面 |

## 4. API definitions

### 4.1 Core API

股票数据获取相关
```
GET /api/stock/info/{stock_code}
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| stock_code | string | true | 股票代码，如000001.SZ |
| period | string | false | 时间周期，默认daily |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| code | string | 股票代码 |
| name | string | 股票名称 |
| price_data | array | K线数据数组 |
| magic_nine | array | 神奇九转指标数据 |
| macd_data | array | MACD指标数据 |
| signals | array | 买卖信号数组 |

技术指标计算
```
POST /api/analysis/calculate
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| stock_code | string | true | 股票代码 |
| indicators | array | true | 需要计算的指标列表 |
| start_date | string | false | 开始日期 |
| end_date | string | false | 结束日期 |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| success | boolean | 计算是否成功 |
| data | object | 指标计算结果 |
| signals | array | 生成的交易信号 |

股票筛选
```
POST /api/screening/filter
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| magic_nine_condition | object | true | 神奇九转筛选条件 |
| macd_condition | object | true | MACD筛选条件 |
| market_cap_range | object | false | 市值范围 |
| industry_filter | array | false | 行业筛选 |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| total_count | integer | 符合条件的股票总数 |
| stocks | array | 筛选结果股票列表 |
| execution_time | float | 筛选执行时间 |

## 5. Server architecture diagram

```mermaid
graph TD
    A[客户端请求] --> B[Flask路由层]
    B --> C[业务逻辑层]
    C --> D[数据访问层]
    D --> E[缓存层 Redis]
    D --> F[数据库层 SQLite]
    C --> G[外部API层]
    G --> H[akshare库]
    G --> I[Tushare API]
    C --> J[技术指标计算层]
    J --> K[神奇九转算法]
    J --> L[MACD计算]
    J --> M[信号生成逻辑]

    subgraph 服务器端
        B
        C
        D
        E
        F
        G
        J
        K
        L
        M
    end
```

## 6. Data model

### 6.1 Data model definition

```mermaid
erDiagram
    USER ||--o{ ANALYSIS_HISTORY : creates
    USER ||--o{ SCREENING_HISTORY : creates
    STOCK_INFO ||--o{ ANALYSIS_HISTORY : analyzed_in
    STOCK_INFO ||--o{ PRICE_DATA : has
    ANALYSIS_HISTORY ||--o{ SIGNAL_RECORD : generates

    USER {
        int id PK
        string email
        string password_hash
        string username
        string user_type
        datetime created_at
        datetime updated_at
    }
    
    STOCK_INFO {
        string stock_code PK
        string stock_name
        string market
        string industry
        float market_cap
        datetime last_updated
    }
    
    PRICE_DATA {
        int id PK
        string stock_code FK
        date trade_date
        float open_price
        float high_price
        float low_price
        float close_price
        bigint volume
        datetime created_at
    }
    
    ANALYSIS_HISTORY {
        int id PK
        int user_id FK
        string stock_code FK
        json analysis_result
        json signals
        datetime created_at
    }
    
    SCREENING_HISTORY {
        int id PK
        int user_id FK
        json filter_conditions
        json screening_result
        int result_count
        datetime created_at
    }
    
    SIGNAL_RECORD {
        int id PK
        int analysis_id FK
        string signal_type
        float signal_strength
        date signal_date
        float target_price
        string status
        datetime created_at
    }
```

### 6.2 Data Definition Language

用户表 (users)
```sql
-- 创建用户表
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    username VARCHAR(100) NOT NULL,
    user_type VARCHAR(20) DEFAULT 'basic' CHECK (user_type IN ('basic', 'premium')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
```

股票信息表 (stock_info)
```sql
-- 创建股票信息表
CREATE TABLE stock_info (
    stock_code VARCHAR(20) PRIMARY KEY,
    stock_name VARCHAR(100) NOT NULL,
    market VARCHAR(10) NOT NULL,
    industry VARCHAR(50),
    market_cap DECIMAL(15,2),
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_stock_info_market ON stock_info(market);
CREATE INDEX idx_stock_info_industry ON stock_info(industry);
```

价格数据表 (price_data)
```sql
-- 创建价格数据表
CREATE TABLE price_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    stock_code VARCHAR(20) NOT NULL,
    trade_date DATE NOT NULL,
    open_price DECIMAL(10,3) NOT NULL,
    high_price DECIMAL(10,3) NOT NULL,
    low_price DECIMAL(10,3) NOT NULL,
    close_price DECIMAL(10,3) NOT NULL,
    volume BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (stock_code) REFERENCES stock_info(stock_code)
);

-- 创建索引
CREATE UNIQUE INDEX idx_price_data_stock_date ON price_data(stock_code, trade_date);
CREATE INDEX idx_price_data_trade_date ON price_data(trade_date DESC);
```

分析历史表 (analysis_history)
```sql
-- 创建分析历史表
CREATE TABLE analysis_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    stock_code VARCHAR(20) NOT NULL,
    analysis_result TEXT, -- JSON格式存储分析结果
    signals TEXT, -- JSON格式存储信号数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (stock_code) REFERENCES stock_info(stock_code)
);

-- 创建索引
CREATE INDEX idx_analysis_history_user_id ON analysis_history(user_id);
CREATE INDEX idx_analysis_history_stock_code ON analysis_history(stock_code);
CREATE INDEX idx_analysis_history_created_at ON analysis_history(created_at DESC);
```

筛选历史表 (screening_history)
```sql
-- 创建筛选历史表
CREATE TABLE screening_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    filter_conditions TEXT, -- JSON格式存储筛选条件
    screening_result TEXT, -- JSON格式存储筛选结果
    result_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 创建索引
CREATE INDEX idx_screening_history_user_id ON screening_history(user_id);
CREATE INDEX idx_screening_history_created_at ON screening_history(created_at DESC);
```

信号记录表 (signal_record)
```sql
-- 创建信号记录表
CREATE TABLE signal_record (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    analysis_id INTEGER NOT NULL,
    signal_type VARCHAR(10) NOT NULL CHECK (signal_type IN ('buy', 'sell', 'hold')),
    signal_strength DECIMAL(3,2) NOT NULL, -- 信号强度 0.00-1.00
    signal_date DATE NOT NULL,
    target_price DECIMAL(10,3),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'executed', 'expired')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (analysis_id) REFERENCES analysis_history(id)
);

-- 创建索引
CREATE INDEX idx_signal_record_analysis_id ON signal_record(analysis_id);
CREATE INDEX idx_signal_record_signal_date ON signal_record(signal_date DESC);
CREATE INDEX idx_signal_record_signal_type ON signal_record(signal_type);
```

初始化数据
```sql
-- 插入示例股票信息
INSERT INTO stock_info (stock_code, stock_name, market, industry, market_cap) VALUES
('000001.SZ', '平安银行', 'SZ', '银行', 280000000000),
('000002.SZ', '万科A', 'SZ', '房地产', 250000000000),
('600000.SH', '浦发银行', 'SH', '银行', 180000000000),
('600036.SH', '招商银行', 'SH', '银行', 1200000000000),
('000858.SZ', '五粮液', 'SZ', '食品饮料', 800000000000);
```