from flask import Flask, jsonify, request
from flask_cors import CORS
import logging
import os
from datetime import datetime, timedelta

# Import our modules
from data.stock_data import StockDataProvider
from indicators.magic_nine_turns import MagicNineTurns
from indicators.macd import MACD
from utils.signal_generator import SignalGenerator
from database.db_manager import DatabaseManager

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize components
try:
    db_manager = DatabaseManager()
    stock_data_provider = StockDataProvider()
    magic_nine = MagicNineTurns()
    macd = MACD()
    signal_generator = SignalGenerator()
    logger.info("All components initialized successfully")
except Exception as e:
    logger.error(f"Error initializing components: {e}")
    db_manager = None
    stock_data_provider = None
    magic_nine = None
    macd = None
    signal_generator = None

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/api/stock/info/<stock_code>', methods=['GET'])
def get_stock_info(stock_code):
    """Get stock information and technical analysis"""
    try:
        # Get query parameters
        period = request.args.get('period', 'daily')
        days = int(request.args.get('days', 100))
        
        # Fetch stock data
        stock_data = stock_data_provider.get_stock_data(stock_code, period, days)
        if not stock_data:
            return jsonify({'error': 'Stock data not found'}), 404
        
        # Calculate technical indicators
        magic_nine_data = magic_nine.calculate(stock_data)
        macd_data = macd_calculator.calculate(stock_data)
        
        # Generate signals
        signals = signal_generator.generate_signals(magic_nine_data, macd_data, stock_data)
        
        # Get stock basic info
        stock_info = stock_data_provider.get_stock_basic_info(stock_code)
        
        return jsonify({
            'code': stock_code,
            'name': stock_info.get('name', ''),
            'price_data': stock_data,
            'magic_nine': magic_nine_data,
            'macd_data': macd_data,
            'signals': signals,
            'last_updated': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analysis/calculate', methods=['POST'])
def calculate_analysis():
    """Calculate technical analysis for given parameters"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        indicators = data.get('indicators', ['magic_nine', 'macd'])
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        if not stock_code:
            return jsonify({'error': 'Stock code is required'}), 400
        
        # Fetch stock data
        stock_data = stock_data_provider.get_stock_data(
            stock_code, 
            start_date=start_date, 
            end_date=end_date
        )
        
        if not stock_data:
            return jsonify({'error': 'Stock data not found'}), 404
        
        result = {'success': True, 'data': {}, 'signals': []}
        
        # Calculate requested indicators
        if 'magic_nine' in indicators:
            result['data']['magic_nine'] = magic_nine.calculate(stock_data)
        
        if 'macd' in indicators:
            result['data']['macd'] = macd_calculator.calculate(stock_data)
        
        # Generate combined signals if both indicators are calculated
        if 'magic_nine' in result['data'] and 'macd' in result['data']:
            result['signals'] = signal_generator.generate_signals(
                result['data']['magic_nine'],
                result['data']['macd'],
                stock_data
            )
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/screening/filter', methods=['POST'])
def filter_stocks():
    """Filter stocks based on technical analysis criteria"""
    try:
        data = request.get_json()
        magic_nine_condition = data.get('magic_nine_condition', {})
        macd_condition = data.get('macd_condition', {})
        market_cap_range = data.get('market_cap_range', {})
        industry_filter = data.get('industry_filter', [])
        
        # Get list of stocks to screen
        stock_list = stock_data_provider.get_stock_list(
            market_cap_range=market_cap_range,
            industry_filter=industry_filter
        )
        
        filtered_stocks = []
        start_time = datetime.now()
        
        for stock_code in stock_list:
            try:
                # Get stock data
                stock_data = stock_data_provider.get_stock_data(stock_code, days=60)
                if not stock_data:
                    continue
                
                # Calculate indicators
                magic_nine_data = magic_nine.calculate(stock_data)
                macd_data = macd_calculator.calculate(stock_data)
                
                # Check if stock meets criteria
                if signal_generator.meets_criteria(
                    magic_nine_data, 
                    macd_data, 
                    magic_nine_condition, 
                    macd_condition
                ):
                    # Generate signals for this stock
                    signals = signal_generator.generate_signals(
                        magic_nine_data, macd_data, stock_data
                    )
                    
                    stock_info = stock_data_provider.get_stock_basic_info(stock_code)
                    filtered_stocks.append({
                        'code': stock_code,
                        'name': stock_info.get('name', ''),
                        'price': stock_data[-1]['close'] if stock_data else 0,
                        'change_pct': stock_info.get('change_pct', 0),
                        'signals': signals,
                        'signal_strength': max([s.get('strength', 0) for s in signals], default=0)
                    })
                    
            except Exception as e:
                print(f"Error processing {stock_code}: {e}")
                continue
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return jsonify({
            'total_count': len(filtered_stocks),
            'stocks': filtered_stocks,
            'execution_time': execution_time
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stocks/list', methods=['GET'])
def get_stock_list():
    """Get list of available stocks"""
    try:
        market = request.args.get('market', 'all')  # 'SZ', 'SH', or 'all'
        limit = int(request.args.get('limit', 100))
        
        stocks = stock_data_provider.get_popular_stocks(market=market, limit=limit)
        return jsonify({
            'stocks': stocks,
            'count': len(stocks)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Technical Analysis Endpoints
@app.route('/api/technical/indicators/<stock_code>', methods=['GET'])
def get_technical_indicators(stock_code):
    """Get comprehensive technical indicators for a stock"""
    try:
        indicators = request.args.getlist('indicators')  # KDJ, RSI, BOLL, etc.
        period = request.args.get('period', '1d')
        days = int(request.args.get('days', 100))
        
        stock_data = stock_data_provider.get_stock_data(stock_code, period, days)
        if not stock_data:
            return jsonify({'error': 'Stock data not found'}), 404
        
        result = {'stock_code': stock_code, 'indicators': {}}
        
        # Calculate requested indicators
        if 'KDJ' in indicators or not indicators:
            from indicators.kdj import KDJ
            kdj = KDJ()
            result['indicators']['kdj'] = kdj.calculate(stock_data)
        
        if 'RSI' in indicators or not indicators:
            from indicators.rsi import RSI
            rsi = RSI()
            result['indicators']['rsi'] = rsi.calculate(stock_data)
        
        if 'BOLL' in indicators or not indicators:
            from indicators.bollinger_bands import BollingerBands
            boll = BollingerBands()
            result['indicators']['bollinger'] = boll.calculate(stock_data)
        
        if 'MACD' in indicators or not indicators:
            result['indicators']['macd'] = macd.calculate(stock_data)
        
        if 'MAGIC_NINE' in indicators or not indicators:
            result['indicators']['magic_nine'] = magic_nine.calculate(stock_data)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/technical/signals/<stock_code>', methods=['GET'])
def get_trading_signals(stock_code):
    """Get trading signals based on technical analysis"""
    try:
        timeframe = request.args.get('timeframe', '1d')
        
        stock_data = stock_data_provider.get_stock_data(stock_code, timeframe, 100)
        if not stock_data:
            return jsonify({'error': 'Stock data not found'}), 404
        
        # Calculate all indicators
        magic_nine_data = magic_nine.calculate(stock_data)
        macd_data = macd.calculate(stock_data)
        
        # Generate comprehensive signals
        signals = signal_generator.generate_comprehensive_signals(
            stock_data, magic_nine_data, macd_data
        )
        
        return jsonify({
            'stock_code': stock_code,
            'signals': signals,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Fundamental Analysis Endpoints
@app.route('/api/fundamental/analysis/<stock_code>', methods=['GET'])
def get_fundamental_analysis(stock_code):
    """Get fundamental analysis data"""
    try:
        from data.fundamental_data import FundamentalDataProvider
        fundamental_provider = FundamentalDataProvider()
        
        # Get financial data
        financial_data = fundamental_provider.get_financial_data(stock_code)
        valuation_data = fundamental_provider.get_valuation_metrics(stock_code)
        industry_analysis = fundamental_provider.get_industry_analysis(stock_code)
        
        return jsonify({
            'stock_code': stock_code,
            'financial_health': financial_data,
            'valuation': valuation_data,
            'industry_analysis': industry_analysis,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/fundamental/ratios/<stock_code>', methods=['GET'])
def get_financial_ratios(stock_code):
    """Get key financial ratios"""
    try:
        from data.fundamental_data import FundamentalDataProvider
        fundamental_provider = FundamentalDataProvider()
        
        ratios = fundamental_provider.get_financial_ratios(stock_code)
        
        return jsonify({
            'stock_code': stock_code,
            'ratios': ratios,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Market sentiment endpoints
@app.route('/api/sentiment/overall', methods=['GET'])
def get_overall_sentiment():
    """Get overall market sentiment"""
    try:
        sentiment_data = sentiment_provider.get_overall_sentiment()
        return jsonify({
            'success': True,
            'data': sentiment_data
        })
    except Exception as e:
        logger.error(f"Error getting overall sentiment: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/sentiment/capital-flow', methods=['GET'])
def get_capital_flow():
    """Get capital flow data"""
    try:
        flow_data = sentiment_provider.get_capital_flow()
        return jsonify({
            'success': True,
            'data': flow_data
        })
    except Exception as e:
        logger.error(f"Error getting capital flow: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Real-time data endpoints
@app.route('/api/realtime/stocks', methods=['POST'])
def get_realtime_stocks():
    """Get real-time data for multiple stocks"""
    try:
        data = request.get_json()
        stock_codes = data.get('codes', [])
        
        if not stock_codes:
            return jsonify({
                'success': False,
                'error': 'No stock codes provided'
            }), 400
        
        realtime_data = stock_data_provider.get_real_time_data(stock_codes)
        return jsonify({
            'success': True,
            'data': realtime_data
        })
    except Exception as e:
        logger.error(f"Error getting real-time data: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/market/overview', methods=['GET'])
def get_market_overview():
    """Get market overview data"""
    try:
        overview_data = stock_data_provider.get_market_overview()
        return jsonify({
            'success': True,
            'data': overview_data
        })
    except Exception as e:
        logger.error(f"Error getting market overview: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stock/fundamentals/<stock_code>', methods=['GET'])
def get_stock_fundamentals(stock_code):
    """Get fundamental data for a stock"""
    try:
        fundamentals = stock_data_provider.get_stock_fundamentals(stock_code)
        return jsonify({
            'success': True,
            'data': fundamentals
        })
    except Exception as e:
        logger.error(f"Error getting fundamentals for {stock_code}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Strategy Building Endpoints
@app.route('/api/strategy/create', methods=['POST'])
def create_strategy():
    """Create a new trading strategy"""
    try:
        data = request.get_json()
        strategy_name = data.get('name')
        strategy_config = data.get('config')
        user_id = data.get('user_id')
        
        from utils.strategy_builder import StrategyBuilder
        strategy_builder = StrategyBuilder()
        
        strategy_id = strategy_builder.create_strategy(
            name=strategy_name,
            config=strategy_config,
            user_id=user_id
        )
        
        return jsonify({
            'success': True,
            'strategy_id': strategy_id,
            'message': 'Strategy created successfully'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/strategy/backtest', methods=['POST'])
def backtest_strategy():
    """Run backtest for a strategy"""
    try:
        data = request.get_json()
        strategy_id = data.get('strategy_id')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        initial_capital = data.get('initial_capital', 100000)
        
        from utils.backtesting_engine import BacktestingEngine
        backtest_engine = BacktestingEngine()
        
        results = backtest_engine.run_backtest(
            strategy_id=strategy_id,
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital
        )
        
        return jsonify({
            'success': True,
            'results': results
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Risk Management Endpoints
@app.route('/api/risk/portfolio-analysis', methods=['POST'])
def analyze_portfolio_risk():
    """Analyze portfolio risk metrics"""
    try:
        data = request.get_json()
        portfolio = data.get('portfolio')  # List of stock codes and weights
        
        from utils.risk_manager import RiskManager
        risk_manager = RiskManager()
        
        risk_metrics = risk_manager.calculate_portfolio_risk(portfolio)
        
        return jsonify({
            'success': True,
            'risk_metrics': risk_metrics
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/risk/var-calculation', methods=['POST'])
def calculate_var():
    """Calculate Value at Risk (VaR)"""
    try:
        data = request.get_json()
        portfolio = data.get('portfolio')
        confidence_level = data.get('confidence_level', 0.95)
        time_horizon = data.get('time_horizon', 1)
        
        from utils.risk_manager import RiskManager
        risk_manager = RiskManager()
        
        var_result = risk_manager.calculate_var(
            portfolio=portfolio,
            confidence_level=confidence_level,
            time_horizon=time_horizon
        )
        
        return jsonify({
            'success': True,
            'var_result': var_result
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    # Initialize database
    db_manager.init_database()
    
    # Run the app
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000)),
        debug=os.environ.get('FLASK_ENV') == 'development'
    )