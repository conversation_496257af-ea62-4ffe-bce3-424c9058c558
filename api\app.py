from flask import Flask, jsonify, request
from flask_cors import CORS
import logging
import os
from datetime import datetime, timedelta

# Import our modules
from data.stock_data import StockDataProvider
from indicators.magic_nine_turns import MagicNineTurns
from indicators.macd import MACD
from utils.signal_generator import SignalGenerator
from database.db_manager import DatabaseManager

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize components
try:
    db_manager = DatabaseManager()
    stock_data_provider = StockDataProvider()
    magic_nine = MagicNineTurns()
    macd = MACD()
    signal_generator = SignalGenerator()
    logger.info("All components initialized successfully")
except Exception as e:
    logger.error(f"Error initializing components: {e}")
    db_manager = None
    stock_data_provider = None
    magic_nine = None
    macd = None
    signal_generator = None

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/api/stock/info/<stock_code>', methods=['GET'])
def get_stock_info(stock_code):
    """Get stock information and technical analysis"""
    try:
        # Get query parameters
        period = request.args.get('period', 'daily')
        days = int(request.args.get('days', 100))
        
        # Fetch stock data
        stock_data = stock_data_provider.get_stock_data(stock_code, period, days)
        if not stock_data:
            return jsonify({'error': 'Stock data not found'}), 404
        
        # Calculate technical indicators
        magic_nine_data = magic_nine.calculate(stock_data)
        macd_data = macd_calculator.calculate(stock_data)
        
        # Generate signals
        signals = signal_generator.generate_signals(magic_nine_data, macd_data, stock_data)
        
        # Get stock basic info
        stock_info = stock_data_provider.get_stock_basic_info(stock_code)
        
        return jsonify({
            'code': stock_code,
            'name': stock_info.get('name', ''),
            'price_data': stock_data,
            'magic_nine': magic_nine_data,
            'macd_data': macd_data,
            'signals': signals,
            'last_updated': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analysis/calculate', methods=['POST'])
def calculate_analysis():
    """Calculate technical analysis for given parameters"""
    try:
        data = request.get_json()
        stock_code = data.get('stock_code')
        indicators = data.get('indicators', ['magic_nine', 'macd'])
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        if not stock_code:
            return jsonify({'error': 'Stock code is required'}), 400
        
        # Fetch stock data
        stock_data = stock_data_provider.get_stock_data(
            stock_code, 
            start_date=start_date, 
            end_date=end_date
        )
        
        if not stock_data:
            return jsonify({'error': 'Stock data not found'}), 404
        
        result = {'success': True, 'data': {}, 'signals': []}
        
        # Calculate requested indicators
        if 'magic_nine' in indicators:
            result['data']['magic_nine'] = magic_nine.calculate(stock_data)
        
        if 'macd' in indicators:
            result['data']['macd'] = macd_calculator.calculate(stock_data)
        
        # Generate combined signals if both indicators are calculated
        if 'magic_nine' in result['data'] and 'macd' in result['data']:
            result['signals'] = signal_generator.generate_signals(
                result['data']['magic_nine'],
                result['data']['macd'],
                stock_data
            )
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/screening/filter', methods=['POST'])
def filter_stocks():
    """Filter stocks based on technical analysis criteria"""
    try:
        data = request.get_json()
        magic_nine_condition = data.get('magic_nine_condition', {})
        macd_condition = data.get('macd_condition', {})
        market_cap_range = data.get('market_cap_range', {})
        industry_filter = data.get('industry_filter', [])
        
        # Get list of stocks to screen
        stock_list = stock_data_provider.get_stock_list(
            market_cap_range=market_cap_range,
            industry_filter=industry_filter
        )
        
        filtered_stocks = []
        start_time = datetime.now()
        
        for stock_code in stock_list:
            try:
                # Get stock data
                stock_data = stock_data_provider.get_stock_data(stock_code, days=60)
                if not stock_data:
                    continue
                
                # Calculate indicators
                magic_nine_data = magic_nine.calculate(stock_data)
                macd_data = macd_calculator.calculate(stock_data)
                
                # Check if stock meets criteria
                if signal_generator.meets_criteria(
                    magic_nine_data, 
                    macd_data, 
                    magic_nine_condition, 
                    macd_condition
                ):
                    # Generate signals for this stock
                    signals = signal_generator.generate_signals(
                        magic_nine_data, macd_data, stock_data
                    )
                    
                    stock_info = stock_data_provider.get_stock_basic_info(stock_code)
                    filtered_stocks.append({
                        'code': stock_code,
                        'name': stock_info.get('name', ''),
                        'price': stock_data[-1]['close'] if stock_data else 0,
                        'change_pct': stock_info.get('change_pct', 0),
                        'signals': signals,
                        'signal_strength': max([s.get('strength', 0) for s in signals], default=0)
                    })
                    
            except Exception as e:
                print(f"Error processing {stock_code}: {e}")
                continue
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return jsonify({
            'total_count': len(filtered_stocks),
            'stocks': filtered_stocks,
            'execution_time': execution_time
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stocks/list', methods=['GET'])
def get_stock_list():
    """Get list of available stocks"""
    try:
        market = request.args.get('market', 'all')  # 'SZ', 'SH', or 'all'
        limit = int(request.args.get('limit', 100))
        
        stocks = stock_data_provider.get_popular_stocks(market=market, limit=limit)
        return jsonify({
            'stocks': stocks,
            'count': len(stocks)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Initialize database
    db_manager.init_database()
    
    # Run the app
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000)),
        debug=os.environ.get('FLASK_ENV') == 'development'
    )