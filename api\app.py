from flask import Flask, jsonify, request
from flask_cors import CORS
import logging
import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import configuration and modules
from config import load_config, setup_logging
from data.stock_data import StockDataProvider
from indicators.magic_nine_turns import MagicNineTurns
from indicators.macd import MACD
from utils.signal_generator import SignalGenerator
from database.db_manager import DatabaseManager

class TradingAPIServer:
    """Centralized Trading API Server with proper error handling and component management"""

    def __init__(self):
        # Load and validate configuration
        self.config = load_config()

        # Setup logging
        setup_logging(self.config.logging)
        self.logger = logging.getLogger(__name__)

        # Initialize Flask app
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = self.config.api.secret_key

        # Setup CORS
        CORS(self.app, origins=self.config.api.cors_origins)

        # Initialize components with proper error handling
        self.components = self._initialize_components()

        # Register routes
        self._register_routes()

        # Register error handlers
        self._register_error_handlers()

    def _initialize_components(self) -> Dict[str, Any]:
        """Initialize all trading components with proper error handling"""
        components = {}

        try:
            components['db_manager'] = DatabaseManager(self.config.database)
            self.logger.info("Database manager initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize database manager: {e}")
            raise RuntimeError("Database initialization failed") from e

        try:
            components['stock_data_provider'] = StockDataProvider(self.config.data_provider)
            self.logger.info("Stock data provider initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize stock data provider: {e}")
            raise RuntimeError("Stock data provider initialization failed") from e

        try:
            components['magic_nine'] = MagicNineTurns()
            components['macd'] = MACD()
            components['signal_generator'] = SignalGenerator()
            self.logger.info("Technical indicators initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize technical indicators: {e}")
            raise RuntimeError("Technical indicators initialization failed") from e

        self.logger.info("All components initialized successfully")
        return components

    def _register_error_handlers(self):
        """Register global error handlers"""

        @self.app.errorhandler(404)
        def not_found(error):
            return jsonify({
                'success': False,
                'error': 'API endpoint not found',
                'message': 'The requested resource does not exist'
            }), 404

        @self.app.errorhandler(500)
        def internal_error(error):
            self.logger.error(f"Internal server error: {error}")
            return jsonify({
                'success': False,
                'error': 'Internal server error',
                'message': 'An unexpected error occurred'
            }), 500

        @self.app.errorhandler(Exception)
        def handle_exception(error):
            self.logger.error(f"Unhandled exception: {error}")
            return jsonify({
                'success': False,
                'error': 'Unexpected error',
                'message': str(error)
            }), 500

    def _register_routes(self):
        """Register all API routes"""
        # Health check
        @self.app.route('/api/health', methods=['GET'])
        def health_check():
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'version': '2.0.0',
                'components': {
                    'database': 'connected' if self.components.get('db_manager') else 'disconnected',
                    'data_provider': 'ready' if self.components.get('stock_data_provider') else 'not_ready'
                }
            })

# Create server instance
trading_server = TradingAPIServer()
app = trading_server.app

# Get component references for backward compatibility
db_manager = trading_server.components.get('db_manager')
stock_data_provider = trading_server.components.get('stock_data_provider')
magic_nine = trading_server.components.get('magic_nine')
macd = trading_server.components.get('macd')
signal_generator = trading_server.components.get('signal_generator')

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/api/stock/info/<stock_code>', methods=['GET'])
def get_stock_info(stock_code):
    """Get stock information and technical analysis"""
    if not stock_data_provider:
        return jsonify({
            'success': False,
            'error': 'Service unavailable',
            'message': 'Stock data provider not initialized'
        }), 503

    try:
        # Validate stock code
        if not stock_code or len(stock_code.strip()) == 0:
            return jsonify({
                'success': False,
                'error': 'Invalid stock code',
                'message': 'Stock code cannot be empty'
            }), 400

        # Get query parameters with validation
        period = request.args.get('period', 'daily')
        try:
            days = int(request.args.get('days', 100))
            if days <= 0 or days > 1000:
                days = 100
        except ValueError:
            days = 100

        # Fetch stock data
        stock_data = stock_data_provider.get_stock_data(stock_code, period, days)
        if not stock_data:
            return jsonify({
                'success': False,
                'error': 'Stock not found',
                'message': f'No data available for stock code: {stock_code}'
            }), 404

        # Calculate technical indicators
        magic_nine_data = magic_nine.calculate(stock_data)
        macd_data = macd.calculate(stock_data)  # Fixed: was macd_calculator

        # Generate signals
        signals = signal_generator.generate_signals(magic_nine_data, macd_data, stock_data)

        # Get stock basic info
        stock_info = stock_data_provider.get_stock_basic_info(stock_code)

        return jsonify({
            'success': True,
            'data': {
                'code': stock_code,
                'name': stock_info.get('name', ''),
                'price_data': stock_data,
                'magic_nine': magic_nine_data,
                'macd_data': macd_data,
                'signals': signals,
                'last_updated': datetime.now().isoformat()
            }
        })

    except Exception as e:
        trading_server.logger.error(f"Error getting stock info for {stock_code}: {e}")
        return jsonify({
            'success': False,
            'error': 'Processing error',
            'message': 'Failed to retrieve stock information'
        }), 500

@app.route('/api/analysis/calculate', methods=['POST'])
def calculate_analysis():
    """Calculate technical analysis for given parameters"""
    if not stock_data_provider:
        return jsonify({
            'success': False,
            'error': 'Service unavailable',
            'message': 'Stock data provider not initialized'
        }), 503

    try:
        # Validate request data
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'Invalid request',
                'message': 'Request body must be valid JSON'
            }), 400

        # Import validation utilities
        from utils.validation import validate_request_data, ValidationError

        # Define validation schema
        schema = {
            'stock_code': {'type': 'stock_code', 'required': True},
            'indicators': {'type': 'list', 'item_type': str, 'required': False},
            'start_date': {'type': 'date', 'required': False},
            'end_date': {'type': 'date', 'required': False}
        }

        # Validate input data
        validated_data = validate_request_data(data, schema)

        stock_code = validated_data['stock_code']
        indicators = validated_data.get('indicators', ['magic_nine', 'macd'])
        start_date = validated_data.get('start_date')
        end_date = validated_data.get('end_date')

        # Fetch stock data
        stock_data = stock_data_provider.get_stock_data(
            stock_code,
            start_date=start_date,
            end_date=end_date
        )

        if not stock_data:
            return jsonify({
                'success': False,
                'error': 'Stock not found',
                'message': f'No data available for stock code: {stock_code}'
            }), 404

        result = {'success': True, 'data': {}, 'signals': []}

        # Calculate requested indicators
        if 'magic_nine' in indicators:
            result['data']['magic_nine'] = magic_nine.calculate(stock_data)

        if 'macd' in indicators:
            result['data']['macd'] = macd.calculate(stock_data)  # Fixed: was macd_calculator

        # Generate combined signals if both indicators are calculated
        if 'magic_nine' in result['data'] and 'macd' in result['data']:
            result['signals'] = signal_generator.generate_signals(
                result['data']['magic_nine'],
                result['data']['macd'],
                stock_data
            )

        return jsonify(result)

    except ValidationError as e:
        trading_server.logger.warning(f"Validation error in calculate_analysis: {e}")
        return jsonify({
            'success': False,
            'error': 'Validation error',
            'message': str(e)
        }), 400
    except Exception as e:
        trading_server.logger.error(f"Error in calculate_analysis: {e}")
        return jsonify({
            'success': False,
            'error': 'Processing error',
            'message': 'Failed to calculate analysis'
        }), 500

@app.route('/api/screening/filter', methods=['POST'])
def filter_stocks():
    """Filter stocks based on technical analysis criteria"""
    try:
        data = request.get_json()
        magic_nine_condition = data.get('magic_nine_condition', {})
        macd_condition = data.get('macd_condition', {})
        market_cap_range = data.get('market_cap_range', {})
        industry_filter = data.get('industry_filter', [])
        
        # Get list of stocks to screen
        stock_list = stock_data_provider.get_stock_list(
            market_cap_range=market_cap_range,
            industry_filter=industry_filter
        )
        
        filtered_stocks = []
        start_time = datetime.now()
        
        for stock_code in stock_list:
            try:
                # Get stock data
                stock_data = stock_data_provider.get_stock_data(stock_code, days=60)
                if not stock_data:
                    continue
                
                # Calculate indicators
                magic_nine_data = magic_nine.calculate(stock_data)
                macd_data = macd.calculate(stock_data)  # Fixed: was macd_calculator
                
                # Check if stock meets criteria
                if signal_generator.meets_criteria(
                    magic_nine_data, 
                    macd_data, 
                    magic_nine_condition, 
                    macd_condition
                ):
                    # Generate signals for this stock
                    signals = signal_generator.generate_signals(
                        magic_nine_data, macd_data, stock_data
                    )
                    
                    stock_info = stock_data_provider.get_stock_basic_info(stock_code)
                    filtered_stocks.append({
                        'code': stock_code,
                        'name': stock_info.get('name', ''),
                        'price': stock_data[-1]['close'] if stock_data else 0,
                        'change_pct': stock_info.get('change_pct', 0),
                        'signals': signals,
                        'signal_strength': max([s.get('strength', 0) for s in signals], default=0)
                    })
                    
            except Exception as e:
                print(f"Error processing {stock_code}: {e}")
                continue
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return jsonify({
            'total_count': len(filtered_stocks),
            'stocks': filtered_stocks,
            'execution_time': execution_time
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stocks/list', methods=['GET'])
def get_stock_list():
    """Get list of available stocks"""
    try:
        market = request.args.get('market', 'all')  # 'SZ', 'SH', or 'all'
        limit = int(request.args.get('limit', 100))
        
        stocks = stock_data_provider.get_popular_stocks(market=market, limit=limit)
        return jsonify({
            'stocks': stocks,
            'count': len(stocks)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Technical Analysis Endpoints
@app.route('/api/technical/indicators/<stock_code>', methods=['GET'])
def get_technical_indicators(stock_code):
    """Get comprehensive technical indicators for a stock"""
    try:
        indicators = request.args.getlist('indicators')  # KDJ, RSI, BOLL, etc.
        period = request.args.get('period', '1d')
        days = int(request.args.get('days', 100))
        
        stock_data = stock_data_provider.get_stock_data(stock_code, period, days)
        if not stock_data:
            return jsonify({'error': 'Stock data not found'}), 404
        
        result = {'stock_code': stock_code, 'indicators': {}}
        
        # Calculate requested indicators
        if 'KDJ' in indicators or not indicators:
            from indicators.kdj import KDJ
            kdj = KDJ()
            result['indicators']['kdj'] = kdj.calculate(stock_data)
        
        if 'RSI' in indicators or not indicators:
            from indicators.rsi import RSI
            rsi = RSI()
            result['indicators']['rsi'] = rsi.calculate(stock_data)
        
        if 'BOLL' in indicators or not indicators:
            from indicators.bollinger_bands import BollingerBands
            boll = BollingerBands()
            result['indicators']['bollinger'] = boll.calculate(stock_data)
        
        if 'MACD' in indicators or not indicators:
            result['indicators']['macd'] = macd.calculate(stock_data)
        
        if 'MAGIC_NINE' in indicators or not indicators:
            result['indicators']['magic_nine'] = magic_nine.calculate(stock_data)
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/technical/signals/<stock_code>', methods=['GET'])
def get_trading_signals(stock_code):
    """Get trading signals based on technical analysis"""
    try:
        timeframe = request.args.get('timeframe', '1d')
        
        stock_data = stock_data_provider.get_stock_data(stock_code, timeframe, 100)
        if not stock_data:
            return jsonify({'error': 'Stock data not found'}), 404
        
        # Calculate all indicators
        magic_nine_data = magic_nine.calculate(stock_data)
        macd_data = macd.calculate(stock_data)
        
        # Generate comprehensive signals
        signals = signal_generator.generate_comprehensive_signals(
            stock_data, magic_nine_data, macd_data
        )
        
        return jsonify({
            'stock_code': stock_code,
            'signals': signals,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Fundamental Analysis Endpoints
@app.route('/api/fundamental/analysis/<stock_code>', methods=['GET'])
def get_fundamental_analysis(stock_code):
    """Get fundamental analysis data"""
    try:
        from data.fundamental_data import FundamentalDataProvider
        fundamental_provider = FundamentalDataProvider()
        
        # Get financial data
        financial_data = fundamental_provider.get_financial_data(stock_code)
        valuation_data = fundamental_provider.get_valuation_metrics(stock_code)
        industry_analysis = fundamental_provider.get_industry_analysis(stock_code)
        
        return jsonify({
            'stock_code': stock_code,
            'financial_health': financial_data,
            'valuation': valuation_data,
            'industry_analysis': industry_analysis,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/fundamental/ratios/<stock_code>', methods=['GET'])
def get_financial_ratios(stock_code):
    """Get key financial ratios"""
    try:
        from data.fundamental_data import FundamentalDataProvider
        fundamental_provider = FundamentalDataProvider()
        
        ratios = fundamental_provider.get_financial_ratios(stock_code)
        
        return jsonify({
            'stock_code': stock_code,
            'ratios': ratios,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Market sentiment endpoints
@app.route('/api/sentiment/overall', methods=['GET'])
def get_overall_sentiment():
    """Get overall market sentiment"""
    try:
        sentiment_data = sentiment_provider.get_overall_sentiment()
        return jsonify({
            'success': True,
            'data': sentiment_data
        })
    except Exception as e:
        logger.error(f"Error getting overall sentiment: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/sentiment/capital-flow', methods=['GET'])
def get_capital_flow():
    """Get capital flow data"""
    try:
        flow_data = sentiment_provider.get_capital_flow()
        return jsonify({
            'success': True,
            'data': flow_data
        })
    except Exception as e:
        logger.error(f"Error getting capital flow: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Real-time data endpoints
@app.route('/api/realtime/stocks', methods=['POST'])
def get_realtime_stocks():
    """Get real-time data for multiple stocks"""
    try:
        data = request.get_json()
        stock_codes = data.get('codes', [])
        
        if not stock_codes:
            return jsonify({
                'success': False,
                'error': 'No stock codes provided'
            }), 400
        
        realtime_data = stock_data_provider.get_real_time_data(stock_codes)
        return jsonify({
            'success': True,
            'data': realtime_data
        })
    except Exception as e:
        logger.error(f"Error getting real-time data: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/market/overview', methods=['GET'])
def get_market_overview():
    """Get market overview data"""
    try:
        overview_data = stock_data_provider.get_market_overview()
        return jsonify({
            'success': True,
            'data': overview_data
        })
    except Exception as e:
        logger.error(f"Error getting market overview: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stock/fundamentals/<stock_code>', methods=['GET'])
def get_stock_fundamentals(stock_code):
    """Get fundamental data for a stock"""
    try:
        fundamentals = stock_data_provider.get_stock_fundamentals(stock_code)
        return jsonify({
            'success': True,
            'data': fundamentals
        })
    except Exception as e:
        logger.error(f"Error getting fundamentals for {stock_code}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Strategy Building Endpoints
@app.route('/api/strategy/create', methods=['POST'])
def create_strategy():
    """Create a new trading strategy"""
    try:
        data = request.get_json()
        strategy_name = data.get('name')
        strategy_config = data.get('config')
        user_id = data.get('user_id')
        
        from utils.strategy_builder import StrategyBuilder
        strategy_builder = StrategyBuilder()
        
        strategy_id = strategy_builder.create_strategy(
            name=strategy_name,
            config=strategy_config,
            user_id=user_id
        )
        
        return jsonify({
            'success': True,
            'strategy_id': strategy_id,
            'message': 'Strategy created successfully'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/strategy/backtest', methods=['POST'])
def backtest_strategy():
    """Run backtest for a strategy"""
    try:
        data = request.get_json()
        strategy_id = data.get('strategy_id')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        initial_capital = data.get('initial_capital', 100000)
        
        from utils.backtesting_engine import BacktestingEngine
        backtest_engine = BacktestingEngine()
        
        results = backtest_engine.run_backtest(
            strategy_id=strategy_id,
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital
        )
        
        return jsonify({
            'success': True,
            'results': results
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Risk Management Endpoints
@app.route('/api/risk/portfolio-analysis', methods=['POST'])
def analyze_portfolio_risk():
    """Analyze portfolio risk metrics"""
    try:
        data = request.get_json()
        portfolio = data.get('portfolio')  # List of stock codes and weights
        
        from utils.risk_manager import RiskManager
        risk_manager = RiskManager()
        
        risk_metrics = risk_manager.calculate_portfolio_risk(portfolio)
        
        return jsonify({
            'success': True,
            'risk_metrics': risk_metrics
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/risk/var-calculation', methods=['POST'])
def calculate_var():
    """Calculate Value at Risk (VaR)"""
    try:
        data = request.get_json()
        portfolio = data.get('portfolio')
        confidence_level = data.get('confidence_level', 0.95)
        time_horizon = data.get('time_horizon', 1)
        
        from utils.risk_manager import RiskManager
        risk_manager = RiskManager()
        
        var_result = risk_manager.calculate_var(
            portfolio=portfolio,
            confidence_level=confidence_level,
            time_horizon=time_horizon
        )
        
        return jsonify({
            'success': True,
            'var_result': var_result
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

def create_app():
    """Application factory function"""
    return trading_server.app

if __name__ == '__main__':
    try:
        # Get configuration
        config = trading_server.config

        # Run the app
        trading_server.logger.info(f"Starting Trading Agent API Server on {config.api.host}:{config.api.port}")
        trading_server.logger.info(f"Environment: {config.environment}")
        trading_server.logger.info(f"Debug mode: {config.api.debug}")

        app.run(
            host=config.api.host,
            port=config.api.port,
            debug=config.api.debug
        )
    except Exception as e:
        logging.error(f"Failed to start server: {e}")
        raise