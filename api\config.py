"""
Configuration management for Trading Agent API
Handles environment variables, security settings, and application configuration
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
import logging

@dataclass
class DatabaseConfig:
    """Database configuration settings"""
    host: str
    port: int
    database: str
    user: str
    password: str
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    
    @classmethod
    def from_env(cls) -> 'DatabaseConfig':
        """Create database config from environment variables"""
        return cls(
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', '5432')),
            database=os.getenv('DB_NAME', 'trading_db'),
            user=os.getenv('DB_USER', 'postgres'),
            password=os.getenv('DB_PASSWORD', ''),
            pool_size=int(os.getenv('DB_POOL_SIZE', '10')),
            max_overflow=int(os.getenv('DB_MAX_OVERFLOW', '20')),
            pool_timeout=int(os.getenv('DB_POOL_TIMEOUT', '30'))
        )
    
    def validate(self) -> bool:
        """Validate database configuration"""
        if not self.password:
            raise ValueError("Database password is required")
        if not self.host:
            raise ValueError("Database host is required")
        if not self.database:
            raise ValueError("Database name is required")
        return True

@dataclass
class APIConfig:
    """API configuration settings"""
    host: str = '0.0.0.0'
    port: int = 5000
    debug: bool = False
    secret_key: str = ''
    cors_origins: list = None
    rate_limit: str = '100 per hour'
    
    @classmethod
    def from_env(cls) -> 'APIConfig':
        """Create API config from environment variables"""
        cors_origins = os.getenv('CORS_ORIGINS', '*').split(',')
        return cls(
            host=os.getenv('API_HOST', '0.0.0.0'),
            port=int(os.getenv('API_PORT', '5000')),
            debug=os.getenv('FLASK_ENV') == 'development',
            secret_key=os.getenv('SECRET_KEY', ''),
            cors_origins=cors_origins,
            rate_limit=os.getenv('RATE_LIMIT', '100 per hour')
        )
    
    def validate(self) -> bool:
        """Validate API configuration"""
        if not self.secret_key:
            raise ValueError("SECRET_KEY is required for production")
        return True

@dataclass
class DataProviderConfig:
    """Data provider configuration"""
    akshare_timeout: int = 30
    cache_ttl: int = 300  # 5 minutes
    max_retries: int = 3
    retry_delay: int = 1
    
    @classmethod
    def from_env(cls) -> 'DataProviderConfig':
        """Create data provider config from environment variables"""
        return cls(
            akshare_timeout=int(os.getenv('AKSHARE_TIMEOUT', '30')),
            cache_ttl=int(os.getenv('CACHE_TTL', '300')),
            max_retries=int(os.getenv('MAX_RETRIES', '3')),
            retry_delay=int(os.getenv('RETRY_DELAY', '1'))
        )

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = 'INFO'
    format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    file_path: Optional[str] = None
    max_bytes: int = 10485760  # 10MB
    backup_count: int = 5
    
    @classmethod
    def from_env(cls) -> 'LoggingConfig':
        """Create logging config from environment variables"""
        return cls(
            level=os.getenv('LOG_LEVEL', 'INFO'),
            format=os.getenv('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            file_path=os.getenv('LOG_FILE'),
            max_bytes=int(os.getenv('LOG_MAX_BYTES', '10485760')),
            backup_count=int(os.getenv('LOG_BACKUP_COUNT', '5'))
        )

class Config:
    """Main configuration class"""
    
    def __init__(self):
        self.database = DatabaseConfig.from_env()
        self.api = APIConfig.from_env()
        self.data_provider = DataProviderConfig.from_env()
        self.logging = LoggingConfig.from_env()
        self.environment = os.getenv('ENVIRONMENT', 'development')
        
    def validate(self) -> bool:
        """Validate all configuration settings"""
        try:
            self.database.validate()
            if self.environment == 'production':
                self.api.validate()
            return True
        except ValueError as e:
            logging.error(f"Configuration validation failed: {e}")
            return False
    
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.environment.lower() == 'production'
    
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.environment.lower() == 'development'
    
    def get_database_url(self) -> str:
        """Get database connection URL"""
        return (f"postgresql://{self.database.user}:{self.database.password}"
                f"@{self.database.host}:{self.database.port}/{self.database.database}")

# Global configuration instance
config = Config()

def load_config() -> Config:
    """Load and validate configuration"""
    if not config.validate():
        raise RuntimeError("Configuration validation failed")
    return config

def setup_logging(config: LoggingConfig):
    """Setup logging configuration"""
    import logging.handlers
    
    # Set logging level
    level = getattr(logging, config.level.upper(), logging.INFO)
    
    # Create formatter
    formatter = logging.Formatter(config.format)
    
    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler (if specified)
    if config.file_path:
        file_handler = logging.handlers.RotatingFileHandler(
            config.file_path,
            maxBytes=config.max_bytes,
            backupCount=config.backup_count
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
