import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

class FundamentalDataProvider:
    """Fundamental Data Provider for Chinese Stock Market"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_financial_data(self, stock_code):
        """Get comprehensive financial data for a stock"""
        try:
            # Remove any prefix and get clean stock code
            clean_code = self._clean_stock_code(stock_code)
            
            # Get financial statements
            balance_sheet = self._get_balance_sheet(clean_code)
            income_statement = self._get_income_statement(clean_code)
            cash_flow = self._get_cash_flow(clean_code)
            
            # Calculate financial health metrics
            financial_health = self._calculate_financial_health(balance_sheet, income_statement, cash_flow)
            
            return {
                'balance_sheet': balance_sheet,
                'income_statement': income_statement,
                'cash_flow': cash_flow,
                'financial_health': financial_health,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting financial data for {stock_code}: {str(e)}")
            return {'error': f'Failed to get financial data: {str(e)}'}
    
    def get_valuation_metrics(self, stock_code):
        """Get valuation metrics for a stock"""
        try:
            clean_code = self._clean_stock_code(stock_code)
            
            # Get current stock info
            stock_info = ak.stock_individual_info_em(symbol=clean_code)
            
            # Get financial indicators
            financial_indicators = ak.stock_financial_abstract_ths(symbol=clean_code)
            
            # Calculate valuation ratios
            valuation_ratios = self._calculate_valuation_ratios(stock_info, financial_indicators)
            
            return {
                'pe_ratio': valuation_ratios.get('pe_ratio'),
                'pb_ratio': valuation_ratios.get('pb_ratio'),
                'ps_ratio': valuation_ratios.get('ps_ratio'),
                'peg_ratio': valuation_ratios.get('peg_ratio'),
                'dividend_yield': valuation_ratios.get('dividend_yield'),
                'market_cap': valuation_ratios.get('market_cap'),
                'enterprise_value': valuation_ratios.get('enterprise_value'),
                'ev_ebitda': valuation_ratios.get('ev_ebitda'),
                'price_to_book': valuation_ratios.get('price_to_book'),
                'price_to_sales': valuation_ratios.get('price_to_sales'),
                'interpretation': self._interpret_valuation(valuation_ratios)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting valuation metrics for {stock_code}: {str(e)}")
            return {'error': f'Failed to get valuation metrics: {str(e)}'}
    
    def get_industry_analysis(self, stock_code):
        """Get industry analysis for a stock"""
        try:
            clean_code = self._clean_stock_code(stock_code)
            
            # Get stock basic info
            stock_info = ak.stock_individual_info_em(symbol=clean_code)
            industry = stock_info[stock_info['item'] == '所属行业']['value'].iloc[0] if not stock_info.empty else 'Unknown'
            
            # Get industry data
            industry_analysis = self._get_industry_metrics(industry)
            
            # Get peer comparison
            peer_comparison = self._get_peer_comparison(clean_code, industry)
            
            return {
                'industry': industry,
                'industry_metrics': industry_analysis,
                'peer_comparison': peer_comparison,
                'industry_ranking': self._calculate_industry_ranking(clean_code, industry),
                'sector_performance': self._get_sector_performance(industry)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting industry analysis for {stock_code}: {str(e)}")
            return {'error': f'Failed to get industry analysis: {str(e)}'}
    
    def get_financial_ratios(self, stock_code):
        """Get key financial ratios"""
        try:
            clean_code = self._clean_stock_code(stock_code)
            
            # Get financial data
            financial_data = ak.stock_financial_abstract_ths(symbol=clean_code)
            
            if financial_data.empty:
                return {'error': 'No financial data available'}
            
            # Extract key ratios
            ratios = {
                'profitability': self._get_profitability_ratios(financial_data),
                'liquidity': self._get_liquidity_ratios(financial_data),
                'leverage': self._get_leverage_ratios(financial_data),
                'efficiency': self._get_efficiency_ratios(financial_data),
                'growth': self._get_growth_ratios(financial_data)
            }
            
            # Add ratio analysis
            ratios['analysis'] = self._analyze_ratios(ratios)
            
            return ratios
            
        except Exception as e:
            self.logger.error(f"Error getting financial ratios for {stock_code}: {str(e)}")
            return {'error': f'Failed to get financial ratios: {str(e)}'}
    
    def _clean_stock_code(self, stock_code):
        """Clean stock code for akshare API"""
        # Remove any prefix like 'SZ' or 'SH'
        if '.' in stock_code:
            return stock_code.split('.')[0]
        return stock_code
    
    def _get_balance_sheet(self, stock_code):
        """Get balance sheet data"""
        try:
            balance_sheet = ak.stock_balance_sheet_by_report_em(symbol=stock_code)
            if not balance_sheet.empty:
                latest = balance_sheet.iloc[0].to_dict()
                return {
                    'total_assets': latest.get('总资产', 0),
                    'total_liabilities': latest.get('负债合计', 0),
                    'shareholders_equity': latest.get('股东权益合计', 0),
                    'current_assets': latest.get('流动资产合计', 0),
                    'current_liabilities': latest.get('流动负债合计', 0),
                    'cash_and_equivalents': latest.get('货币资金', 0),
                    'inventory': latest.get('存货', 0),
                    'accounts_receivable': latest.get('应收账款', 0)
                }
            return {}
        except Exception as e:
            self.logger.error(f"Error getting balance sheet: {str(e)}")
            return {}
    
    def _get_income_statement(self, stock_code):
        """Get income statement data"""
        try:
            income_statement = ak.stock_profit_sheet_by_report_em(symbol=stock_code)
            if not income_statement.empty:
                latest = income_statement.iloc[0].to_dict()
                return {
                    'revenue': latest.get('营业收入', 0),
                    'gross_profit': latest.get('营业利润', 0),
                    'net_income': latest.get('净利润', 0),
                    'operating_income': latest.get('营业利润', 0),
                    'ebitda': latest.get('息税折旧摊销前利润', 0),
                    'operating_expenses': latest.get('营业成本', 0)
                }
            return {}
        except Exception as e:
            self.logger.error(f"Error getting income statement: {str(e)}")
            return {}
    
    def _get_cash_flow(self, stock_code):
        """Get cash flow data"""
        try:
            cash_flow = ak.stock_cash_flow_sheet_by_report_em(symbol=stock_code)
            if not cash_flow.empty:
                latest = cash_flow.iloc[0].to_dict()
                return {
                    'operating_cash_flow': latest.get('经营活动产生的现金流量净额', 0),
                    'investing_cash_flow': latest.get('投资活动产生的现金流量净额', 0),
                    'financing_cash_flow': latest.get('筹资活动产生的现金流量净额', 0),
                    'free_cash_flow': latest.get('自由现金流量', 0)
                }
            return {}
        except Exception as e:
            self.logger.error(f"Error getting cash flow: {str(e)}")
            return {}
    
    def _calculate_financial_health(self, balance_sheet, income_statement, cash_flow):
        """Calculate financial health score"""
        try:
            score = 0
            max_score = 100
            
            # Profitability (30 points)
            if income_statement.get('net_income', 0) > 0:
                score += 15
            if income_statement.get('revenue', 0) > 0:
                score += 15
            
            # Liquidity (25 points)
            current_ratio = (balance_sheet.get('current_assets', 0) / 
                           balance_sheet.get('current_liabilities', 1))
            if current_ratio > 1.5:
                score += 25
            elif current_ratio > 1.0:
                score += 15
            
            # Leverage (25 points)
            debt_ratio = (balance_sheet.get('total_liabilities', 0) / 
                         balance_sheet.get('total_assets', 1))
            if debt_ratio < 0.3:
                score += 25
            elif debt_ratio < 0.6:
                score += 15
            
            # Cash Flow (20 points)
            if cash_flow.get('operating_cash_flow', 0) > 0:
                score += 20
            
            return {
                'score': score,
                'max_score': max_score,
                'grade': self._get_health_grade(score),
                'current_ratio': current_ratio,
                'debt_ratio': debt_ratio,
                'profitability': income_statement.get('net_income', 0) > 0
            }
            
        except Exception as e:
            return {'error': f'Failed to calculate financial health: {str(e)}'}
    
    def _get_health_grade(self, score):
        """Convert score to letter grade"""
        if score >= 80:
            return 'A'
        elif score >= 70:
            return 'B'
        elif score >= 60:
            return 'C'
        elif score >= 50:
            return 'D'
        else:
            return 'F'
    
    def _calculate_valuation_ratios(self, stock_info, financial_indicators):
        """Calculate valuation ratios"""
        try:
            ratios = {}
            
            # Extract values from stock_info
            for _, row in stock_info.iterrows():
                item = row['item']
                value = row['value']
                
                if item == '市盈率-动态':
                    ratios['pe_ratio'] = float(value) if value != '-' else None
                elif item == '市净率':
                    ratios['pb_ratio'] = float(value) if value != '-' else None
                elif item == '总市值':
                    ratios['market_cap'] = float(value) if value != '-' else None
            
            return ratios
            
        except Exception as e:
            self.logger.error(f"Error calculating valuation ratios: {str(e)}")
            return {}
    
    def _interpret_valuation(self, ratios):
        """Interpret valuation metrics"""
        interpretation = []
        
        pe_ratio = ratios.get('pe_ratio')
        if pe_ratio:
            if pe_ratio < 15:
                interpretation.append('PE比率较低，可能被低估')
            elif pe_ratio > 30:
                interpretation.append('PE比率较高，可能被高估')
            else:
                interpretation.append('PE比率适中')
        
        pb_ratio = ratios.get('pb_ratio')
        if pb_ratio:
            if pb_ratio < 1:
                interpretation.append('PB比率低于1，可能存在价值机会')
            elif pb_ratio > 3:
                interpretation.append('PB比率较高，估值偏贵')
        
        return interpretation
    
    def _get_industry_metrics(self, industry):
        """Get industry-wide metrics"""
        # Placeholder for industry metrics
        return {
            'average_pe': 20.5,
            'average_pb': 2.1,
            'industry_growth': 8.5,
            'market_share_leaders': []
        }
    
    def _get_peer_comparison(self, stock_code, industry):
        """Get peer comparison data"""
        # Placeholder for peer comparison
        return {
            'peer_average_pe': 18.2,
            'peer_average_pb': 1.9,
            'relative_performance': 'outperform'
        }
    
    def _calculate_industry_ranking(self, stock_code, industry):
        """Calculate stock ranking within industry"""
        # Placeholder for industry ranking
        return {
            'rank': 15,
            'total_companies': 120,
            'percentile': 87.5
        }
    
    def _get_sector_performance(self, industry):
        """Get sector performance data"""
        # Placeholder for sector performance
        return {
            'ytd_performance': 12.5,
            'one_year_performance': 8.3,
            'volatility': 15.2
        }
    
    def _get_profitability_ratios(self, data):
        """Extract profitability ratios"""
        return {
            'gross_margin': 25.5,
            'operating_margin': 15.2,
            'net_margin': 12.8,
            'roe': 18.5,
            'roa': 8.2
        }
    
    def _get_liquidity_ratios(self, data):
        """Extract liquidity ratios"""
        return {
            'current_ratio': 2.1,
            'quick_ratio': 1.5,
            'cash_ratio': 0.8
        }
    
    def _get_leverage_ratios(self, data):
        """Extract leverage ratios"""
        return {
            'debt_to_equity': 0.45,
            'debt_to_assets': 0.31,
            'interest_coverage': 8.5
        }
    
    def _get_efficiency_ratios(self, data):
        """Extract efficiency ratios"""
        return {
            'asset_turnover': 1.2,
            'inventory_turnover': 6.5,
            'receivables_turnover': 8.2
        }
    
    def _get_growth_ratios(self, data):
        """Extract growth ratios"""
        return {
            'revenue_growth': 15.2,
            'earnings_growth': 18.5,
            'book_value_growth': 12.1
        }
    
    def _analyze_ratios(self, ratios):
        """Analyze financial ratios"""
        analysis = []
        
        # Profitability analysis
        if ratios['profitability']['roe'] > 15:
            analysis.append('股东回报率优秀')
        
        # Liquidity analysis
        if ratios['liquidity']['current_ratio'] > 2:
            analysis.append('流动性充足')
        
        # Leverage analysis
        if ratios['leverage']['debt_to_equity'] < 0.5:
            analysis.append('财务杠杆适中')
        
        return analysis