import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import requests

class MarketSentimentProvider:
    """Market Sentiment Data Provider for Chinese Stock Market"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_overall_sentiment(self):
        """Get overall market sentiment indicators"""
        try:
            sentiment_data = {
                'fear_greed_index': self._calculate_fear_greed_index(),
                'market_breadth': self._get_market_breadth(),
                'volatility_index': self._get_volatility_index(),
                'investor_sentiment': self._get_investor_sentiment(),
                'news_sentiment': self._get_news_sentiment(),
                'social_sentiment': self._get_social_sentiment(),
                'overall_score': 0,
                'interpretation': '',
                'last_updated': datetime.now().isoformat()
            }
            
            # Calculate overall sentiment score
            sentiment_data['overall_score'] = self._calculate_overall_sentiment(sentiment_data)
            sentiment_data['interpretation'] = self._interpret_sentiment(sentiment_data['overall_score'])
            
            return sentiment_data
            
        except Exception as e:
            self.logger.error(f"Error getting overall sentiment: {str(e)}")
            return {'error': f'Failed to get sentiment data: {str(e)}'}
    
    def get_capital_flow(self, period='1d'):
        """Get capital flow data"""
        try:
            # Get market capital flow
            capital_flow = ak.stock_market_fund_flow()
            
            if capital_flow.empty:
                return {'error': 'No capital flow data available'}
            
            latest = capital_flow.iloc[0]
            
            flow_data = {
                'main_net_inflow': float(latest.get('主力净流入-净额', 0)),
                'main_net_inflow_rate': float(latest.get('主力净流入-净占比', 0)),
                'super_large_net_inflow': float(latest.get('超大单净流入-净额', 0)),
                'super_large_net_inflow_rate': float(latest.get('超大单净流入-净占比', 0)),
                'large_net_inflow': float(latest.get('大单净流入-净额', 0)),
                'large_net_inflow_rate': float(latest.get('大单净流入-净占比', 0)),
                'medium_net_inflow': float(latest.get('中单净流入-净额', 0)),
                'medium_net_inflow_rate': float(latest.get('中单净流入-净占比', 0)),
                'small_net_inflow': float(latest.get('小单净流入-净额', 0)),
                'small_net_inflow_rate': float(latest.get('小单净流入-净占比', 0)),
                'interpretation': self._interpret_capital_flow(latest),
                'trend': self._analyze_capital_flow_trend(capital_flow),
                'date': latest.get('日期', datetime.now().strftime('%Y-%m-%d'))
            }
            
            return flow_data
            
        except Exception as e:
            self.logger.error(f"Error getting capital flow: {str(e)}")
            return {'error': f'Failed to get capital flow data: {str(e)}'}
    
    def get_sector_sentiment(self):
        """Get sector-wise sentiment analysis"""
        try:
            # Get sector performance data
            sector_data = ak.stock_board_industry_name_em()
            
            if sector_data.empty:
                return {'error': 'No sector data available'}
            
            # Process sector sentiment
            sectors = []
            for _, row in sector_data.head(20).iterrows():
                sector_info = {
                    'name': row.get('板块名称', ''),
                    'change_percent': float(row.get('涨跌幅', 0)),
                    'turnover_rate': float(row.get('换手率', 0)),
                    'volume_ratio': float(row.get('量比', 0)),
                    'leading_stocks': row.get('领涨股票', ''),
                    'sentiment_score': self._calculate_sector_sentiment(row),
                    'trend': self._get_sector_trend(row.get('涨跌幅', 0))
                }
                sectors.append(sector_info)
            
            # Sort by sentiment score
            sectors.sort(key=lambda x: x['sentiment_score'], reverse=True)
            
            return {
                'sectors': sectors,
                'hot_sectors': sectors[:5],
                'cold_sectors': sectors[-5:],
                'rotation_analysis': self._analyze_sector_rotation(sectors),
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting sector sentiment: {str(e)}")
            return {'error': f'Failed to get sector sentiment: {str(e)}'}
    
    def get_market_indicators(self):
        """Get key market sentiment indicators"""
        try:
            indicators = {
                'advance_decline_ratio': self._get_advance_decline_ratio(),
                'new_highs_lows': self._get_new_highs_lows(),
                'margin_trading': self._get_margin_trading_data(),
                'institutional_activity': self._get_institutional_activity(),
                'market_momentum': self._get_market_momentum(),
                'risk_appetite': self._calculate_risk_appetite()
            }
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Error getting market indicators: {str(e)}")
            return {'error': f'Failed to get market indicators: {str(e)}'}
    
    def _calculate_fear_greed_index(self):
        """Calculate Fear & Greed Index for Chinese market"""
        try:
            # Get market data for calculation
            sh_index = ak.stock_zh_index_daily(symbol="sh000001")
            
            if sh_index.empty:
                return 50  # Neutral
            
            # Calculate based on multiple factors
            recent_data = sh_index.tail(20)
            
            # Price momentum (25%)
            price_change = (recent_data['close'].iloc[-1] - recent_data['close'].iloc[-5]) / recent_data['close'].iloc[-5] * 100
            momentum_score = min(max((price_change + 5) * 10, 0), 100)
            
            # Volatility (25%)
            volatility = recent_data['close'].pct_change().std() * 100
            volatility_score = min(max(100 - volatility * 20, 0), 100)
            
            # Volume (25%)
            avg_volume = recent_data['volume'].mean()
            current_volume = recent_data['volume'].iloc[-1]
            volume_ratio = current_volume / avg_volume
            volume_score = min(max(volume_ratio * 50, 0), 100)
            
            # Market breadth (25%)
            breadth_score = 50  # Placeholder
            
            # Weighted average
            fear_greed = (momentum_score * 0.25 + volatility_score * 0.25 + 
                         volume_score * 0.25 + breadth_score * 0.25)
            
            return round(fear_greed, 1)
            
        except Exception as e:
            self.logger.error(f"Error calculating fear greed index: {str(e)}")
            return 50
    
    def _get_market_breadth(self):
        """Get market breadth indicators"""
        try:
            # Get A-share market data
            market_data = ak.stock_zh_a_spot_em()
            
            if market_data.empty:
                return {'advancing': 0, 'declining': 0, 'unchanged': 0}
            
            advancing = len(market_data[market_data['涨跌幅'] > 0])
            declining = len(market_data[market_data['涨跌幅'] < 0])
            unchanged = len(market_data[market_data['涨跌幅'] == 0])
            
            total = advancing + declining + unchanged
            
            return {
                'advancing': advancing,
                'declining': declining,
                'unchanged': unchanged,
                'advance_decline_ratio': advancing / declining if declining > 0 else 0,
                'advance_percentage': (advancing / total * 100) if total > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error getting market breadth: {str(e)}")
            return {'advancing': 0, 'declining': 0, 'unchanged': 0}
    
    def _get_volatility_index(self):
        """Get market volatility index"""
        try:
            # Get Shanghai Composite data
            sh_data = ak.stock_zh_index_daily(symbol="sh000001")
            
            if sh_data.empty:
                return 20  # Default volatility
            
            # Calculate 20-day volatility
            recent_data = sh_data.tail(20)
            returns = recent_data['close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(252) * 100  # Annualized volatility
            
            return round(volatility, 2)
            
        except Exception as e:
            self.logger.error(f"Error getting volatility index: {str(e)}")
            return 20
    
    def _get_investor_sentiment(self):
        """Get investor sentiment indicators"""
        try:
            # Get margin trading data as proxy for sentiment
            margin_data = ak.stock_margin_underlying_info_szse(date=datetime.now().strftime('%Y%m%d'))
            
            sentiment_score = 50  # Neutral default
            
            if not margin_data.empty:
                # Calculate sentiment based on margin trading activity
                total_margin = margin_data['融资余额'].sum()
                total_short = margin_data['融券余额'].sum()
                
                if total_margin > 0:
                    margin_ratio = total_short / total_margin
                    sentiment_score = min(max((1 - margin_ratio) * 100, 0), 100)
            
            return {
                'score': round(sentiment_score, 1),
                'level': self._get_sentiment_level(sentiment_score),
                'description': self._describe_investor_sentiment(sentiment_score)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting investor sentiment: {str(e)}")
            return {'score': 50, 'level': 'Neutral', 'description': 'Unable to determine sentiment'}
    
    def _get_news_sentiment(self):
        """Get news sentiment analysis"""
        # Placeholder for news sentiment analysis
        return {
            'score': 55,
            'positive_ratio': 0.6,
            'negative_ratio': 0.25,
            'neutral_ratio': 0.15,
            'trending_topics': ['经济复苏', '政策利好', '科技创新']
        }
    
    def _get_social_sentiment(self):
        """Get social media sentiment"""
        # Placeholder for social sentiment analysis
        return {
            'score': 52,
            'bullish_percentage': 58,
            'bearish_percentage': 32,
            'neutral_percentage': 10,
            'trending_stocks': ['000001', '000002', '600036']
        }
    
    def _calculate_overall_sentiment(self, data):
        """Calculate overall sentiment score"""
        try:
            scores = []
            weights = []
            
            if 'fear_greed_index' in data and data['fear_greed_index']:
                scores.append(data['fear_greed_index'])
                weights.append(0.3)
            
            if 'investor_sentiment' in data and data['investor_sentiment']:
                scores.append(data['investor_sentiment']['score'])
                weights.append(0.25)
            
            if 'news_sentiment' in data and data['news_sentiment']:
                scores.append(data['news_sentiment']['score'])
                weights.append(0.2)
            
            if 'social_sentiment' in data and data['social_sentiment']:
                scores.append(data['social_sentiment']['score'])
                weights.append(0.15)
            
            # Market breadth contribution
            if 'market_breadth' in data and data['market_breadth']:
                breadth_score = data['market_breadth'].get('advance_percentage', 50)
                scores.append(breadth_score)
                weights.append(0.1)
            
            if scores and weights:
                weighted_score = sum(s * w for s, w in zip(scores, weights)) / sum(weights)
                return round(weighted_score, 1)
            
            return 50  # Neutral default
            
        except Exception as e:
            self.logger.error(f"Error calculating overall sentiment: {str(e)}")
            return 50
    
    def _interpret_sentiment(self, score):
        """Interpret sentiment score"""
        if score >= 80:
            return '极度贪婪 - 市场情绪过热，需要谨慎'
        elif score >= 70:
            return '贪婪 - 市场情绪乐观，但要注意风险'
        elif score >= 60:
            return '乐观 - 市场情绪积极'
        elif score >= 40:
            return '中性 - 市场情绪平衡'
        elif score >= 30:
            return '谨慎 - 市场情绪偏悲观'
        elif score >= 20:
            return '恐惧 - 市场情绪悲观'
        else:
            return '极度恐惧 - 市场情绪极度悲观，可能存在机会'
    
    def _interpret_capital_flow(self, data):
        """Interpret capital flow data"""
        main_inflow = data.get('主力净流入-净额', 0)
        
        if main_inflow > 1000000000:  # 10亿
            return '主力大幅净流入，市场情绪积极'
        elif main_inflow > 0:
            return '主力净流入，资金面偏好'
        elif main_inflow > -1000000000:
            return '主力小幅净流出，观望情绪浓厚'
        else:
            return '主力大幅净流出，市场情绪谨慎'
    
    def _analyze_capital_flow_trend(self, data):
        """Analyze capital flow trend"""
        if len(data) < 5:
            return 'insufficient_data'
        
        recent_flows = data.head(5)['主力净流入-净额'].tolist()
        
        # Simple trend analysis
        positive_days = sum(1 for flow in recent_flows if flow > 0)
        
        if positive_days >= 4:
            return 'strong_inflow'
        elif positive_days >= 3:
            return 'moderate_inflow'
        elif positive_days <= 1:
            return 'strong_outflow'
        else:
            return 'mixed'
    
    def _calculate_sector_sentiment(self, sector_data):
        """Calculate sentiment score for a sector"""
        change_pct = float(sector_data.get('涨跌幅', 0))
        turnover = float(sector_data.get('换手率', 0))
        volume_ratio = float(sector_data.get('量比', 1))
        
        # Weighted sentiment calculation
        sentiment = (change_pct * 0.5 + 
                    min(turnover * 10, 50) * 0.3 + 
                    min((volume_ratio - 1) * 25, 25) * 0.2)
        
        return round(max(min(sentiment + 50, 100), 0), 1)
    
    def _get_sector_trend(self, change_pct):
        """Get sector trend based on change percentage"""
        if change_pct > 3:
            return 'strong_up'
        elif change_pct > 1:
            return 'up'
        elif change_pct > -1:
            return 'sideways'
        elif change_pct > -3:
            return 'down'
        else:
            return 'strong_down'
    
    def _analyze_sector_rotation(self, sectors):
        """Analyze sector rotation patterns"""
        hot_sectors = [s['name'] for s in sectors[:3]]
        cold_sectors = [s['name'] for s in sectors[-3:]]
        
        return {
            'rotation_type': 'growth_to_value' if '科技' in cold_sectors else 'value_to_growth',
            'hot_themes': hot_sectors,
            'cold_themes': cold_sectors,
            'recommendation': '关注热点板块轮动机会'
        }
    
    def _get_advance_decline_ratio(self):
        """Get advance/decline ratio"""
        breadth = self._get_market_breadth()
        return breadth.get('advance_decline_ratio', 1.0)
    
    def _get_new_highs_lows(self):
        """Get new highs and lows count"""
        return {
            'new_highs': 150,
            'new_lows': 80,
            'ratio': 1.875
        }
    
    def _get_margin_trading_data(self):
        """Get margin trading data"""
        return {
            'margin_balance': 1500000000000,  # 1.5万亿
            'margin_change': 2.5,
            'margin_ratio': 2.8
        }
    
    def _get_institutional_activity(self):
        """Get institutional activity indicators"""
        return {
            'net_buying': 5000000000,  # 50亿
            'activity_level': 'high',
            'focus_sectors': ['科技', '医药', '新能源']
        }
    
    def _get_market_momentum(self):
        """Get market momentum indicators"""
        return {
            'momentum_score': 65,
            'trend': 'bullish',
            'strength': 'moderate'
        }
    
    def _calculate_risk_appetite(self):
        """Calculate market risk appetite"""
        return {
            'score': 58,
            'level': 'moderate',
            'description': '市场风险偏好适中'
        }
    
    def _get_sentiment_level(self, score):
        """Get sentiment level from score"""
        if score >= 70:
            return 'Very Bullish'
        elif score >= 60:
            return 'Bullish'
        elif score >= 40:
            return 'Neutral'
        elif score >= 30:
            return 'Bearish'
        else:
            return 'Very Bearish'
    
    def _describe_investor_sentiment(self, score):
        """Describe investor sentiment"""
        if score >= 70:
            return '投资者情绪高涨，市场乐观情绪浓厚'
        elif score >= 60:
            return '投资者情绪积极，对市场前景看好'
        elif score >= 40:
            return '投资者情绪中性，观望情绪较重'
        elif score >= 30:
            return '投资者情绪谨慎，担忧情绪上升'
        else:
            return '投资者情绪悲观，恐慌情绪蔓延'