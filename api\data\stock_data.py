import akshare as ak
import tushare as ts
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import time
import logging
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
import sys
import os.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.cache import cached, cache_stock_data, cache_manager
from config import DataProviderConfig

class StockDataProvider:
    """
    Stock Data Provider for Chinese Stock Market
    
    This class handles data fetching from akshare and Tushare APIs,
    providing unified interface for stock data access with caching support.
    """
    
    def __init__(self, config: Optional[DataProviderConfig] = None):
        if config is None:
            config = DataProviderConfig.from_env()

        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialize Tushare
        self.tushare_token = os.getenv('TUSHARE_TOKEN')
        if self.tushare_token:
            ts.set_token(self.tushare_token)
            self.ts_pro = ts.pro_api()
        else:
            self.ts_pro = None
            self.logger.warning("Tushare token not found. Some features may be limited.")

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.5  # 500ms between requests
        self.tushare_last_request = 0
        self.tushare_min_interval = 0.2  # 200ms for Tushare
    
    @cached(ttl=300, key_prefix="stock_data:")  # Cache for 5 minutes
    def get_stock_data(self, stock_code: str, period: str = 'daily', days: int = 100,
                      start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """
        Get stock price data for a given stock code with caching

        Args:
            stock_code: Stock code (e.g., '000001', '600000')
            period: Data period ('daily', 'weekly', 'monthly')
            days: Number of days to fetch (if start_date/end_date not provided)
            start_date: Start date (YYYY-MM-DD format)
            end_date: End date (YYYY-MM-DD format)

        Returns:
            List of price data dictionaries
        """
        try:
            # Normalize stock code
            normalized_code = self._normalize_stock_code(stock_code)
            
            # Rate limiting
            self._rate_limit()
            
            # Calculate date range if not provided
            if not start_date or not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
            else:
                # Convert YYYY-MM-DD to YYYYMMDD format for akshare
                start_date = start_date.replace('-', '')
                end_date = end_date.replace('-', '')
            
            # Fetch data from akshare
            try:
                if period == 'daily':
                    df = ak.stock_zh_a_hist(symbol=normalized_code, period="daily", 
                                          start_date=start_date, end_date=end_date, adjust="qfq")
                elif period == 'weekly':
                    df = ak.stock_zh_a_hist(symbol=normalized_code, period="weekly", 
                                          start_date=start_date, end_date=end_date, adjust="qfq")
                elif period == 'monthly':
                    df = ak.stock_zh_a_hist(symbol=normalized_code, period="monthly", 
                                          start_date=start_date, end_date=end_date, adjust="qfq")
                else:
                    raise ValueError(f"Unsupported period: {period}")
                
                if df.empty:
                    self.logger.warning(f"No data found for stock {stock_code}")
                    return []
                
                # Convert to standard format
                price_data = self._convert_akshare_data(df)
                
                # Cache the result
                self._save_to_cache(cache_key, price_data)
                
                return price_data
                
            except Exception as e:
                self.logger.error(f"Error fetching data from akshare for {stock_code}: {e}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error in get_stock_data for {stock_code}: {e}")
            return []
    
    def get_stock_basic_info(self, stock_code: str) -> Dict[str, Any]:
        """
        Get basic stock information
        
        Args:
            stock_code: Stock code
            
        Returns:
            Dictionary with basic stock info
        """
        try:
            normalized_code = self._normalize_stock_code(stock_code)
            
            # Check cache
            cache_key = f"info_{normalized_code}"
            cached_info = self._get_from_cache(cache_key)
            if cached_info:
                return cached_info
            
            self._rate_limit()
            
            # Get stock info from akshare
            try:
                # Get real-time data
                df_realtime = ak.stock_zh_a_spot_em()
                stock_info = df_realtime[df_realtime['代码'] == normalized_code]
                
                if not stock_info.empty:
                    info = {
                        'code': normalized_code,
                        'name': stock_info['名称'].iloc[0],
                        'price': float(stock_info['最新价'].iloc[0]),
                        'change': float(stock_info['涨跌额'].iloc[0]),
                        'change_pct': float(stock_info['涨跌幅'].iloc[0]),
                        'volume': float(stock_info['成交量'].iloc[0]),
                        'turnover': float(stock_info['成交额'].iloc[0]),
                        'market_cap': float(stock_info['总市值'].iloc[0]) if '总市值' in stock_info.columns else 0
                    }
                else:
                    # Fallback: basic info
                    info = {
                        'code': normalized_code,
                        'name': self._get_stock_name_fallback(normalized_code),
                        'price': 0,
                        'change': 0,
                        'change_pct': 0,
                        'volume': 0,
                        'turnover': 0,
                        'market_cap': 0
                    }
                
                # Cache the result
                self._save_to_cache(cache_key, info, timeout=60)  # Shorter cache for real-time data
                
                return info
                
            except Exception as e:
                self.logger.error(f"Error fetching stock info from akshare for {stock_code}: {e}")
                return {
                    'code': normalized_code,
                    'name': self._get_stock_name_fallback(normalized_code),
                    'price': 0,
                    'change': 0,
                    'change_pct': 0,
                    'volume': 0,
                    'turnover': 0,
                    'market_cap': 0
                }
                
        except Exception as e:
            self.logger.error(f"Error in get_stock_basic_info for {stock_code}: {e}")
            return {'code': stock_code, 'name': 'Unknown', 'price': 0}
    
    def get_stock_list(self, market_cap_range: Dict[str, float] = None, 
                      industry_filter: List[str] = None) -> List[str]:
        """
        Get list of stocks for screening
        
        Args:
            market_cap_range: Dictionary with 'min' and 'max' market cap values
            industry_filter: List of industry names to filter
            
        Returns:
            List of stock codes
        """
        try:
            cache_key = "stock_list"
            cached_list = self._get_from_cache(cache_key)
            if cached_list:
                return cached_list
            
            self._rate_limit()
            
            # Get stock list from akshare
            try:
                # Get A-share stock list
                df_stocks = ak.stock_zh_a_spot_em()
                
                stock_codes = []
                for _, row in df_stocks.iterrows():
                    code = row['代码']
                    market_cap = float(row['总市值']) if '总市值' in row and pd.notna(row['总市值']) else 0
                    
                    # Apply market cap filter
                    if market_cap_range:
                        min_cap = market_cap_range.get('min', 0)
                        max_cap = market_cap_range.get('max', float('inf'))
                        if not (min_cap <= market_cap <= max_cap):
                            continue
                    
                    stock_codes.append(code)
                
                # Limit to reasonable number for screening
                stock_codes = stock_codes[:500]  # Top 500 by market cap
                
                # Cache the result
                self._save_to_cache(cache_key, stock_codes, timeout=3600)  # 1 hour cache
                
                return stock_codes
                
            except Exception as e:
                self.logger.error(f"Error fetching stock list from akshare: {e}")
                # Return some popular stocks as fallback
                return self._get_popular_stocks_fallback()
                
        except Exception as e:
            self.logger.error(f"Error in get_stock_list: {e}")
            return self._get_popular_stocks_fallback()
    
    @cached(ttl=600, key_prefix="popular_stocks:")  # Cache for 10 minutes
    def get_popular_stocks(self, market: str = 'all', limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get list of popular stocks
        
        Args:
            market: Market filter ('SZ', 'SH', or 'all')
            limit: Maximum number of stocks to return
            
        Returns:
            List of stock dictionaries
        """
        try:
            cache_key = f"popular_stocks_{market}_{limit}"
            cached_stocks = self._get_from_cache(cache_key)
            if cached_stocks:
                return cached_stocks
            
            self._rate_limit()
            
            # Get popular stocks from akshare
            try:
                df_stocks = ak.stock_zh_a_spot_em()
                
                # Filter by market if specified
                if market == 'SZ':
                    df_stocks = df_stocks[df_stocks['代码'].str.startswith(('000', '002', '300'))]
                elif market == 'SH':
                    df_stocks = df_stocks[df_stocks['代码'].str.startswith('6')]
                
                # Sort by market cap or volume
                if '总市值' in df_stocks.columns:
                    df_stocks = df_stocks.sort_values('总市值', ascending=False)
                else:
                    df_stocks = df_stocks.sort_values('成交额', ascending=False)
                
                stocks = []
                for _, row in df_stocks.head(limit).iterrows():
                    stocks.append({
                        'code': row['代码'],
                        'name': row['名称'],
                        'price': float(row['最新价']),
                        'change_pct': float(row['涨跌幅']),
                        'volume': float(row['成交量']),
                        'market_cap': float(row['总市值']) if '总市值' in row and pd.notna(row['总市值']) else 0
                    })
                
                # Cache the result
                self._save_to_cache(cache_key, stocks, timeout=1800)  # 30 minutes cache
                
                return stocks
                
            except Exception as e:
                self.logger.error(f"Error fetching popular stocks from akshare: {e}")
                return self._get_popular_stocks_fallback_dict()
                
        except Exception as e:
            self.logger.error(f"Error in get_popular_stocks: {e}")
            return self._get_popular_stocks_fallback_dict()
    
    def _normalize_stock_code(self, stock_code: str) -> str:
        """
        Normalize stock code to akshare format
        
        Args:
            stock_code: Input stock code
            
        Returns:
            Normalized stock code
        """
        # Remove any suffixes like .SZ, .SH
        code = stock_code.split('.')[0]
        
        # Ensure 6 digits
        code = code.zfill(6)
        
        return code
    
    def _convert_akshare_data(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Convert akshare DataFrame to standard format
        
        Args:
            df: akshare DataFrame
            
        Returns:
            List of price data dictionaries
        """
        price_data = []
        
        for _, row in df.iterrows():
            price_data.append({
                'date': row['日期'].strftime('%Y-%m-%d') if hasattr(row['日期'], 'strftime') else str(row['日期']),
                'open': float(row['开盘']),
                'high': float(row['最高']),
                'low': float(row['最低']),
                'close': float(row['收盘']),
                'volume': float(row['成交量']),
                'amount': float(row['成交额']) if '成交额' in row else 0
            })
        
        return price_data
    
    def _rate_limit(self):
        """
        Implement rate limiting to avoid overwhelming the API
        """
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last_request)
        
        self.last_request_time = time.time()
    
    def _get_from_cache(self, key: str) -> Optional[Any]:
        """
        Get data from cache if not expired
        
        Args:
            key: Cache key
            
        Returns:
            Cached data or None
        """
        if key in self.cache:
            data, timestamp = self.cache[key]
            if time.time() - timestamp < self.cache_timeout:
                return data
            else:
                del self.cache[key]
        return None
    
    def _save_to_cache(self, key: str, data: Any, timeout: int = None):
        """
        Save data to cache
        
        Args:
            key: Cache key
            data: Data to cache
            timeout: Custom timeout (uses default if None)
        """
        self.cache[key] = (data, time.time())
    
    def _get_stock_name_fallback(self, stock_code: str) -> str:
        """
        Get stock name fallback when API fails
        
        Args:
            stock_code: Stock code
            
        Returns:
            Stock name or generic name
        """
        # Some well-known stock names
        known_stocks = {
            '000001': '平安银行',
            '000002': '万科A',
            '600000': '浦发银行',
            '600036': '招商银行',
            '600519': '贵州茅台',
            '000858': '五粮液'
        }
        
        return known_stocks.get(stock_code, f'股票{stock_code}')
    
    def _get_popular_stocks_fallback(self) -> List[str]:
        """
        Get popular stocks fallback list
        
        Returns:
            List of popular stock codes
        """
        return [
            '000001', '000002', '000858', '600000', '600036', '600519',
            '000651', '002415', '300059', '600276', '002304', '000568'
        ]
    
    def _get_popular_stocks_fallback_dict(self) -> List[Dict[str, Any]]:
        """
        Get popular stocks fallback as dictionaries
        
        Returns:
            List of stock dictionaries
        """
        fallback_stocks = [
            {'code': '000001', 'name': '平安银行', 'price': 0, 'change_pct': 0, 'volume': 0, 'market_cap': 0},
            {'code': '000002', 'name': '万科A', 'price': 0, 'change_pct': 0, 'volume': 0, 'market_cap': 0},
            {'code': '600000', 'name': '浦发银行', 'price': 0, 'change_pct': 0, 'volume': 0, 'market_cap': 0},
            {'code': '600036', 'name': '招商银行', 'price': 0, 'change_pct': 0, 'volume': 0, 'market_cap': 0},
            {'code': '600519', 'name': '贵州茅台', 'price': 0, 'change_pct': 0, 'volume': 0, 'market_cap': 0}
        ]
        return fallback_stocks
    
    def get_real_time_data(self, stock_codes: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Get real-time stock data for multiple stocks
        
        Args:
            stock_codes: List of stock codes
            
        Returns:
            Dictionary with stock codes as keys and real-time data as values
        """
        try:
            cache_key = f"realtime_{','.join(sorted(stock_codes))}"
            cached_data = self._get_from_cache(cache_key)
            if cached_data:
                return cached_data
            
            real_time_data = {}
            
            # Use ThreadPoolExecutor for concurrent requests
            with ThreadPoolExecutor(max_workers=5) as executor:
                future_to_code = {}
                
                for code in stock_codes[:20]:  # Limit to 20 stocks for performance
                    future = executor.submit(self._get_single_real_time_data, code)
                    future_to_code[future] = code
                
                for future in as_completed(future_to_code):
                    code = future_to_code[future]
                    try:
                        data = future.result(timeout=10)
                        if data:
                            real_time_data[code] = data
                    except Exception as e:
                        self.logger.error(f"Error getting real-time data for {code}: {e}")
            
            # Cache for 30 seconds
            self._save_to_cache(cache_key, real_time_data, timeout=30)
            
            return real_time_data
            
        except Exception as e:
            self.logger.error(f"Error in get_real_time_data: {e}")
            return {}
    
    def _get_single_real_time_data(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        Get real-time data for a single stock
        
        Args:
            stock_code: Stock code
            
        Returns:
            Real-time data dictionary or None
        """
        try:
            self._rate_limit()
            
            # Try akshare first
            try:
                df = ak.stock_zh_a_spot_em()
                stock_data = df[df['代码'] == stock_code]
                
                if not stock_data.empty:
                    row = stock_data.iloc[0]
                    return {
                        'code': stock_code,
                        'name': row['名称'],
                        'price': float(row['最新价']),
                        'open': float(row['今开']),
                        'high': float(row['最高']),
                        'low': float(row['最低']),
                        'prev_close': float(row['昨收']),
                        'change': float(row['涨跌额']),
                        'change_pct': float(row['涨跌幅']),
                        'volume': float(row['成交量']),
                        'amount': float(row['成交额']),
                        'market_cap': float(row['总市值']) if '总市值' in row and pd.notna(row['总市值']) else 0,
                        'pe_ratio': float(row['市盈率-动态']) if '市盈率-动态' in row and pd.notna(row['市盈率-动态']) else 0,
                        'pb_ratio': float(row['市净率']) if '市净率' in row and pd.notna(row['市净率']) else 0,
                        'timestamp': datetime.now().isoformat()
                    }
            except Exception as e:
                self.logger.warning(f"akshare real-time data failed for {stock_code}: {e}")
            
            # Try Tushare as fallback
            if self.ts_pro:
                try:
                    self._tushare_rate_limit()
                    
                    # Convert code format for Tushare
                    ts_code = self._convert_to_tushare_code(stock_code)
                    
                    df = self.ts_pro.daily_basic(ts_code=ts_code, trade_date=datetime.now().strftime('%Y%m%d'))
                    
                    if not df.empty:
                        row = df.iloc[0]
                        return {
                            'code': stock_code,
                            'name': '',  # Tushare doesn't provide name in daily_basic
                            'price': float(row['close']) if 'close' in row and pd.notna(row['close']) else 0,
                            'market_cap': float(row['total_mv']) * 10000 if 'total_mv' in row and pd.notna(row['total_mv']) else 0,
                            'pe_ratio': float(row['pe']) if 'pe' in row and pd.notna(row['pe']) else 0,
                            'pb_ratio': float(row['pb']) if 'pb' in row and pd.notna(row['pb']) else 0,
                            'timestamp': datetime.now().isoformat()
                        }
                except Exception as e:
                    self.logger.warning(f"Tushare real-time data failed for {stock_code}: {e}")
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting single real-time data for {stock_code}: {e}")
            return None
    
    def get_market_overview(self) -> Dict[str, Any]:
        """
        Get market overview data
        
        Returns:
            Market overview dictionary
        """
        try:
            cache_key = "market_overview"
            cached_data = self._get_from_cache(cache_key)
            if cached_data:
                return cached_data
            
            overview = {
                'timestamp': datetime.now().isoformat(),
                'indices': {},
                'market_stats': {},
                'top_gainers': [],
                'top_losers': [],
                'most_active': []
            }
            
            try:
                # Get major indices
                self._rate_limit()
                
                # Shanghai Composite
                sh_index = ak.stock_zh_index_spot_em(symbol="sh000001")
                if not sh_index.empty:
                    row = sh_index.iloc[0]
                    overview['indices']['sh_composite'] = {
                        'name': '上证综指',
                        'value': float(row['最新价']),
                        'change': float(row['涨跌额']),
                        'change_pct': float(row['涨跌幅'])
                    }
                
                # Shenzhen Component
                sz_index = ak.stock_zh_index_spot_em(symbol="sz399001")
                if not sz_index.empty:
                    row = sz_index.iloc[0]
                    overview['indices']['sz_component'] = {
                        'name': '深证成指',
                        'value': float(row['最新价']),
                        'change': float(row['涨跌额']),
                        'change_pct': float(row['涨跌幅'])
                    }
                
                # ChiNext
                cy_index = ak.stock_zh_index_spot_em(symbol="sz399006")
                if not cy_index.empty:
                    row = cy_index.iloc[0]
                    overview['indices']['chinext'] = {
                        'name': '创业板指',
                        'value': float(row['最新价']),
                        'change': float(row['涨跌额']),
                        'change_pct': float(row['涨跌幅'])
                    }
                
                # Get market statistics
                self._rate_limit()
                df_stocks = ak.stock_zh_a_spot_em()
                
                if not df_stocks.empty:
                    total_stocks = len(df_stocks)
                    rising_stocks = len(df_stocks[df_stocks['涨跌幅'] > 0])
                    falling_stocks = len(df_stocks[df_stocks['涨跌幅'] < 0])
                    unchanged_stocks = total_stocks - rising_stocks - falling_stocks
                    
                    overview['market_stats'] = {
                        'total_stocks': total_stocks,
                        'rising_stocks': rising_stocks,
                        'falling_stocks': falling_stocks,
                        'unchanged_stocks': unchanged_stocks,
                        'rising_ratio': rising_stocks / total_stocks if total_stocks > 0 else 0
                    }
                    
                    # Top gainers
                    top_gainers = df_stocks.nlargest(10, '涨跌幅')
                    overview['top_gainers'] = [
                        {
                            'code': row['代码'],
                            'name': row['名称'],
                            'price': float(row['最新价']),
                            'change_pct': float(row['涨跌幅'])
                        }
                        for _, row in top_gainers.iterrows()
                    ]
                    
                    # Top losers
                    top_losers = df_stocks.nsmallest(10, '涨跌幅')
                    overview['top_losers'] = [
                        {
                            'code': row['代码'],
                            'name': row['名称'],
                            'price': float(row['最新价']),
                            'change_pct': float(row['涨跌幅'])
                        }
                        for _, row in top_losers.iterrows()
                    ]
                    
                    # Most active by volume
                    most_active = df_stocks.nlargest(10, '成交量')
                    overview['most_active'] = [
                        {
                            'code': row['代码'],
                            'name': row['名称'],
                            'price': float(row['最新价']),
                            'volume': float(row['成交量']),
                            'amount': float(row['成交额'])
                        }
                        for _, row in most_active.iterrows()
                    ]
                
            except Exception as e:
                self.logger.error(f"Error getting market overview from akshare: {e}")
            
            # Cache for 1 minute
            self._save_to_cache(cache_key, overview, timeout=60)
            
            return overview
            
        except Exception as e:
            self.logger.error(f"Error in get_market_overview: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'indices': {},
                'market_stats': {},
                'top_gainers': [],
                'top_losers': [],
                'most_active': []
            }
    
    def get_stock_fundamentals(self, stock_code: str) -> Dict[str, Any]:
        """
        Get fundamental data for a stock using Tushare
        
        Args:
            stock_code: Stock code
            
        Returns:
            Fundamental data dictionary
        """
        try:
            if not self.ts_pro:
                self.logger.warning("Tushare not available for fundamental data")
                return {}
            
            cache_key = f"fundamentals_{stock_code}"
            cached_data = self._get_from_cache(cache_key)
            if cached_data:
                return cached_data
            
            self._tushare_rate_limit()
            
            ts_code = self._convert_to_tushare_code(stock_code)
            fundamentals = {
                'code': stock_code,
                'timestamp': datetime.now().isoformat()
            }
            
            # Get basic info
            try:
                basic_info = self.ts_pro.stock_basic(ts_code=ts_code)
                if not basic_info.empty:
                    row = basic_info.iloc[0]
                    fundamentals['basic_info'] = {
                        'name': row['name'],
                        'industry': row['industry'],
                        'area': row['area'],
                        'market': row['market'],
                        'list_date': row['list_date']
                    }
            except Exception as e:
                self.logger.warning(f"Error getting basic info for {stock_code}: {e}")
            
            # Get latest financial data
            try:
                # Get latest quarterly report date
                current_date = datetime.now()
                if current_date.month <= 3:
                    report_date = f"{current_date.year - 1}1231"
                elif current_date.month <= 6:
                    report_date = f"{current_date.year}0331"
                elif current_date.month <= 9:
                    report_date = f"{current_date.year}0630"
                else:
                    report_date = f"{current_date.year}0930"
                
                # Income statement
                income = self.ts_pro.income(ts_code=ts_code, period=report_date)
                if not income.empty:
                    row = income.iloc[0]
                    fundamentals['income'] = {
                        'revenue': float(row['revenue']) if pd.notna(row['revenue']) else 0,
                        'net_profit': float(row['n_income']) if pd.notna(row['n_income']) else 0,
                        'gross_profit': float(row['revenue']) - float(row['oper_cost']) if pd.notna(row['revenue']) and pd.notna(row['oper_cost']) else 0
                    }
                
                # Balance sheet
                balance = self.ts_pro.balancesheet(ts_code=ts_code, period=report_date)
                if not balance.empty:
                    row = balance.iloc[0]
                    fundamentals['balance'] = {
                        'total_assets': float(row['total_assets']) if pd.notna(row['total_assets']) else 0,
                        'total_liab': float(row['total_liab']) if pd.notna(row['total_liab']) else 0,
                        'total_equity': float(row['total_hldr_eqy_exc_min_int']) if pd.notna(row['total_hldr_eqy_exc_min_int']) else 0
                    }
                
                # Cash flow
                cashflow = self.ts_pro.cashflow(ts_code=ts_code, period=report_date)
                if not cashflow.empty:
                    row = cashflow.iloc[0]
                    fundamentals['cashflow'] = {
                        'operating_cf': float(row['n_cashflow_act']) if pd.notna(row['n_cashflow_act']) else 0,
                        'investing_cf': float(row['n_cashflow_inv_act']) if pd.notna(row['n_cashflow_inv_act']) else 0,
                        'financing_cf': float(row['n_cashflow_fin_act']) if pd.notna(row['n_cashflow_fin_act']) else 0
                    }
                
            except Exception as e:
                self.logger.warning(f"Error getting financial data for {stock_code}: {e}")
            
            # Cache for 1 hour
            self._save_to_cache(cache_key, fundamentals, timeout=3600)
            
            return fundamentals
            
        except Exception as e:
            self.logger.error(f"Error in get_stock_fundamentals for {stock_code}: {e}")
            return {}
    
    def _convert_to_tushare_code(self, stock_code: str) -> str:
        """
        Convert stock code to Tushare format
        
        Args:
            stock_code: Standard stock code
            
        Returns:
            Tushare format code (e.g., '000001.SZ')
        """
        code = self._normalize_stock_code(stock_code)
        
        if code.startswith('6'):
            return f"{code}.SH"
        else:
            return f"{code}.SZ"
    
    def _tushare_rate_limit(self):
        """
        Implement rate limiting for Tushare API
        """
        current_time = time.time()
        time_since_last_request = current_time - self.tushare_last_request
        
        if time_since_last_request < self.tushare_min_interval:
            time.sleep(self.tushare_min_interval - time_since_last_request)
        
        self.tushare_last_request = time.time()