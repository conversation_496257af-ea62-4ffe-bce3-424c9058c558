import akshare as ak
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import time
import logging

class StockDataProvider:
    """
    Stock Data Provider for Chinese Stock Market
    
    This class handles data fetching from akshare and Tushare APIs,
    providing unified interface for stock data access with caching support.
    """
    
    def __init__(self):
        self.cache = {}  # Simple in-memory cache
        self.cache_timeout = 300  # 5 minutes cache timeout
        self.logger = logging.getLogger(__name__)
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.5  # 500ms between requests
    
    def get_stock_data(self, stock_code: str, period: str = 'daily', days: int = 100, 
                      start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """
        Get stock price data for a given stock code
        
        Args:
            stock_code: Stock code (e.g., '000001', '600000')
            period: Data period ('daily', 'weekly', 'monthly')
            days: Number of days to fetch (if start_date/end_date not provided)
            start_date: Start date (YYYY-MM-DD format)
            end_date: End date (YYYY-MM-DD format)
            
        Returns:
            List of price data dictionaries
        """
        try:
            # Normalize stock code
            normalized_code = self._normalize_stock_code(stock_code)
            
            # Check cache first
            cache_key = f"{normalized_code}_{period}_{days}_{start_date}_{end_date}"
            cached_data = self._get_from_cache(cache_key)
            if cached_data:
                return cached_data
            
            # Rate limiting
            self._rate_limit()
            
            # Calculate date range if not provided
            if not start_date or not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
            else:
                # Convert YYYY-MM-DD to YYYYMMDD format for akshare
                start_date = start_date.replace('-', '')
                end_date = end_date.replace('-', '')
            
            # Fetch data from akshare
            try:
                if period == 'daily':
                    df = ak.stock_zh_a_hist(symbol=normalized_code, period="daily", 
                                          start_date=start_date, end_date=end_date, adjust="qfq")
                elif period == 'weekly':
                    df = ak.stock_zh_a_hist(symbol=normalized_code, period="weekly", 
                                          start_date=start_date, end_date=end_date, adjust="qfq")
                elif period == 'monthly':
                    df = ak.stock_zh_a_hist(symbol=normalized_code, period="monthly", 
                                          start_date=start_date, end_date=end_date, adjust="qfq")
                else:
                    raise ValueError(f"Unsupported period: {period}")
                
                if df.empty:
                    self.logger.warning(f"No data found for stock {stock_code}")
                    return []
                
                # Convert to standard format
                price_data = self._convert_akshare_data(df)
                
                # Cache the result
                self._save_to_cache(cache_key, price_data)
                
                return price_data
                
            except Exception as e:
                self.logger.error(f"Error fetching data from akshare for {stock_code}: {e}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error in get_stock_data for {stock_code}: {e}")
            return []
    
    def get_stock_basic_info(self, stock_code: str) -> Dict[str, Any]:
        """
        Get basic stock information
        
        Args:
            stock_code: Stock code
            
        Returns:
            Dictionary with basic stock info
        """
        try:
            normalized_code = self._normalize_stock_code(stock_code)
            
            # Check cache
            cache_key = f"info_{normalized_code}"
            cached_info = self._get_from_cache(cache_key)
            if cached_info:
                return cached_info
            
            self._rate_limit()
            
            # Get stock info from akshare
            try:
                # Get real-time data
                df_realtime = ak.stock_zh_a_spot_em()
                stock_info = df_realtime[df_realtime['代码'] == normalized_code]
                
                if not stock_info.empty:
                    info = {
                        'code': normalized_code,
                        'name': stock_info['名称'].iloc[0],
                        'price': float(stock_info['最新价'].iloc[0]),
                        'change': float(stock_info['涨跌额'].iloc[0]),
                        'change_pct': float(stock_info['涨跌幅'].iloc[0]),
                        'volume': float(stock_info['成交量'].iloc[0]),
                        'turnover': float(stock_info['成交额'].iloc[0]),
                        'market_cap': float(stock_info['总市值'].iloc[0]) if '总市值' in stock_info.columns else 0
                    }
                else:
                    # Fallback: basic info
                    info = {
                        'code': normalized_code,
                        'name': self._get_stock_name_fallback(normalized_code),
                        'price': 0,
                        'change': 0,
                        'change_pct': 0,
                        'volume': 0,
                        'turnover': 0,
                        'market_cap': 0
                    }
                
                # Cache the result
                self._save_to_cache(cache_key, info, timeout=60)  # Shorter cache for real-time data
                
                return info
                
            except Exception as e:
                self.logger.error(f"Error fetching stock info from akshare for {stock_code}: {e}")
                return {
                    'code': normalized_code,
                    'name': self._get_stock_name_fallback(normalized_code),
                    'price': 0,
                    'change': 0,
                    'change_pct': 0,
                    'volume': 0,
                    'turnover': 0,
                    'market_cap': 0
                }
                
        except Exception as e:
            self.logger.error(f"Error in get_stock_basic_info for {stock_code}: {e}")
            return {'code': stock_code, 'name': 'Unknown', 'price': 0}
    
    def get_stock_list(self, market_cap_range: Dict[str, float] = None, 
                      industry_filter: List[str] = None) -> List[str]:
        """
        Get list of stocks for screening
        
        Args:
            market_cap_range: Dictionary with 'min' and 'max' market cap values
            industry_filter: List of industry names to filter
            
        Returns:
            List of stock codes
        """
        try:
            cache_key = "stock_list"
            cached_list = self._get_from_cache(cache_key)
            if cached_list:
                return cached_list
            
            self._rate_limit()
            
            # Get stock list from akshare
            try:
                # Get A-share stock list
                df_stocks = ak.stock_zh_a_spot_em()
                
                stock_codes = []
                for _, row in df_stocks.iterrows():
                    code = row['代码']
                    market_cap = float(row['总市值']) if '总市值' in row and pd.notna(row['总市值']) else 0
                    
                    # Apply market cap filter
                    if market_cap_range:
                        min_cap = market_cap_range.get('min', 0)
                        max_cap = market_cap_range.get('max', float('inf'))
                        if not (min_cap <= market_cap <= max_cap):
                            continue
                    
                    stock_codes.append(code)
                
                # Limit to reasonable number for screening
                stock_codes = stock_codes[:500]  # Top 500 by market cap
                
                # Cache the result
                self._save_to_cache(cache_key, stock_codes, timeout=3600)  # 1 hour cache
                
                return stock_codes
                
            except Exception as e:
                self.logger.error(f"Error fetching stock list from akshare: {e}")
                # Return some popular stocks as fallback
                return self._get_popular_stocks_fallback()
                
        except Exception as e:
            self.logger.error(f"Error in get_stock_list: {e}")
            return self._get_popular_stocks_fallback()
    
    def get_popular_stocks(self, market: str = 'all', limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get list of popular stocks
        
        Args:
            market: Market filter ('SZ', 'SH', or 'all')
            limit: Maximum number of stocks to return
            
        Returns:
            List of stock dictionaries
        """
        try:
            cache_key = f"popular_stocks_{market}_{limit}"
            cached_stocks = self._get_from_cache(cache_key)
            if cached_stocks:
                return cached_stocks
            
            self._rate_limit()
            
            # Get popular stocks from akshare
            try:
                df_stocks = ak.stock_zh_a_spot_em()
                
                # Filter by market if specified
                if market == 'SZ':
                    df_stocks = df_stocks[df_stocks['代码'].str.startswith(('000', '002', '300'))]
                elif market == 'SH':
                    df_stocks = df_stocks[df_stocks['代码'].str.startswith('6')]
                
                # Sort by market cap or volume
                if '总市值' in df_stocks.columns:
                    df_stocks = df_stocks.sort_values('总市值', ascending=False)
                else:
                    df_stocks = df_stocks.sort_values('成交额', ascending=False)
                
                stocks = []
                for _, row in df_stocks.head(limit).iterrows():
                    stocks.append({
                        'code': row['代码'],
                        'name': row['名称'],
                        'price': float(row['最新价']),
                        'change_pct': float(row['涨跌幅']),
                        'volume': float(row['成交量']),
                        'market_cap': float(row['总市值']) if '总市值' in row and pd.notna(row['总市值']) else 0
                    })
                
                # Cache the result
                self._save_to_cache(cache_key, stocks, timeout=1800)  # 30 minutes cache
                
                return stocks
                
            except Exception as e:
                self.logger.error(f"Error fetching popular stocks from akshare: {e}")
                return self._get_popular_stocks_fallback_dict()
                
        except Exception as e:
            self.logger.error(f"Error in get_popular_stocks: {e}")
            return self._get_popular_stocks_fallback_dict()
    
    def _normalize_stock_code(self, stock_code: str) -> str:
        """
        Normalize stock code to akshare format
        
        Args:
            stock_code: Input stock code
            
        Returns:
            Normalized stock code
        """
        # Remove any suffixes like .SZ, .SH
        code = stock_code.split('.')[0]
        
        # Ensure 6 digits
        code = code.zfill(6)
        
        return code
    
    def _convert_akshare_data(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Convert akshare DataFrame to standard format
        
        Args:
            df: akshare DataFrame
            
        Returns:
            List of price data dictionaries
        """
        price_data = []
        
        for _, row in df.iterrows():
            price_data.append({
                'date': row['日期'].strftime('%Y-%m-%d') if hasattr(row['日期'], 'strftime') else str(row['日期']),
                'open': float(row['开盘']),
                'high': float(row['最高']),
                'low': float(row['最低']),
                'close': float(row['收盘']),
                'volume': float(row['成交量']),
                'amount': float(row['成交额']) if '成交额' in row else 0
            })
        
        return price_data
    
    def _rate_limit(self):
        """
        Implement rate limiting to avoid overwhelming the API
        """
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last_request)
        
        self.last_request_time = time.time()
    
    def _get_from_cache(self, key: str) -> Optional[Any]:
        """
        Get data from cache if not expired
        
        Args:
            key: Cache key
            
        Returns:
            Cached data or None
        """
        if key in self.cache:
            data, timestamp = self.cache[key]
            if time.time() - timestamp < self.cache_timeout:
                return data
            else:
                del self.cache[key]
        return None
    
    def _save_to_cache(self, key: str, data: Any, timeout: int = None):
        """
        Save data to cache
        
        Args:
            key: Cache key
            data: Data to cache
            timeout: Custom timeout (uses default if None)
        """
        self.cache[key] = (data, time.time())
    
    def _get_stock_name_fallback(self, stock_code: str) -> str:
        """
        Get stock name fallback when API fails
        
        Args:
            stock_code: Stock code
            
        Returns:
            Stock name or generic name
        """
        # Some well-known stock names
        known_stocks = {
            '000001': '平安银行',
            '000002': '万科A',
            '600000': '浦发银行',
            '600036': '招商银行',
            '600519': '贵州茅台',
            '000858': '五粮液'
        }
        
        return known_stocks.get(stock_code, f'股票{stock_code}')
    
    def _get_popular_stocks_fallback(self) -> List[str]:
        """
        Get popular stocks fallback list
        
        Returns:
            List of popular stock codes
        """
        return [
            '000001', '000002', '000858', '600000', '600036', '600519',
            '000651', '002415', '300059', '600276', '002304', '000568'
        ]
    
    def _get_popular_stocks_fallback_dict(self) -> List[Dict[str, Any]]:
        """
        Get popular stocks fallback as dictionaries
        
        Returns:
            List of stock dictionaries
        """
        fallback_stocks = [
            {'code': '000001', 'name': '平安银行', 'price': 0, 'change_pct': 0, 'volume': 0, 'market_cap': 0},
            {'code': '000002', 'name': '万科A', 'price': 0, 'change_pct': 0, 'volume': 0, 'market_cap': 0},
            {'code': '600000', 'name': '浦发银行', 'price': 0, 'change_pct': 0, 'volume': 0, 'market_cap': 0},
            {'code': '600036', 'name': '招商银行', 'price': 0, 'change_pct': 0, 'volume': 0, 'market_cap': 0},
            {'code': '600519', 'name': '贵州茅台', 'price': 0, 'change_pct': 0, 'volume': 0, 'market_cap': 0}
        ]
        return fallback_stocks