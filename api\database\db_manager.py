import sqlite3
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import json
import os

class DatabaseManager:
    """
    Database Manager for Trading Analysis Application
    
    Handles SQLite database operations for user data, stock information,
    price data, and analysis history.
    """
    
    def __init__(self, db_path: str = 'trading_analysis.db'):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._init_database()
    
    def _init_database(self):
        """
        Initialize database and create tables if they don't exist
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create users table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username VARCHAR(50) UNIQUE NOT NULL,
                        email VARCHAR(100) UNIQUE NOT NULL,
                        password_hash VARCHAR(255) NOT NULL,
                        user_type VARCHAR(20) DEFAULT 'basic',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        last_login TIMESTAMP,
                        is_active BOOLEAN DEFAULT 1
                    )
                ''')
                
                # Create stock_info table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS stock_info (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stock_code VARCHAR(20) UNIQUE NOT NULL,
                        stock_name VARCHAR(100) NOT NULL,
                        market VARCHAR(10) NOT NULL,
                        industry VARCHAR(50),
                        market_cap DECIMAL(15,2),
                        pe_ratio DECIMAL(8,2),
                        pb_ratio DECIMAL(8,2),
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create price_data table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS price_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        stock_code VARCHAR(20) NOT NULL,
                        date DATE NOT NULL,
                        open_price DECIMAL(10,3) NOT NULL,
                        high_price DECIMAL(10,3) NOT NULL,
                        low_price DECIMAL(10,3) NOT NULL,
                        close_price DECIMAL(10,3) NOT NULL,
                        volume BIGINT NOT NULL,
                        turnover DECIMAL(15,2),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(stock_code, date)
                    )
                ''')
                
                # Create analysis_history table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS analysis_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        stock_code VARCHAR(20) NOT NULL,
                        analysis_type VARCHAR(50) NOT NULL,
                        analysis_data TEXT NOT NULL,
                        signals_generated TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                ''')
                
                # Create user_watchlist table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_watchlist (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        stock_code VARCHAR(20) NOT NULL,
                        added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id),
                        UNIQUE(user_id, stock_code)
                    )
                ''')
                
                # Create screening_results table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS screening_results (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        screening_criteria TEXT NOT NULL,
                        results_data TEXT NOT NULL,
                        result_count INTEGER NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                ''')
                
                # Create indexes for better performance
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_price_data_stock_date ON price_data(stock_code, date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_history_user_stock ON analysis_history(user_id, stock_code)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_watchlist_user ON user_watchlist(user_id)')
                
                conn.commit()
                self.logger.info("Database initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Error initializing database: {e}")
            raise
    
    def save_stock_info(self, stock_info: Dict[str, Any]) -> bool:
        """
        Save or update stock basic information
        
        Args:
            stock_info: Stock information dictionary
            
        Returns:
            True if successful
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO stock_info 
                    (stock_code, stock_name, market, industry, market_cap, pe_ratio, pb_ratio, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    stock_info['stock_code'],
                    stock_info['stock_name'],
                    stock_info['market'],
                    stock_info.get('industry'),
                    stock_info.get('market_cap'),
                    stock_info.get('pe_ratio'),
                    stock_info.get('pb_ratio')
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Error saving stock info: {e}")
            return False
    
    def save_price_data(self, stock_code: str, price_data: List[Dict[str, Any]]) -> bool:
        """
        Save historical price data
        
        Args:
            stock_code: Stock code
            price_data: List of price data dictionaries
            
        Returns:
            True if successful
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for data in price_data:
                    cursor.execute('''
                        INSERT OR REPLACE INTO price_data 
                        (stock_code, date, open_price, high_price, low_price, close_price, volume, turnover)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        stock_code,
                        data['date'],
                        data['open'],
                        data['high'],
                        data['low'],
                        data['close'],
                        data['volume'],
                        data.get('turnover', 0)
                    ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Error saving price data: {e}")
            return False
    
    def get_price_data(self, stock_code: str, start_date: str = None, 
                      end_date: str = None, limit: int = 250) -> List[Dict[str, Any]]:
        """
        Get historical price data for a stock
        
        Args:
            stock_code: Stock code
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            limit: Maximum number of records
            
        Returns:
            List of price data dictionaries
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = '''
                    SELECT date, open_price, high_price, low_price, close_price, volume, turnover
                    FROM price_data 
                    WHERE stock_code = ?
                '''
                params = [stock_code]
                
                if start_date:
                    query += ' AND date >= ?'
                    params.append(start_date)
                
                if end_date:
                    query += ' AND date <= ?'
                    params.append(end_date)
                
                query += ' ORDER BY date DESC LIMIT ?'
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                return [
                    {
                        'date': row[0],
                        'open': float(row[1]),
                        'high': float(row[2]),
                        'low': float(row[3]),
                        'close': float(row[4]),
                        'volume': int(row[5]),
                        'turnover': float(row[6]) if row[6] else 0
                    }
                    for row in rows
                ]
                
        except Exception as e:
            self.logger.error(f"Error getting price data: {e}")
            return []
    
    def save_analysis_result(self, user_id: Optional[int], stock_code: str, 
                           analysis_type: str, analysis_data: Dict[str, Any], 
                           signals: List[Dict[str, Any]] = None) -> bool:
        """
        Save analysis result to history
        
        Args:
            user_id: User ID (None for anonymous)
            stock_code: Stock code
            analysis_type: Type of analysis
            analysis_data: Analysis data
            signals: Generated signals
            
        Returns:
            True if successful
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO analysis_history 
                    (user_id, stock_code, analysis_type, analysis_data, signals_generated)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    user_id,
                    stock_code,
                    analysis_type,
                    json.dumps(analysis_data),
                    json.dumps(signals) if signals else None
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Error saving analysis result: {e}")
            return False
    
    def get_analysis_history(self, user_id: Optional[int] = None, 
                           stock_code: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get analysis history
        
        Args:
            user_id: User ID filter
            stock_code: Stock code filter
            limit: Maximum number of records
            
        Returns:
            List of analysis history records
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                query = '''
                    SELECT id, user_id, stock_code, analysis_type, analysis_data, 
                           signals_generated, created_at
                    FROM analysis_history
                    WHERE 1=1
                '''
                params = []
                
                if user_id is not None:
                    query += ' AND user_id = ?'
                    params.append(user_id)
                
                if stock_code:
                    query += ' AND stock_code = ?'
                    params.append(stock_code)
                
                query += ' ORDER BY created_at DESC LIMIT ?'
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                return [
                    {
                        'id': row[0],
                        'user_id': row[1],
                        'stock_code': row[2],
                        'analysis_type': row[3],
                        'analysis_data': json.loads(row[4]) if row[4] else {},
                        'signals_generated': json.loads(row[5]) if row[5] else [],
                        'created_at': row[6]
                    }
                    for row in rows
                ]
                
        except Exception as e:
            self.logger.error(f"Error getting analysis history: {e}")
            return []
    
    def save_screening_result(self, user_id: Optional[int], criteria: Dict[str, Any], 
                            results: List[Dict[str, Any]]) -> bool:
        """
        Save stock screening result
        
        Args:
            user_id: User ID (None for anonymous)
            criteria: Screening criteria
            results: Screening results
            
        Returns:
            True if successful
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO screening_results 
                    (user_id, screening_criteria, results_data, result_count)
                    VALUES (?, ?, ?, ?)
                ''', (
                    user_id,
                    json.dumps(criteria),
                    json.dumps(results),
                    len(results)
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Error saving screening result: {e}")
            return False
    
    def get_popular_stocks(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get popular stocks based on analysis frequency
        
        Args:
            limit: Maximum number of stocks
            
        Returns:
            List of popular stock codes with analysis counts
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get stocks analyzed in the last 30 days
                thirty_days_ago = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                
                cursor.execute('''
                    SELECT ah.stock_code, si.stock_name, COUNT(*) as analysis_count
                    FROM analysis_history ah
                    LEFT JOIN stock_info si ON ah.stock_code = si.stock_code
                    WHERE ah.created_at >= ?
                    GROUP BY ah.stock_code
                    ORDER BY analysis_count DESC
                    LIMIT ?
                ''', (thirty_days_ago, limit))
                
                rows = cursor.fetchall()
                
                return [
                    {
                        'stock_code': row[0],
                        'stock_name': row[1] or 'Unknown',
                        'analysis_count': row[2]
                    }
                    for row in rows
                ]
                
        except Exception as e:
            self.logger.error(f"Error getting popular stocks: {e}")
            return []
    
    def cleanup_old_data(self, days_to_keep: int = 90):
        """
        Clean up old data to maintain database performance
        
        Args:
            days_to_keep: Number of days of data to keep
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).strftime('%Y-%m-%d')
                
                # Clean up old analysis history
                cursor.execute('''
                    DELETE FROM analysis_history 
                    WHERE created_at < ?
                ''', (cutoff_date,))
                
                # Clean up old screening results
                cursor.execute('''
                    DELETE FROM screening_results 
                    WHERE created_at < ?
                ''', (cutoff_date,))
                
                conn.commit()
                self.logger.info(f"Cleaned up data older than {cutoff_date}")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {e}")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get database statistics
        
        Returns:
            Dictionary with database statistics
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                stats = {}
                
                # Count records in each table
                tables = ['users', 'stock_info', 'price_data', 'analysis_history', 
                         'user_watchlist', 'screening_results']
                
                for table in tables:
                    cursor.execute(f'SELECT COUNT(*) FROM {table}')
                    stats[f'{table}_count'] = cursor.fetchone()[0]
                
                # Get database file size
                if os.path.exists(self.db_path):
                    stats['db_size_mb'] = round(os.path.getsize(self.db_path) / (1024 * 1024), 2)
                else:
                    stats['db_size_mb'] = 0
                
                return stats
                
        except Exception as e:
            self.logger.error(f"Error getting database stats: {e}")
            return {}