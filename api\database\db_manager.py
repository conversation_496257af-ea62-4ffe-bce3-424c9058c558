import psycopg2
import psycopg2.extras
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import json
import os

class DatabaseManager:
    """
    Database Manager for Trading Analysis Application
    
    Handles PostgreSQL database operations for user data, stock information,
    price data, and analysis history.
    """
    
    def __init__(self, host='localhost', port=5432, database='trading_db', user='postgres', password='password'):
        self.host = host
        self.port = port
        self.database = database
        self.user = user
        self.password = password
        self.connection_string = f"host={host} port={port} dbname={database} user={user} password={password}"
        self.logger = logging.getLogger(__name__)
        self._init_database()
    
    def get_connection(self):
        """Get database connection"""
        return psycopg2.connect(
            host=self.host,
            port=self.port,
            database=self.database,
            user=self.user,
            password=self.password
        )
    
    def _init_database(self):
        """
        Initialize database and create tables if they don't exist
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Create users table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        id SERIAL PRIMARY KEY,
                        email VARCHAR(255) UNIQUE NOT NULL,
                        password_hash VARCHAR(255) NOT NULL,
                        username VARCHAR(100) NOT NULL,
                        user_type VARCHAR(20) DEFAULT 'basic' CHECK (user_type IN ('basic', 'premium', 'professional')),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create stock_info table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS stock_info (
                        stock_code VARCHAR(20) PRIMARY KEY,
                        stock_name VARCHAR(100) NOT NULL,
                        market VARCHAR(10) NOT NULL,
                        industry VARCHAR(50),
                        market_cap DECIMAL(15,2),
                        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create price_data table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS price_data (
                        id SERIAL PRIMARY KEY,
                        stock_code VARCHAR(20) NOT NULL,
                        trade_date DATE NOT NULL,
                        open_price DECIMAL(10,3) NOT NULL,
                        high_price DECIMAL(10,3) NOT NULL,
                        low_price DECIMAL(10,3) NOT NULL,
                        close_price DECIMAL(10,3) NOT NULL,
                        volume BIGINT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (stock_code) REFERENCES stock_info(stock_code)
                    )
                ''')
                
                # Create analysis_history table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS analysis_history (
                        id SERIAL PRIMARY KEY,
                        user_id INTEGER,
                        stock_code VARCHAR(20) NOT NULL,
                        analysis_result TEXT,
                        signals TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id),
                        FOREIGN KEY (stock_code) REFERENCES stock_info(stock_code)
                    )
                ''')
                
                # Create screening_history table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS screening_history (
                        id SERIAL PRIMARY KEY,
                        user_id INTEGER,
                        filter_conditions TEXT,
                        screening_result TEXT,
                        result_count INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id)
                    )
                ''')
                
                # Create signal_record table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS signal_record (
                        id SERIAL PRIMARY KEY,
                        analysis_id INTEGER NOT NULL,
                        signal_type VARCHAR(10) NOT NULL CHECK (signal_type IN ('buy', 'sell', 'hold')),
                        signal_strength DECIMAL(3,2) NOT NULL,
                        signal_date DATE NOT NULL,
                        target_price DECIMAL(10,3),
                        status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'executed', 'expired')),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (analysis_id) REFERENCES analysis_history(id)
                    )
                ''')
                
                # Create indexes for better performance
                cursor.execute('CREATE UNIQUE INDEX IF NOT EXISTS idx_price_data_stock_date ON price_data(stock_code, trade_date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_price_data_trade_date ON price_data(trade_date DESC)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_history_user_id ON analysis_history(user_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_history_stock_code ON analysis_history(stock_code)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_history_created_at ON analysis_history(created_at DESC)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_screening_history_user_id ON screening_history(user_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_screening_history_created_at ON screening_history(created_at DESC)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_signal_record_analysis_id ON signal_record(analysis_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_signal_record_signal_date ON signal_record(signal_date DESC)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_signal_record_signal_type ON signal_record(signal_type)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_info_market ON stock_info(market)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_stock_info_industry ON stock_info(industry)')
                
                conn.commit()
                self.logger.info("Database initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Error initializing database: {e}")
            raise
    
    def save_stock_info(self, stock_info: Dict[str, Any]) -> bool:
        """
        Save or update stock basic information
        
        Args:
            stock_info: Stock information dictionary
            
        Returns:
            True if successful
        """
        try:
            with psycopg2.connect(self.connection_string) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO stock_info 
                    (stock_code, stock_name, market, industry, market_cap, last_updated)
                    VALUES (%s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
                    ON CONFLICT (stock_code) DO UPDATE SET
                        stock_name = EXCLUDED.stock_name,
                        market = EXCLUDED.market,
                        industry = EXCLUDED.industry,
                        market_cap = EXCLUDED.market_cap,
                        last_updated = CURRENT_TIMESTAMP
                ''', (
                    stock_info['stock_code'],
                    stock_info['stock_name'],
                    stock_info['market'],
                    stock_info.get('industry'),
                    stock_info.get('market_cap')
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Error saving stock info: {e}")
            return False
    
    def save_price_data(self, stock_code: str, price_data: List[Dict[str, Any]]) -> bool:
        """
        Save historical price data
        
        Args:
            stock_code: Stock code
            price_data: List of price data dictionaries
            
        Returns:
            True if successful
        """
        try:
            with psycopg2.connect(self.connection_string) as conn:
                cursor = conn.cursor()
                
                for data in price_data:
                    cursor.execute('''
                        INSERT INTO price_data 
                        (stock_code, trade_date, open_price, high_price, low_price, close_price, volume)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (stock_code, trade_date) DO UPDATE SET
                            open_price = EXCLUDED.open_price,
                            high_price = EXCLUDED.high_price,
                            low_price = EXCLUDED.low_price,
                            close_price = EXCLUDED.close_price,
                            volume = EXCLUDED.volume
                    ''', (
                        stock_code,
                        data['date'],
                        data['open'],
                        data['high'],
                        data['low'],
                        data['close'],
                        data['volume']
                    ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Error saving price data: {e}")
            return False
    
    def get_price_data(self, stock_code: str, start_date: str = None, 
                      end_date: str = None, limit: int = 250) -> List[Dict[str, Any]]:
        """
        Get historical price data for a stock
        
        Args:
            stock_code: Stock code
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            limit: Maximum number of records
            
        Returns:
            List of price data dictionaries
        """
        try:
            with psycopg2.connect(self.connection_string) as conn:
                cursor = conn.cursor(psycopg2.extras.RealDictCursor)
                
                query = '''
                    SELECT trade_date, open_price, high_price, low_price, close_price, volume
                    FROM price_data 
                    WHERE stock_code = %s
                '''
                params = [stock_code]
                
                if start_date:
                    query += ' AND trade_date >= %s'
                    params.append(start_date)
                
                if end_date:
                    query += ' AND trade_date <= %s'
                    params.append(end_date)
                
                query += ' ORDER BY trade_date DESC LIMIT %s'
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                return [
                    {
                        'date': str(row['trade_date']),
                        'open': float(row['open_price']),
                        'high': float(row['high_price']),
                        'low': float(row['low_price']),
                        'close': float(row['close_price']),
                        'volume': int(row['volume']),
                        'turnover': 0
                    }
                    for row in rows
                ]
                
        except Exception as e:
            self.logger.error(f"Error getting price data: {e}")
            return []
    
    def save_analysis_result(self, user_id: Optional[int], stock_code: str, 
                           analysis_type: str, analysis_data: Dict[str, Any], 
                           signals: List[Dict[str, Any]] = None) -> bool:
        """
        Save analysis result to history
        
        Args:
            user_id: User ID (None for anonymous)
            stock_code: Stock code
            analysis_type: Type of analysis
            analysis_data: Analysis data
            signals: Generated signals
            
        Returns:
            True if successful
        """
        try:
            with psycopg2.connect(self.connection_string) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO analysis_history 
                    (user_id, stock_code, analysis_result, signals)
                    VALUES (%s, %s, %s, %s)
                ''', (
                    user_id,
                    stock_code,
                    json.dumps(analysis_data),
                    json.dumps(signals) if signals else None
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Error saving analysis result: {e}")
            return False
    
    def get_analysis_history(self, user_id: Optional[int] = None, 
                           stock_code: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get analysis history
        
        Args:
            user_id: User ID filter
            stock_code: Stock code filter
            limit: Maximum number of records
            
        Returns:
            List of analysis history records
        """
        try:
            with psycopg2.connect(self.connection_string) as conn:
                cursor = conn.cursor(psycopg2.extras.RealDictCursor)
                
                query = '''
                    SELECT id, user_id, stock_code, analysis_result, 
                           signals, created_at
                    FROM analysis_history
                    WHERE 1=1
                '''
                params = []
                
                if user_id is not None:
                    query += ' AND user_id = %s'
                    params.append(user_id)
                
                if stock_code:
                    query += ' AND stock_code = %s'
                    params.append(stock_code)
                
                query += ' ORDER BY created_at DESC LIMIT %s'
                params.append(limit)
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                return [
                    {
                        'id': row['id'],
                        'user_id': row['user_id'],
                        'stock_code': row['stock_code'],
                        'analysis_data': json.loads(row['analysis_result']) if row['analysis_result'] else {},
                        'signals_generated': json.loads(row['signals']) if row['signals'] else [],
                        'created_at': str(row['created_at'])
                    }
                    for row in rows
                ]
                
        except Exception as e:
            self.logger.error(f"Error getting analysis history: {e}")
            return []
    
    def save_screening_result(self, user_id: Optional[int], criteria: Dict[str, Any], 
                            results: List[Dict[str, Any]]) -> bool:
        """
        Save stock screening result
        
        Args:
            user_id: User ID (None for anonymous)
            criteria: Screening criteria
            results: Screening results
            
        Returns:
            True if successful
        """
        try:
            with psycopg2.connect(self.connection_string) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO screening_history 
                    (user_id, filter_conditions, screening_result, result_count)
                    VALUES (%s, %s, %s, %s)
                ''', (
                    user_id,
                    json.dumps(criteria),
                    json.dumps(results),
                    len(results)
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Error saving screening result: {e}")
            return False
    
    def get_popular_stocks(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get popular stocks based on analysis frequency
        
        Args:
            limit: Maximum number of stocks
            
        Returns:
            List of popular stock codes with analysis counts
        """
        try:
            with psycopg2.connect(self.connection_string) as conn:
                cursor = conn.cursor(psycopg2.extras.RealDictCursor)
                
                # Get stocks analyzed in the last 30 days
                thirty_days_ago = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
                
                cursor.execute('''
                    SELECT ah.stock_code, si.stock_name, COUNT(*) as analysis_count
                    FROM analysis_history ah
                    LEFT JOIN stock_info si ON ah.stock_code = si.stock_code
                    WHERE ah.created_at >= %s
                    GROUP BY ah.stock_code, si.stock_name
                    ORDER BY analysis_count DESC
                    LIMIT %s
                ''', (thirty_days_ago, limit))
                
                rows = cursor.fetchall()
                
                return [
                    {
                        'stock_code': row['stock_code'],
                        'stock_name': row['stock_name'] or 'Unknown',
                        'analysis_count': row['analysis_count']
                    }
                    for row in rows
                ]
                
        except Exception as e:
            self.logger.error(f"Error getting popular stocks: {e}")
            return []
    
    def cleanup_old_data(self, days_to_keep: int = 90):
        """
        Clean up old data to maintain database performance
        
        Args:
            days_to_keep: Number of days of data to keep
        """
        try:
            with psycopg2.connect(self.connection_string) as conn:
                cursor = conn.cursor()
                
                cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).strftime('%Y-%m-%d')
                
                # Clean up old analysis history
                cursor.execute('''
                    DELETE FROM analysis_history 
                    WHERE created_at < %s
                ''', (cutoff_date,))
                
                # Clean up old screening results
                cursor.execute('''
                    DELETE FROM screening_history 
                    WHERE created_at < %s
                ''', (cutoff_date,))
                
                conn.commit()
                self.logger.info(f"Cleaned up data older than {cutoff_date}")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {e}")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get database statistics
        
        Returns:
            Dictionary with database statistics
        """
        try:
            with psycopg2.connect(self.connection_string) as conn:
                cursor = conn.cursor()
                
                stats = {}
                
                # Count records in each table
                tables = ['users', 'stock_info', 'price_data', 'analysis_history', 
                         'screening_history', 'signal_record']
                
                for table in tables:
                    cursor.execute(f'SELECT COUNT(*) FROM {table}')
                    stats[f'{table}_count'] = cursor.fetchone()[0]
                
                # Get database size
                cursor.execute("SELECT pg_size_pretty(pg_database_size(current_database()))")
                stats['db_size'] = cursor.fetchone()[0]
                
                return stats
                
        except Exception as e:
            self.logger.error(f"Error getting database stats: {e}")
            return {}