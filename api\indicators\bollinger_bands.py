import pandas as pd
import numpy as np

class BollingerBands:
    """Bollinger Bands Indicator Implementation"""
    
    def __init__(self, period=20, std_dev=2):
        self.period = period
        self.std_dev = std_dev
    
    def calculate(self, data):
        """Calculate Bollinger Bands
        
        Args:
            data: DataFrame with 'close' column
            
        Returns:
            dict: Bollinger Bands values and analysis
        """
        try:
            df = data.copy()
            
            # Calculate Simple Moving Average (Middle Band)
            sma = df['close'].rolling(window=self.period).mean()
            
            # Calculate Standard Deviation
            std = df['close'].rolling(window=self.period).std()
            
            # Calculate Upper and Lower Bands
            upper_band = sma + (std * self.std_dev)
            lower_band = sma - (std * self.std_dev)
            
            # Convert to lists
            upper_values = upper_band.fillna(method='bfill').tolist()
            middle_values = sma.fillna(method='bfill').tolist()
            lower_values = lower_band.fillna(method='bfill').tolist()
            close_values = df['close'].tolist()
            
            # Calculate Bollinger Band Width and %B
            band_width = ((upper_band - lower_band) / sma * 100).fillna(0).tolist()
            percent_b = ((df['close'] - lower_band) / (upper_band - lower_band) * 100).fillna(50).tolist()
            
            # Get current values
            current_close = close_values[-1] if close_values else 0
            current_upper = upper_values[-1] if upper_values else 0
            current_middle = middle_values[-1] if middle_values else 0
            current_lower = lower_values[-1] if lower_values else 0
            current_width = band_width[-1] if band_width else 0
            current_percent_b = percent_b[-1] if percent_b else 50
            
            # Generate signals
            signals = self._generate_signals(close_values, upper_values, middle_values, lower_values, percent_b)
            
            # Detect squeeze and expansion
            squeeze_expansion = self._detect_squeeze_expansion(band_width)
            
            return {
                'upper_band': upper_values,
                'middle_band': middle_values,
                'lower_band': lower_values,
                'band_width': band_width,
                'percent_b': percent_b,
                'current': {
                    'close': current_close,
                    'upper': current_upper,
                    'middle': current_middle,
                    'lower': current_lower,
                    'width': current_width,
                    'percent_b': current_percent_b
                },
                'signals': signals,
                'squeeze_expansion': squeeze_expansion,
                'interpretation': self._interpret_bollinger(current_close, current_upper, current_middle, current_lower, current_percent_b)
            }
            
        except Exception as e:
            return {'error': f'Bollinger Bands calculation failed: {str(e)}'}
    
    def _generate_signals(self, close_values, upper_values, middle_values, lower_values, percent_b):
        """Generate trading signals based on Bollinger Bands"""
        signals = []
        
        if len(close_values) < 2:
            return signals
        
        current_close = close_values[-1]
        prev_close = close_values[-2]
        current_upper = upper_values[-1]
        current_middle = middle_values[-1]
        current_lower = lower_values[-1]
        current_percent_b = percent_b[-1]
        prev_percent_b = percent_b[-2] if len(percent_b) > 1 else 50
        
        # Bollinger Band Bounce (from lower band)
        if prev_close <= lower_values[-2] and current_close > current_lower:
            signals.append({
                'type': 'buy',
                'signal': 'Bollinger Band Bounce',
                'strength': 'strong',
                'description': '价格从下轨反弹，买入信号'
            })
        
        # Bollinger Band Rejection (from upper band)
        if prev_close >= upper_values[-2] and current_close < current_upper:
            signals.append({
                'type': 'sell',
                'signal': 'Bollinger Band Rejection',
                'strength': 'strong',
                'description': '价格从上轨回落，卖出信号'
            })
        
        # Middle Band Cross (bullish)
        if prev_close <= middle_values[-2] and current_close > current_middle:
            signals.append({
                'type': 'buy',
                'signal': 'Middle Band Breakout',
                'strength': 'medium',
                'description': '价格突破中轨，多头信号'
            })
        
        # Middle Band Cross (bearish)
        if prev_close >= middle_values[-2] and current_close < current_middle:
            signals.append({
                'type': 'sell',
                'signal': 'Middle Band Breakdown',
                'strength': 'medium',
                'description': '价格跌破中轨，空头信号'
            })
        
        # %B signals
        if prev_percent_b <= 20 and current_percent_b > 20:
            signals.append({
                'type': 'buy',
                'signal': 'Oversold Recovery',
                'strength': 'medium',
                'description': '%B从超卖区域恢复'
            })
        
        if prev_percent_b >= 80 and current_percent_b < 80:
            signals.append({
                'type': 'sell',
                'signal': 'Overbought Pullback',
                'strength': 'medium',
                'description': '%B从超买区域回落'
            })
        
        # Band squeeze breakout
        if len(upper_values) >= 20:
            recent_width = [(upper_values[i] - lower_values[i]) / middle_values[i] * 100 
                           for i in range(-20, 0) if middle_values[i] != 0]
            if recent_width:
                avg_width = sum(recent_width) / len(recent_width)
                current_width = (current_upper - current_lower) / current_middle * 100
                
                if current_width < avg_width * 0.7:  # Squeeze condition
                    if current_close > current_upper:
                        signals.append({
                            'type': 'buy',
                            'signal': 'Squeeze Breakout (Bullish)',
                            'strength': 'very_strong',
                            'description': '挤压突破向上，强烈买入信号'
                        })
                    elif current_close < current_lower:
                        signals.append({
                            'type': 'sell',
                            'signal': 'Squeeze Breakout (Bearish)',
                            'strength': 'very_strong',
                            'description': '挤压突破向下，强烈卖出信号'
                        })
        
        return signals
    
    def _detect_squeeze_expansion(self, band_width, lookback=20):
        """Detect Bollinger Band squeeze and expansion"""
        if len(band_width) < lookback:
            return {'status': 'insufficient_data'}
        
        recent_width = band_width[-lookback:]
        current_width = band_width[-1]
        avg_width = sum(recent_width) / len(recent_width)
        min_width = min(recent_width)
        max_width = max(recent_width)
        
        # Determine current state
        if current_width < avg_width * 0.7:
            status = 'squeeze'
            description = '布林带收窄，波动性降低，准备突破'
        elif current_width > avg_width * 1.3:
            status = 'expansion'
            description = '布林带扩张，波动性增加，趋势强劲'
        else:
            status = 'normal'
            description = '布林带正常状态'
        
        # Calculate squeeze intensity
        squeeze_intensity = (avg_width - current_width) / avg_width * 100 if avg_width > 0 else 0
        
        return {
            'status': status,
            'description': description,
            'current_width': current_width,
            'average_width': avg_width,
            'squeeze_intensity': max(0, squeeze_intensity),
            'width_percentile': (current_width - min_width) / (max_width - min_width) * 100 if max_width > min_width else 50
        }
    
    def _interpret_bollinger(self, close, upper, middle, lower, percent_b):
        """Interpret Bollinger Bands position"""
        if close > upper:
            return {
                'position': 'above_upper_band',
                'action': 'consider_sell',
                'description': f'价格突破上轨 ({close:.2f} > {upper:.2f})，可能超买',
                'risk_level': 'high',
                'percent_b_interpretation': self._interpret_percent_b(percent_b)
            }
        elif close < lower:
            return {
                'position': 'below_lower_band',
                'action': 'consider_buy',
                'description': f'价格跌破下轨 ({close:.2f} < {lower:.2f})，可能超卖',
                'risk_level': 'high',
                'percent_b_interpretation': self._interpret_percent_b(percent_b)
            }
        elif close > middle:
            return {
                'position': 'above_middle_band',
                'action': 'bullish_bias',
                'description': f'价格在中轨上方 ({close:.2f} > {middle:.2f})，偏多头',
                'risk_level': 'medium',
                'percent_b_interpretation': self._interpret_percent_b(percent_b)
            }
        else:
            return {
                'position': 'below_middle_band',
                'action': 'bearish_bias',
                'description': f'价格在中轨下方 ({close:.2f} < {middle:.2f})，偏空头',
                'risk_level': 'medium',
                'percent_b_interpretation': self._interpret_percent_b(percent_b)
            }
    
    def _interpret_percent_b(self, percent_b):
        """Interpret %B value"""
        if percent_b > 100:
            return f'%B = {percent_b:.1f} - 价格在上轨之上，强烈超买'
        elif percent_b > 80:
            return f'%B = {percent_b:.1f} - 接近上轨，可能超买'
        elif percent_b < 0:
            return f'%B = {percent_b:.1f} - 价格在下轨之下，强烈超卖'
        elif percent_b < 20:
            return f'%B = {percent_b:.1f} - 接近下轨，可能超卖'
        else:
            return f'%B = {percent_b:.1f} - 在布林带中间区域'