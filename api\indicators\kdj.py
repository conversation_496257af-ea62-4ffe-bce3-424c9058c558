import pandas as pd
import numpy as np

class KDJ:
    """KDJ (Stochastic) Indicator Implementation"""
    
    def __init__(self, k_period=9, d_period=3, j_period=3):
        self.k_period = k_period
        self.d_period = d_period
        self.j_period = j_period
    
    def calculate(self, data):
        """Calculate KDJ indicator
        
        Args:
            data: DataFrame with columns ['high', 'low', 'close']
            
        Returns:
            dict: KDJ values with K, D, J lines
        """
        try:
            df = data.copy()
            
            # Calculate RSV (Raw Stochastic Value)
            low_min = df['low'].rolling(window=self.k_period).min()
            high_max = df['high'].rolling(window=self.k_period).max()
            
            rsv = (df['close'] - low_min) / (high_max - low_min) * 100
            rsv = rsv.fillna(50)  # Fill NaN with 50
            
            # Calculate K, D, J
            k_values = []
            d_values = []
            j_values = []
            
            k_prev = 50  # Initial K value
            d_prev = 50  # Initial D value
            
            for i, rsv_val in enumerate(rsv):
                if pd.isna(rsv_val):
                    rsv_val = 50
                
                # K = (2/3) * K_prev + (1/3) * RSV
                k_curr = (2/3) * k_prev + (1/3) * rsv_val
                k_values.append(k_curr)
                
                # D = (2/3) * D_prev + (1/3) * K
                d_curr = (2/3) * d_prev + (1/3) * k_curr
                d_values.append(d_curr)
                
                # J = 3 * K - 2 * D
                j_curr = 3 * k_curr - 2 * d_curr
                j_values.append(j_curr)
                
                k_prev = k_curr
                d_prev = d_curr
            
            # Get recent values for signals
            latest_k = k_values[-1] if k_values else 50
            latest_d = d_values[-1] if d_values else 50
            latest_j = j_values[-1] if j_values else 50
            
            # Generate signals
            signals = self._generate_signals(k_values, d_values, j_values)
            
            return {
                'k': k_values,
                'd': d_values,
                'j': j_values,
                'current': {
                    'k': latest_k,
                    'd': latest_d,
                    'j': latest_j
                },
                'signals': signals,
                'interpretation': self._interpret_kdj(latest_k, latest_d, latest_j)
            }
            
        except Exception as e:
            return {'error': f'KDJ calculation failed: {str(e)}'}
    
    def _generate_signals(self, k_values, d_values, j_values):
        """Generate trading signals based on KDJ"""
        signals = []
        
        if len(k_values) < 2 or len(d_values) < 2:
            return signals
        
        latest_k = k_values[-1]
        latest_d = d_values[-1]
        latest_j = j_values[-1]
        prev_k = k_values[-2]
        prev_d = d_values[-2]
        
        # Golden Cross: K line crosses above D line
        if prev_k <= prev_d and latest_k > latest_d and latest_k < 80:
            signals.append({
                'type': 'buy',
                'signal': 'KDJ Golden Cross',
                'strength': 'medium',
                'description': 'K线上穿D线，买入信号'
            })
        
        # Death Cross: K line crosses below D line
        if prev_k >= prev_d and latest_k < latest_d and latest_k > 20:
            signals.append({
                'type': 'sell',
                'signal': 'KDJ Death Cross',
                'strength': 'medium',
                'description': 'K线下穿D线，卖出信号'
            })
        
        # Oversold condition
        if latest_k < 20 and latest_d < 20:
            signals.append({
                'type': 'buy',
                'signal': 'KDJ Oversold',
                'strength': 'strong',
                'description': 'KDJ超卖，反弹信号'
            })
        
        # Overbought condition
        if latest_k > 80 and latest_d > 80:
            signals.append({
                'type': 'sell',
                'signal': 'KDJ Overbought',
                'strength': 'strong',
                'description': 'KDJ超买，回调信号'
            })
        
        return signals
    
    def _interpret_kdj(self, k, d, j):
        """Interpret KDJ values"""
        if k > 80 and d > 80:
            return {
                'trend': 'overbought',
                'action': 'consider_sell',
                'description': '超买区域，考虑卖出'
            }
        elif k < 20 and d < 20:
            return {
                'trend': 'oversold',
                'action': 'consider_buy',
                'description': '超卖区域，考虑买入'
            }
        elif k > d:
            return {
                'trend': 'bullish',
                'action': 'hold_or_buy',
                'description': '多头趋势，持有或买入'
            }
        else:
            return {
                'trend': 'bearish',
                'action': 'hold_or_sell',
                'description': '空头趋势，持有或卖出'
            }