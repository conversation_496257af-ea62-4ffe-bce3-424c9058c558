import pandas as pd
import numpy as np
from typing import List, Dict, Any, Tuple
from scipy.signal import find_peaks

class MACD:
    """
    MACD (Moving Average Convergence Divergence) with Divergence Detection
    
    This class calculates MACD indicator and detects top/bottom divergences
    which are key signals for potential trend reversals.
    
    MACD Components:
    - MACD Line: 12-period EMA - 26-period EMA
    - Signal Line: 9-period EMA of MACD Line
    - Histogram: MACD Line - Signal Line
    
    Divergence Types:
    - Bullish Divergence: Price makes lower lows, MACD makes higher lows
    - Bearish Divergence: Price makes higher highs, MACD makes lower highs
    """
    
    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        self.min_periods_for_divergence = 20
    
    def calculate(self, price_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate MACD indicator with divergence detection
        
        Args:
            price_data: List of price data dictionaries with 'close', 'high', 'low', 'date' keys
            
        Returns:
            Dictionary containing MACD data and divergence signals
        """
        if len(price_data) < self.slow_period + self.signal_period:
            return {
                'data': [],
                'divergences': [],
                'current_macd': 0,
                'current_signal': 0,
                'current_histogram': 0,
                'trend': 'neutral'
            }
        
        df = pd.DataFrame(price_data)
        df['close'] = pd.to_numeric(df['close'])
        df['high'] = pd.to_numeric(df['high'])
        df['low'] = pd.to_numeric(df['low'])
        
        # Calculate EMAs
        df['ema_fast'] = df['close'].ewm(span=self.fast_period).mean()
        df['ema_slow'] = df['close'].ewm(span=self.slow_period).mean()
        
        # Calculate MACD components
        df['macd'] = df['ema_fast'] - df['ema_slow']
        df['signal'] = df['macd'].ewm(span=self.signal_period).mean()
        df['histogram'] = df['macd'] - df['signal']
        
        # Detect divergences
        divergences = self._detect_divergences(df)
        
        # Prepare result data
        result_data = []
        for i, row in df.iterrows():
            if i >= self.slow_period + self.signal_period - 1:
                result_data.append({
                    'date': row['date'],
                    'close': float(row['close']),
                    'macd': float(row['macd']),
                    'signal': float(row['signal']),
                    'histogram': float(row['histogram'])
                })
        
        # Get current values
        current_macd = float(df['macd'].iloc[-1])
        current_signal = float(df['signal'].iloc[-1])
        current_histogram = float(df['histogram'].iloc[-1])
        
        # Determine trend
        trend = self._determine_trend(df)
        
        return {
            'data': result_data,
            'divergences': divergences,
            'current_macd': current_macd,
            'current_signal': current_signal,
            'current_histogram': current_histogram,
            'trend': trend
        }
    
    def _detect_divergences(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Detect bullish and bearish divergences between price and MACD
        
        Args:
            df: DataFrame with price and MACD data
            
        Returns:
            List of divergence signals
        """
        if len(df) < self.min_periods_for_divergence:
            return []
        
        divergences = []
        
        # Find peaks and troughs in price and MACD
        price_peaks, price_troughs = self._find_peaks_and_troughs(df['close'])
        macd_peaks, macd_troughs = self._find_peaks_and_troughs(df['macd'])
        
        # Detect bearish divergences (price higher highs, MACD lower highs)
        bearish_divergences = self._detect_bearish_divergences(
            df, price_peaks, macd_peaks
        )
        divergences.extend(bearish_divergences)
        
        # Detect bullish divergences (price lower lows, MACD higher lows)
        bullish_divergences = self._detect_bullish_divergences(
            df, price_troughs, macd_troughs
        )
        divergences.extend(bullish_divergences)
        
        return sorted(divergences, key=lambda x: x['date'])
    
    def _find_peaks_and_troughs(self, series: pd.Series, min_distance: int = 5) -> Tuple[List[int], List[int]]:
        """
        Find peaks and troughs in a time series
        
        Args:
            series: Time series data
            min_distance: Minimum distance between peaks/troughs
            
        Returns:
            Tuple of (peaks_indices, troughs_indices)
        """
        # Find peaks
        peaks, _ = find_peaks(series, distance=min_distance)
        
        # Find troughs (peaks in inverted series)
        troughs, _ = find_peaks(-series, distance=min_distance)
        
        return peaks.tolist(), troughs.tolist()
    
    def _detect_bearish_divergences(self, df: pd.DataFrame, price_peaks: List[int], macd_peaks: List[int]) -> List[Dict[str, Any]]:
        """
        Detect bearish divergences (price higher highs, MACD lower highs)
        
        Args:
            df: DataFrame with price and MACD data
            price_peaks: Indices of price peaks
            macd_peaks: Indices of MACD peaks
            
        Returns:
            List of bearish divergence signals
        """
        divergences = []
        
        if len(price_peaks) < 2 or len(macd_peaks) < 2:
            return divergences
        
        # Look for recent peaks within a reasonable window
        for i in range(1, len(price_peaks)):
            price_peak1_idx = price_peaks[i-1]
            price_peak2_idx = price_peaks[i]
            
            # Find corresponding MACD peaks
            macd_peak1_idx = self._find_nearest_peak(price_peak1_idx, macd_peaks, window=10)
            macd_peak2_idx = self._find_nearest_peak(price_peak2_idx, macd_peaks, window=10)
            
            if macd_peak1_idx is None or macd_peak2_idx is None:
                continue
            
            price1 = df['close'].iloc[price_peak1_idx]
            price2 = df['close'].iloc[price_peak2_idx]
            macd1 = df['macd'].iloc[macd_peak1_idx]
            macd2 = df['macd'].iloc[macd_peak2_idx]
            
            # Check for bearish divergence: price higher high, MACD lower high
            if price2 > price1 and macd2 < macd1:
                strength = self._calculate_divergence_strength(price1, price2, macd1, macd2)
                
                divergences.append({
                    'type': 'bearish',
                    'date': df['date'].iloc[price_peak2_idx],
                    'price': float(price2),
                    'macd': float(macd2),
                    'strength': strength,
                    'description': f'Bearish divergence: Price higher high ({price2:.2f} > {price1:.2f}), MACD lower high ({macd2:.4f} < {macd1:.4f})'
                })
        
        return divergences
    
    def _detect_bullish_divergences(self, df: pd.DataFrame, price_troughs: List[int], macd_troughs: List[int]) -> List[Dict[str, Any]]:
        """
        Detect bullish divergences (price lower lows, MACD higher lows)
        
        Args:
            df: DataFrame with price and MACD data
            price_troughs: Indices of price troughs
            macd_troughs: Indices of MACD troughs
            
        Returns:
            List of bullish divergence signals
        """
        divergences = []
        
        if len(price_troughs) < 2 or len(macd_troughs) < 2:
            return divergences
        
        # Look for recent troughs within a reasonable window
        for i in range(1, len(price_troughs)):
            price_trough1_idx = price_troughs[i-1]
            price_trough2_idx = price_troughs[i]
            
            # Find corresponding MACD troughs
            macd_trough1_idx = self._find_nearest_peak(price_trough1_idx, macd_troughs, window=10)
            macd_trough2_idx = self._find_nearest_peak(price_trough2_idx, macd_troughs, window=10)
            
            if macd_trough1_idx is None or macd_trough2_idx is None:
                continue
            
            price1 = df['close'].iloc[price_trough1_idx]
            price2 = df['close'].iloc[price_trough2_idx]
            macd1 = df['macd'].iloc[macd_trough1_idx]
            macd2 = df['macd'].iloc[macd_trough2_idx]
            
            # Check for bullish divergence: price lower low, MACD higher low
            if price2 < price1 and macd2 > macd1:
                strength = self._calculate_divergence_strength(price1, price2, macd1, macd2)
                
                divergences.append({
                    'type': 'bullish',
                    'date': df['date'].iloc[price_trough2_idx],
                    'price': float(price2),
                    'macd': float(macd2),
                    'strength': strength,
                    'description': f'Bullish divergence: Price lower low ({price2:.2f} < {price1:.2f}), MACD higher low ({macd2:.4f} > {macd1:.4f})'
                })
        
        return divergences
    
    def _find_nearest_peak(self, target_idx: int, peaks: List[int], window: int = 10) -> int:
        """
        Find the nearest peak to a target index within a window
        
        Args:
            target_idx: Target index
            peaks: List of peak indices
            window: Search window size
            
        Returns:
            Index of nearest peak or None if not found
        """
        nearest_peak = None
        min_distance = float('inf')
        
        for peak_idx in peaks:
            distance = abs(peak_idx - target_idx)
            if distance <= window and distance < min_distance:
                min_distance = distance
                nearest_peak = peak_idx
        
        return nearest_peak
    
    def _calculate_divergence_strength(self, price1: float, price2: float, macd1: float, macd2: float) -> int:
        """
        Calculate divergence strength based on the magnitude of price and MACD differences
        
        Args:
            price1, price2: Price values at two points
            macd1, macd2: MACD values at two points
            
        Returns:
            Strength score from 1 to 5
        """
        price_change_pct = abs((price2 - price1) / price1) * 100
        macd_change_pct = abs((macd2 - macd1) / abs(macd1)) * 100 if macd1 != 0 else 0
        
        # Calculate strength based on the magnitude of divergence
        if price_change_pct > 5 and macd_change_pct > 20:
            return 5  # Very strong
        elif price_change_pct > 3 and macd_change_pct > 15:
            return 4  # Strong
        elif price_change_pct > 2 and macd_change_pct > 10:
            return 3  # Moderate
        elif price_change_pct > 1 and macd_change_pct > 5:
            return 2  # Weak
        else:
            return 1  # Very weak
    
    def _determine_trend(self, df: pd.DataFrame) -> str:
        """
        Determine current MACD trend
        
        Args:
            df: DataFrame with MACD data
            
        Returns:
            Trend string: 'bullish', 'bearish', or 'neutral'
        """
        if len(df) < 10:
            return 'neutral'
        
        recent_macd = df['macd'].tail(5)
        recent_signal = df['signal'].tail(5)
        recent_histogram = df['histogram'].tail(5)
        
        # Check MACD line vs Signal line
        macd_above_signal = recent_macd.iloc[-1] > recent_signal.iloc[-1]
        
        # Check histogram trend
        histogram_increasing = recent_histogram.iloc[-1] > recent_histogram.iloc[-3]
        
        # Check MACD line trend
        macd_increasing = recent_macd.iloc[-1] > recent_macd.iloc[-3]
        
        if macd_above_signal and histogram_increasing and macd_increasing:
            return 'bullish'
        elif not macd_above_signal and not histogram_increasing and not macd_increasing:
            return 'bearish'
        else:
            return 'neutral'