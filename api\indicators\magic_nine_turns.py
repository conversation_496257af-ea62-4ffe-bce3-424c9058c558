import pandas as pd
import numpy as np
from typing import List, Dict, Any

class MagicNineTurns:
    """
    Magic Nine Turns (神奇九转) Technical Indicator
    
    This indicator identifies potential reversal points by counting consecutive
    periods where the closing price is higher/lower than the closing price
    from 4 periods ago.
    
    Rules:
    - Count up when close > close[4 periods ago]
    - Count down when close < close[4 periods ago]
    - Reset count when direction changes
    - Signal generated at count 9 (potential reversal)
    - Enhanced signals at count 13+ (stronger reversal)
    """
    
    def __init__(self):
        self.lookback_period = 4
        self.signal_threshold = 9
        self.strong_signal_threshold = 13
    
    def calculate(self, price_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate Magic Nine Turns indicator
        
        Args:
            price_data: List of price data dictionaries with 'close', 'date' keys
            
        Returns:
            Dictionary containing Magic Nine Turns data
        """
        if len(price_data) < self.lookback_period + 1:
            return {
                'counts': [],
                'signals': [],
                'current_count': 0,
                'current_direction': None,
                'last_signal': None
            }
        
        df = pd.DataFrame(price_data)
        df['close'] = pd.to_numeric(df['close'])
        
        # Calculate the comparison with 4 periods ago
        df['close_4_ago'] = df['close'].shift(self.lookback_period)
        df['direction'] = np.where(
            df['close'] > df['close_4_ago'], 1,
            np.where(df['close'] < df['close_4_ago'], -1, 0)
        )
        
        # Calculate consecutive counts
        counts = []
        signals = []
        current_count = 0
        current_direction = 0
        
        for i, row in df.iterrows():
            if i < self.lookback_period:
                counts.append(0)
                signals.append(None)
                continue
            
            direction = row['direction']
            
            # Reset count if direction changes or is neutral
            if direction != current_direction or direction == 0:
                current_count = 1 if direction != 0 else 0
                current_direction = direction
            else:
                # Continue counting in the same direction
                current_count += 1
            
            counts.append(current_count * current_direction)
            
            # Generate signals
            signal = self._generate_signal(current_count, current_direction, row)
            signals.append(signal)
        
        # Add calculated data to dataframe
        df['magic_nine_count'] = counts
        df['magic_nine_signal'] = signals
        
        # Prepare result
        result_data = []
        for i, row in df.iterrows():
            if i >= self.lookback_period:
                result_data.append({
                    'date': row['date'],
                    'close': float(row['close']),
                    'count': int(row['magic_nine_count']),
                    'signal': row['magic_nine_signal']
                })
        
        # Get current state
        current_count = counts[-1] if counts else 0
        current_direction = 'up' if current_count > 0 else 'down' if current_count < 0 else None
        
        # Find last signal
        last_signal = None
        for signal in reversed(signals):
            if signal:
                last_signal = signal
                break
        
        return {
            'data': result_data,
            'current_count': abs(current_count),
            'current_direction': current_direction,
            'last_signal': last_signal,
            'signal_threshold': self.signal_threshold,
            'strong_signal_threshold': self.strong_signal_threshold
        }
    
    def _generate_signal(self, count: int, direction: int, price_row: pd.Series) -> Dict[str, Any]:
        """
        Generate trading signal based on Magic Nine Turns count
        
        Args:
            count: Current consecutive count
            direction: Current direction (1 for up, -1 for down)
            price_row: Current price data row
            
        Returns:
            Signal dictionary or None
        """
        if count < self.signal_threshold:
            return None
        
        signal_type = None
        signal_strength = 0
        
        if direction == 1 and count >= self.signal_threshold:
            # Uptrend exhaustion - potential sell signal
            signal_type = 'sell'
            signal_strength = min(count - self.signal_threshold + 1, 5)
            
        elif direction == -1 and count >= self.signal_threshold:
            # Downtrend exhaustion - potential buy signal
            signal_type = 'buy'
            signal_strength = min(count - self.signal_threshold + 1, 5)
        
        if signal_type:
            # Enhanced signal strength for counts >= 13
            if count >= self.strong_signal_threshold:
                signal_strength = min(signal_strength + 2, 5)
            
            return {
                'type': signal_type,
                'strength': signal_strength,
                'count': count,
                'price': float(price_row['close']),
                'date': price_row['date'],
                'description': f'Magic Nine Turns {signal_type.upper()} signal (count: {count})'
            }
        
        return None
    
    def get_signal_description(self, count: int, direction: str) -> str:
        """
        Get human-readable description of current Magic Nine Turns state
        
        Args:
            count: Current count
            direction: Current direction ('up' or 'down')
            
        Returns:
            Description string
        """
        if count == 0:
            return "No active Magic Nine Turns pattern"
        
        direction_text = "上涨" if direction == 'up' else "下跌"
        
        if count < self.signal_threshold:
            return f"连续{count}个周期{direction_text}，距离信号还需{self.signal_threshold - count}个周期"
        elif count == self.signal_threshold:
            signal_type = "卖出" if direction == 'up' else "买入"
            return f"神奇九转{signal_type}信号！连续{count}个周期{direction_text}"
        elif count < self.strong_signal_threshold:
            signal_type = "卖出" if direction == 'up' else "买入"
            return f"强化{signal_type}信号！连续{count}个周期{direction_text}"
        else:
            signal_type = "卖出" if direction == 'up' else "买入"
            return f"极强{signal_type}信号！连续{count}个周期{direction_text}，反转概率很高"
    
    def validate_signal(self, signal_data: Dict[str, Any], price_data: List[Dict[str, Any]]) -> bool:
        """
        Validate Magic Nine Turns signal with additional criteria
        
        Args:
            signal_data: Signal data from calculate method
            price_data: Recent price data for validation
            
        Returns:
            True if signal is valid, False otherwise
        """
        if not signal_data.get('last_signal'):
            return False
        
        last_signal = signal_data['last_signal']
        
        # Basic validation: signal strength should be at least 1
        if last_signal.get('strength', 0) < 1:
            return False
        
        # Additional validation can be added here:
        # - Volume confirmation
        # - Price action confirmation
        # - Market condition filters
        
        return True