import pandas as pd
import numpy as np

class RSI:
    """Relative Strength Index (RSI) Indicator Implementation"""
    
    def __init__(self, period=14):
        self.period = period
    
    def calculate(self, data):
        """Calculate RSI indicator
        
        Args:
            data: DataFrame with 'close' column
            
        Returns:
            dict: RSI values and analysis
        """
        try:
            df = data.copy()
            
            # Calculate price changes
            delta = df['close'].diff()
            
            # Separate gains and losses
            gains = delta.where(delta > 0, 0)
            losses = -delta.where(delta < 0, 0)
            
            # Calculate average gains and losses using exponential moving average
            avg_gains = gains.ewm(span=self.period, adjust=False).mean()
            avg_losses = losses.ewm(span=self.period, adjust=False).mean()
            
            # Calculate RS (Relative Strength)
            rs = avg_gains / avg_losses
            
            # Calculate RSI
            rsi = 100 - (100 / (1 + rs))
            rsi_values = rsi.fillna(50).tolist()
            
            # Get current RSI value
            current_rsi = rsi_values[-1] if rsi_values else 50
            
            # Generate signals
            signals = self._generate_signals(rsi_values)
            
            # Calculate RSI divergence
            divergence = self._check_divergence(df['close'].tolist(), rsi_values)
            
            return {
                'values': rsi_values,
                'current': current_rsi,
                'signals': signals,
                'divergence': divergence,
                'interpretation': self._interpret_rsi(current_rsi),
                'support_resistance': self._find_support_resistance(rsi_values)
            }
            
        except Exception as e:
            return {'error': f'RSI calculation failed: {str(e)}'}
    
    def _generate_signals(self, rsi_values):
        """Generate trading signals based on RSI"""
        signals = []
        
        if len(rsi_values) < 2:
            return signals
        
        current_rsi = rsi_values[-1]
        prev_rsi = rsi_values[-2]
        
        # Oversold bounce signal
        if prev_rsi <= 30 and current_rsi > 30:
            signals.append({
                'type': 'buy',
                'signal': 'RSI Oversold Bounce',
                'strength': 'strong',
                'description': 'RSI从超卖区域反弹，买入信号'
            })
        
        # Overbought pullback signal
        if prev_rsi >= 70 and current_rsi < 70:
            signals.append({
                'type': 'sell',
                'signal': 'RSI Overbought Pullback',
                'strength': 'strong',
                'description': 'RSI从超买区域回落，卖出信号'
            })
        
        # Extreme oversold (potential reversal)
        if current_rsi < 20:
            signals.append({
                'type': 'buy',
                'signal': 'RSI Extreme Oversold',
                'strength': 'very_strong',
                'description': 'RSI极度超卖，强烈反转信号'
            })
        
        # Extreme overbought (potential reversal)
        if current_rsi > 80:
            signals.append({
                'type': 'sell',
                'signal': 'RSI Extreme Overbought',
                'strength': 'very_strong',
                'description': 'RSI极度超买，强烈回调信号'
            })
        
        # RSI centerline cross
        if prev_rsi <= 50 and current_rsi > 50:
            signals.append({
                'type': 'buy',
                'signal': 'RSI Bullish Centerline Cross',
                'strength': 'medium',
                'description': 'RSI突破中线，多头信号'
            })
        elif prev_rsi >= 50 and current_rsi < 50:
            signals.append({
                'type': 'sell',
                'signal': 'RSI Bearish Centerline Cross',
                'strength': 'medium',
                'description': 'RSI跌破中线，空头信号'
            })
        
        return signals
    
    def _check_divergence(self, prices, rsi_values, lookback=20):
        """Check for RSI divergence with price"""
        if len(prices) < lookback or len(rsi_values) < lookback:
            return {'type': 'none', 'description': '数据不足以检测背离'}
        
        recent_prices = prices[-lookback:]
        recent_rsi = rsi_values[-lookback:]
        
        # Find peaks and troughs
        price_peaks = self._find_peaks(recent_prices)
        rsi_peaks = self._find_peaks(recent_rsi)
        price_troughs = self._find_troughs(recent_prices)
        rsi_troughs = self._find_troughs(recent_rsi)
        
        # Check for bullish divergence (price makes lower lows, RSI makes higher lows)
        if len(price_troughs) >= 2 and len(rsi_troughs) >= 2:
            if (recent_prices[price_troughs[-1]] < recent_prices[price_troughs[-2]] and
                recent_rsi[rsi_troughs[-1]] > recent_rsi[rsi_troughs[-2]]):
                return {
                    'type': 'bullish',
                    'description': '看涨背离：价格创新低但RSI未创新低',
                    'strength': 'strong'
                }
        
        # Check for bearish divergence (price makes higher highs, RSI makes lower highs)
        if len(price_peaks) >= 2 and len(rsi_peaks) >= 2:
            if (recent_prices[price_peaks[-1]] > recent_prices[price_peaks[-2]] and
                recent_rsi[rsi_peaks[-1]] < recent_rsi[rsi_peaks[-2]]):
                return {
                    'type': 'bearish',
                    'description': '看跌背离：价格创新高但RSI未创新高',
                    'strength': 'strong'
                }
        
        return {'type': 'none', 'description': '未检测到明显背离'}
    
    def _find_peaks(self, data, min_distance=3):
        """Find peaks in data"""
        peaks = []
        for i in range(min_distance, len(data) - min_distance):
            if all(data[i] > data[i-j] for j in range(1, min_distance+1)) and \
               all(data[i] > data[i+j] for j in range(1, min_distance+1)):
                peaks.append(i)
        return peaks
    
    def _find_troughs(self, data, min_distance=3):
        """Find troughs in data"""
        troughs = []
        for i in range(min_distance, len(data) - min_distance):
            if all(data[i] < data[i-j] for j in range(1, min_distance+1)) and \
               all(data[i] < data[i+j] for j in range(1, min_distance+1)):
                troughs.append(i)
        return troughs
    
    def _interpret_rsi(self, rsi_value):
        """Interpret RSI value"""
        if rsi_value >= 70:
            return {
                'condition': 'overbought',
                'action': 'consider_sell',
                'description': f'RSI {rsi_value:.1f} - 超买区域，考虑卖出',
                'risk_level': 'high'
            }
        elif rsi_value <= 30:
            return {
                'condition': 'oversold',
                'action': 'consider_buy',
                'description': f'RSI {rsi_value:.1f} - 超卖区域，考虑买入',
                'risk_level': 'low'
            }
        elif rsi_value > 50:
            return {
                'condition': 'bullish',
                'action': 'hold_or_buy',
                'description': f'RSI {rsi_value:.1f} - 多头区域，持有或买入',
                'risk_level': 'medium'
            }
        else:
            return {
                'condition': 'bearish',
                'action': 'hold_or_sell',
                'description': f'RSI {rsi_value:.1f} - 空头区域，持有或卖出',
                'risk_level': 'medium'
            }
    
    def _find_support_resistance(self, rsi_values, lookback=50):
        """Find RSI support and resistance levels"""
        if len(rsi_values) < lookback:
            return {'support': None, 'resistance': None}
        
        recent_rsi = rsi_values[-lookback:]
        
        # Find common support/resistance levels
        support_levels = []
        resistance_levels = []
        
        # Check for horizontal support/resistance
        for level in [20, 30, 50, 70, 80]:
            touches = sum(1 for rsi in recent_rsi if abs(rsi - level) < 2)
            if touches >= 3:
                if level < 50:
                    support_levels.append(level)
                else:
                    resistance_levels.append(level)
        
        return {
            'support': support_levels[-1] if support_levels else 30,
            'resistance': resistance_levels