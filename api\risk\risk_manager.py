import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
from scipy import stats
import json

class RiskManager:
    """Risk Management and Portfolio Analysis"""
    
    def __init__(self, data_provider=None):
        self.logger = logging.getLogger(__name__)
        self.data_provider = data_provider
        self.risk_free_rate = 0.03  # 3% annual risk-free rate
    
    def analyze_portfolio(self, portfolio: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive portfolio risk analysis"""
        try:
            holdings = portfolio.get('holdings', [])
            total_value = portfolio.get('total_value', 0)
            
            if not holdings:
                return {'error': 'No holdings in portfolio'}
            
            # Calculate portfolio metrics
            portfolio_metrics = self._calculate_portfolio_metrics(holdings, total_value)
            
            # Risk metrics
            risk_metrics = self._calculate_risk_metrics(holdings)
            
            # Diversification analysis
            diversification = self._analyze_diversification(holdings)
            
            # Correlation analysis
            correlation_analysis = self._analyze_correlations(holdings)
            
            # Risk warnings
            risk_warnings = self._generate_risk_warnings(portfolio_metrics, risk_metrics, diversification)
            
            return {
                'portfolio_metrics': portfolio_metrics,
                'risk_metrics': risk_metrics,
                'diversification': diversification,
                'correlation_analysis': correlation_analysis,
                'risk_warnings': risk_warnings,
                'recommendations': self._generate_risk_recommendations(risk_metrics, diversification)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing portfolio: {str(e)}")
            return {'error': f'Portfolio analysis failed: {str(e)}'}
    
    def calculate_var(self, portfolio: Dict[str, Any], confidence_level: float = 0.95, 
                     time_horizon: int = 1) -> Dict[str, Any]:
        """Calculate Value at Risk (VaR)"""
        try:
            holdings = portfolio.get('holdings', [])
            total_value = portfolio.get('total_value', 0)
            
            if not holdings or total_value <= 0:
                return {'error': 'Invalid portfolio data'}
            
            # Get historical returns for portfolio components
            portfolio_returns = self._get_portfolio_returns(holdings)
            
            if 'error' in portfolio_returns:
                return portfolio_returns
            
            returns_data = portfolio_returns['returns']
            
            # Calculate VaR using different methods
            var_results = {
                'confidence_level': confidence_level,
                'time_horizon': time_horizon,
                'portfolio_value': total_value
            }
            
            # Historical VaR
            historical_var = self._calculate_historical_var(returns_data, confidence_level, time_horizon)
            var_results['historical_var'] = {
                'value': round(historical_var * total_value, 2),
                'percentage': round(historical_var * 100, 2)
            }
            
            # Parametric VaR
            parametric_var = self._calculate_parametric_var(returns_data, confidence_level, time_horizon)
            var_results['parametric_var'] = {
                'value': round(parametric_var * total_value, 2),
                'percentage': round(parametric_var * 100, 2)
            }
            
            # Monte Carlo VaR
            monte_carlo_var = self._calculate_monte_carlo_var(returns_data, confidence_level, time_horizon)
            var_results['monte_carlo_var'] = {
                'value': round(monte_carlo_var * total_value, 2),
                'percentage': round(monte_carlo_var * 100, 2)
            }
            
            # Expected Shortfall (Conditional VaR)
            expected_shortfall = self._calculate_expected_shortfall(returns_data, confidence_level)
            var_results['expected_shortfall'] = {
                'value': round(expected_shortfall * total_value, 2),
                'percentage': round(expected_shortfall * 100, 2)
            }
            
            return var_results
            
        except Exception as e:
            self.logger.error(f"Error calculating VaR: {str(e)}")
            return {'error': f'VaR calculation failed: {str(e)}'}
    
    def stress_test(self, portfolio: Dict[str, Any], scenarios: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Perform stress testing on portfolio"""
        try:
            holdings = portfolio.get('holdings', [])
            total_value = portfolio.get('total_value', 0)
            
            if not holdings:
                return {'error': 'No holdings in portfolio'}
            
            stress_results = []
            
            # Default scenarios if none provided
            if not scenarios:
                scenarios = self._get_default_stress_scenarios()
            
            for scenario in scenarios:
                scenario_result = self._apply_stress_scenario(holdings, total_value, scenario)
                stress_results.append(scenario_result)
            
            # Summary statistics
            worst_case = min(stress_results, key=lambda x: x['portfolio_change_pct'])
            best_case = max(stress_results, key=lambda x: x['portfolio_change_pct'])
            
            return {
                'stress_test_results': stress_results,
                'summary': {
                    'worst_case_scenario': worst_case,
                    'best_case_scenario': best_case,
                    'average_impact': round(np.mean([r['portfolio_change_pct'] for r in stress_results]), 2)
                },
                'risk_assessment': self._assess_stress_test_results(stress_results)
            }
            
        except Exception as e:
            self.logger.error(f"Error in stress testing: {str(e)}")
            return {'error': f'Stress testing failed: {str(e)}'}
    
    def calculate_position_sizing(self, stock_code: str, portfolio_value: float, 
                                risk_tolerance: float, volatility: float = None) -> Dict[str, Any]:
        """Calculate optimal position sizing"""
        try:
            # Get stock volatility if not provided
            if volatility is None:
                volatility = self._get_stock_volatility(stock_code)
                if 'error' in volatility:
                    return volatility
                volatility = volatility['volatility']
            
            # Kelly Criterion
            kelly_fraction = self._calculate_kelly_criterion(stock_code, volatility)
            
            # Risk-based position sizing
            risk_based_size = self._calculate_risk_based_position(portfolio_value, risk_tolerance, volatility)
            
            # Volatility-adjusted position sizing
            volatility_adjusted_size = self._calculate_volatility_adjusted_position(portfolio_value, volatility)
            
            # Maximum position size (diversification constraint)
            max_position_pct = min(0.20, 1.0 / max(5, int(portfolio_value / 10000)))  # Max 20% or 1/N rule
            max_position_value = portfolio_value * max_position_pct
            
            return {
                'stock_code': stock_code,
                'portfolio_value': portfolio_value,
                'volatility': round(volatility * 100, 2),
                'position_sizing_methods': {
                    'kelly_criterion': {
                        'fraction': round(kelly_fraction, 4),
                        'position_value': round(portfolio_value * kelly_fraction, 2),
                        'position_pct': round(kelly_fraction * 100, 2)
                    },
                    'risk_based': {
                        'position_value': round(risk_based_size, 2),
                        'position_pct': round(risk_based_size / portfolio_value * 100, 2)
                    },
                    'volatility_adjusted': {
                        'position_value': round(volatility_adjusted_size, 2),
                        'position_pct': round(volatility_adjusted_size / portfolio_value * 100, 2)
                    }
                },
                'recommended_position': {
                    'value': round(min(risk_based_size, max_position_value), 2),
                    'percentage': round(min(risk_based_size, max_position_value) / portfolio_value * 100, 2)
                },
                'max_position_limit': {
                    'value': round(max_position_value, 2),
                    'percentage': round(max_position_pct * 100, 2)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating position sizing: {str(e)}")
            return {'error': f'Position sizing calculation failed: {str(e)}'}
    
    def monitor_risk_limits(self, portfolio: Dict[str, Any], risk_limits: Dict[str, Any]) -> Dict[str, Any]:
        """Monitor portfolio against risk limits"""
        try:
            holdings = portfolio.get('holdings', [])
            total_value = portfolio.get('total_value', 0)
            
            violations = []
            warnings = []
            
            # Check concentration limits
            if 'max_position_pct' in risk_limits:
                max_position_pct = risk_limits['max_position_pct']
                for holding in holdings:
                    position_pct = holding.get('value', 0) / total_value * 100
                    if position_pct > max_position_pct:
                        violations.append({
                            'type': 'concentration_violation',
                            'stock_code': holding.get('stock_code'),
                            'current_pct': round(position_pct, 2),
                            'limit_pct': max_position_pct,
                            'severity': 'high'
                        })
            
            # Check sector concentration
            if 'max_sector_pct' in risk_limits:
                sector_exposure = self._calculate_sector_exposure(holdings)
                max_sector_pct = risk_limits['max_sector_pct']
                
                for sector, exposure_pct in sector_exposure.items():
                    if exposure_pct > max_sector_pct:
                        violations.append({
                            'type': 'sector_concentration_violation',
                            'sector': sector,
                            'current_pct': round(exposure_pct, 2),
                            'limit_pct': max_sector_pct,
                            'severity': 'medium'
                        })
            
            # Check portfolio volatility
            if 'max_portfolio_volatility' in risk_limits:
                portfolio_vol = self._calculate_portfolio_volatility(holdings)
                max_vol = risk_limits['max_portfolio_volatility']
                
                if portfolio_vol > max_vol:
                    violations.append({
                        'type': 'volatility_violation',
                        'current_volatility': round(portfolio_vol * 100, 2),
                        'limit_volatility': round(max_vol * 100, 2),
                        'severity': 'high'
                    })
            
            # Check drawdown limits
            if 'max_drawdown' in risk_limits:
                current_drawdown = portfolio.get('current_drawdown', 0)
                max_drawdown = risk_limits['max_drawdown']
                
                if current_drawdown > max_drawdown:
                    violations.append({
                        'type': 'drawdown_violation',
                        'current_drawdown': round(current_drawdown, 2),
                        'limit_drawdown': round(max_drawdown, 2),
                        'severity': 'critical'
                    })
            
            # Generate warnings for approaching limits
            warning_threshold = 0.8  # 80% of limit
            for violation in violations:
                if violation['type'] == 'concentration_violation':
                    if violation['current_pct'] > violation['limit_pct'] * warning_threshold:
                        warnings.append(f"Position in {violation['stock_code']} approaching concentration limit")
            
            return {
                'violations': violations,
                'warnings': warnings,
                'risk_score': self._calculate_risk_score(violations),
                'compliance_status': 'compliant' if not violations else 'violations_detected',
                'recommendations': self._generate_compliance_recommendations(violations)
            }
            
        except Exception as e:
            self.logger.error(f"Error monitoring risk limits: {str(e)}")
            return {'error': f'Risk monitoring failed: {str(e)}'}
    
    def _calculate_portfolio_metrics(self, holdings: List[Dict], total_value: float) -> Dict[str, Any]:
        """Calculate basic portfolio metrics"""
        try:
            num_positions = len(holdings)
            
            # Position sizes
            position_values = [holding.get('value', 0) for holding in holdings]
            position_weights = [value / total_value for value in position_values]
            
            # Concentration metrics
            max_position_weight = max(position_weights) if position_weights else 0
            top_5_weight = sum(sorted(position_weights, reverse=True)[:5])
            
            # Herfindahl Index (concentration measure)
            herfindahl_index = sum(w**2 for w in position_weights)
            
            return {
                'total_positions': num_positions,
                'total_value': total_value,
                'max_position_weight': round(max_position_weight * 100, 2),
                'top_5_concentration': round(top_5_weight * 100, 2),
                'herfindahl_index': round(herfindahl_index, 4),
                'effective_positions': round(1 / herfindahl_index, 1) if herfindahl_index > 0 else 0
            }
            
        except Exception as e:
            return {}
    
    def _calculate_risk_metrics(self, holdings: List[Dict]) -> Dict[str, Any]:
        """Calculate portfolio risk metrics"""
        try:
            # Portfolio volatility
            portfolio_vol = self._calculate_portfolio_volatility(holdings)
            
            # Beta calculation (simplified)
            portfolio_beta = self._calculate_portfolio_beta(holdings)
            
            # Tracking error (vs benchmark)
            tracking_error = self._calculate_tracking_error(holdings)
            
            # Information ratio
            information_ratio = self._calculate_information_ratio(holdings)
            
            return {
                'portfolio_volatility': round(portfolio_vol * 100, 2),
                'portfolio_beta': round(portfolio_beta, 2),
                'tracking_error': round(tracking_error * 100, 2),
                'information_ratio': round(information_ratio, 2),
                'risk_adjusted_return': self._calculate_risk_adjusted_return(holdings)
            }
            
        except Exception as e:
            return {}
    
    def _analyze_diversification(self, holdings: List[Dict]) -> Dict[str, Any]:
        """Analyze portfolio diversification"""
        try:
            # Sector diversification
            sector_exposure = self._calculate_sector_exposure(holdings)
            
            # Market cap diversification
            market_cap_exposure = self._calculate_market_cap_exposure(holdings)
            
            # Geographic diversification (for Chinese stocks)
            geographic_exposure = self._calculate_geographic_exposure(holdings)
            
            # Diversification score
            diversification_score = self._calculate_diversification_score(holdings)
            
            return {
                'sector_exposure': sector_exposure,
                'market_cap_exposure': market_cap_exposure,
                'geographic_exposure': geographic_exposure,
                'diversification_score': round(diversification_score, 2),
                'diversification_rating': self._get_diversification_rating(diversification_score)
            }
            
        except Exception as e:
            return {}
    
    def _analyze_correlations(self, holdings: List[Dict]) -> Dict[str, Any]:
        """Analyze correlations between holdings"""
        try:
            stock_codes = [holding.get('stock_code') for holding in holdings]
            
            # Get correlation matrix
            correlation_matrix = self._get_correlation_matrix(stock_codes)
            
            if 'error' in correlation_matrix:
                return correlation_matrix
            
            # Calculate average correlation
            correlations = correlation_matrix['correlations']
            avg_correlation = np.mean([correlations[i][j] for i in range(len(correlations)) 
                                    for j in range(i+1, len(correlations))])
            
            # Find highly correlated pairs
            high_correlation_pairs = []
            for i, stock1 in enumerate(stock_codes):
                for j, stock2 in enumerate(stock_codes[i+1:], i+1):
                    corr = correlations[i][j]
                    if abs(corr) > 0.7:  # High correlation threshold
                        high_correlation_pairs.append({
                            'stock1': stock1,
                            'stock2': stock2,
                            'correlation': round(corr, 3)
                        })
            
            return {
                'correlation_matrix': correlations,
                'average_correlation': round(avg_correlation, 3),
                'high_correlation_pairs': high_correlation_pairs,
                'correlation_risk': 'high' if avg_correlation > 0.5 else 'medium' if avg_correlation > 0.3 else 'low'
            }
            
        except Exception as e:
            return {}
    
    def _calculate_historical_var(self, returns: List[float], confidence_level: float, time_horizon: int) -> float:
        """Calculate historical VaR"""
        try:
            returns_array = np.array(returns)
            percentile = (1 - confidence_level) * 100
            var = np.percentile(returns_array, percentile)
            return abs(var) * np.sqrt(time_horizon)
        except:
            return 0.05  # Default 5%
    
    def _calculate_parametric_var(self, returns: List[float], confidence_level: float, time_horizon: int) -> float:
        """Calculate parametric VaR using normal distribution"""
        try:
            returns_array = np.array(returns)
            mean_return = np.mean(returns_array)
            std_return = np.std(returns_array)
            
            # Z-score for confidence level
            z_score = stats.norm.ppf(1 - confidence_level)
            
            var = abs(mean_return + z_score * std_return) * np.sqrt(time_horizon)
            return var
        except:
            return 0.05
    
    def _calculate_monte_carlo_var(self, returns: List[float], confidence_level: float, time_horizon: int) -> float:
        """Calculate Monte Carlo VaR"""
        try:
            returns_array = np.array(returns)
            mean_return = np.mean(returns_array)
            std_return = np.std(returns_array)
            
            # Generate random scenarios
            num_simulations = 10000
            simulated_returns = np.random.normal(mean_return, std_return, num_simulations)
            
            # Scale for time horizon
            simulated_returns *= np.sqrt(time_horizon)
            
            # Calculate VaR
            percentile = (1 - confidence_level) * 100
            var = abs(np.percentile(simulated_returns, percentile))
            return var
        except:
            return 0.05
    
    def _calculate_expected_shortfall(self, returns: List[float], confidence_level: float) -> float:
        """Calculate Expected Shortfall (Conditional VaR)"""
        try:
            returns_array = np.array(returns)
            percentile = (1 - confidence_level) * 100
            var_threshold = np.percentile(returns_array, percentile)
            
            # Expected shortfall is the mean of returns below VaR threshold
            tail_returns = returns_array[returns_array <= var_threshold]
            expected_shortfall = abs(np.mean(tail_returns)) if len(tail_returns) > 0 else 0.05
            
            return expected_shortfall
        except:
            return 0.06
    
    def _get_portfolio_returns(self, holdings: List[Dict]) -> Dict[str, Any]:
        """Get historical returns for portfolio components"""
        # Placeholder - would integrate with actual data provider
        # Generate sample returns for demonstration
        returns = np.random.normal(0.001, 0.02, 252).tolist()  # Daily returns for 1 year
        return {'returns': returns}
    
    def _get_default_stress_scenarios(self) -> List[Dict[str, Any]]:
        """Get default stress testing scenarios"""
        return [
            {
                'name': '市场崩盘',
                'description': '股市下跌30%',
                'market_shock': -0.30,
                'sector_impacts': {'金融': -0.35, '科技': -0.25, '消费': -0.20}
            },
            {
                'name': '利率上升',
                'description': '利率上升200个基点',
                'interest_rate_shock': 0.02,
                'sector_impacts': {'金融': 0.10, '房地产': -0.15, '公用事业': -0.10}
            },
            {
                'name': '经济衰退',
                'description': 'GDP下降5%',
                'gdp_shock': -0.05,
                'sector_impacts': {'周期性': -0.25, '防御性': -0.10, '消费': -0.20}
            }
        ]
    
    def _apply_stress_scenario(self, holdings: List[Dict], total_value: float, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """Apply stress scenario to portfolio"""
        try:
            scenario_impact = 0
            
            # Apply market shock if present
            if 'market_shock' in scenario:
                scenario_impact += scenario['market_shock']
            
            # Apply sector-specific impacts
            sector_impacts = scenario.get('sector_impacts', {})
            for holding in holdings:
                sector = holding.get('sector', '其他')
                if sector in sector_impacts:
                    scenario_impact += sector_impacts[sector] * (holding.get('value', 0) / total_value)
            
            new_portfolio_value = total_value * (1 + scenario_impact)
            
            return {
                'scenario_name': scenario.get('name', 'Unknown'),
                'scenario_description': scenario.get('description', ''),
                'portfolio_value_before': total_value,
                'portfolio_value_after': round(new_portfolio_value, 2),
                'portfolio_change': round(new_portfolio_value - total_value, 2),
                'portfolio_change_pct': round(scenario_impact * 100, 2)
            }
            
        except Exception as e:
            return {'error': f'Stress scenario application failed: {str(e)}'}
    
    def _get_stock_volatility(self, stock_code: str) -> Dict[str, Any]:
        """Get stock volatility"""
        # Placeholder - would integrate with actual data provider
        return {'volatility': 0.25}  # 25% annual volatility
    
    def _calculate_kelly_criterion(self, stock_code: str, volatility: float) -> float:
        """Calculate Kelly Criterion for position sizing"""
        try:
            # Simplified Kelly calculation
            # Kelly = (bp - q) / b where b=odds, p=win probability, q=lose probability
            win_probability = 0.55  # 55% win rate assumption
            avg_win = 0.08  # 8% average win
            avg_loss = 0.05  # 5% average loss
            
            kelly_fraction = (win_probability * avg_win - (1 - win_probability) * avg_loss) / avg_win
            
            # Cap Kelly fraction to prevent over-leverage
            return min(kelly_fraction, 0.25)  # Max 25%
            
        except Exception as e:
            return 0.05  # Conservative 5%
    
    def _calculate_risk_based_position(self, portfolio_value: float, risk_tolerance: float, volatility: float) -> float:
        """Calculate risk-based position size"""
        try:
            # Risk tolerance as percentage of portfolio to risk
            risk_amount = portfolio_value * risk_tolerance
            
            # Position size based on volatility
            position_size = risk_amount / volatility
            
            return min(position_size, portfolio_value * 0.20)  # Max 20% of portfolio
            
        except Exception as e:
            return portfolio_value * 0.05  # Conservative 5%
    
    def _calculate_volatility_adjusted_position(self, portfolio_value: float, volatility: float) -> float:
        """Calculate volatility-adjusted position size"""
        try:
            # Target volatility approach
            target_volatility = 0.15  # 15% target volatility
            
            if volatility > 0:
                position_fraction = target_volatility / volatility
                return portfolio_value * min(position_fraction, 0.20)  # Max 20%
            
            return portfolio_value * 0.10  # Default 10%
            
        except Exception as e:
            return portfolio_value * 0.05
    
    def _calculate_sector_exposure(self, holdings: List[Dict]) -> Dict[str, float]:
        """Calculate sector exposure percentages"""
        try:
            sector_values = {}
            total_value = sum(holding.get('value', 0) for holding in holdings)
            
            for holding in holdings:
                sector = holding.get('sector', '其他')
                value = holding.get('value', 0)
                
                if sector not in sector_values:
                    sector_values[sector] = 0
                sector_values[sector] += value
            
            # Convert to percentages
            sector_exposure = {}
            for sector, value in sector_values.items():
                sector_exposure[sector] = round(value / total_value * 100, 2) if total_value > 0 else 0
            
            return sector_exposure
            
        except Exception as e:
            return {}
    
    def _calculate_portfolio_volatility(self, holdings: List[Dict]) -> float:
        """Calculate portfolio volatility"""
        # Simplified calculation - would use actual correlation matrix
        try:
            weighted_volatilities = []
            total_value = sum(holding.get('value', 0) for holding in holdings)
            
            for holding in holdings:
                weight = holding.get('value', 0) / total_value if total_value > 0 else 0
                volatility = holding.get('volatility', 0.20)  # Default 20%
                weighted_volatilities.append(weight * volatility)
            
            # Simplified portfolio volatility (assumes some diversification)
            portfolio_vol = sum(weighted_volatilities) * 0.8  # 20% diversification benefit
            return portfolio_vol
            
        except Exception as e:
            return 0.20  # Default 20%
    
    def _calculate_portfolio_beta(self, holdings: List[Dict]) -> float:
        """Calculate portfolio beta"""
        try:
            weighted_betas = []
            total_value = sum(holding.get('value', 0) for holding in holdings)
            
            for holding in holdings:
                weight = holding.get('value', 0) / total_value if total_value > 0 else 0
                beta = holding.get('beta', 1.0)  # Default beta of 1
                weighted_betas.append(weight * beta)
            
            return sum(weighted_betas)
            
        except Exception as e:
            return 1.0  # Market beta
    
    def _calculate_tracking_error(self, holdings: List[Dict]) -> float:
        """Calculate tracking error vs benchmark"""
        # Placeholder calculation
        return 0.05  # 5% tracking error
    
    def _calculate_information_ratio(self, holdings: List[Dict]) -> float:
        """Calculate information ratio"""
        # Placeholder calculation
        return 0.5  # Information ratio
    
    def _calculate_risk_adjusted_return(self, holdings: List[Dict]) -> float:
        """Calculate risk-adjusted return"""
        # Placeholder calculation
        return 0.12  # 12% risk-adjusted return
    
    def _calculate_market_cap_exposure(self, holdings: List[Dict]) -> Dict[str, float]:
        """Calculate market cap exposure"""
        try:
            cap_categories = {'大盘股': 0, '中盘股': 0, '小盘股': 0}
            total_value = sum(holding.get('value', 0) for holding in holdings)
            
            for holding in holdings:
                market_cap = holding.get('market_cap', 0)
                value = holding.get('value', 0)
                
                # Categorize by market cap (in billions)
                if market_cap > 100:
                    cap_categories['大盘股'] += value
                elif market_cap > 20:
                    cap_categories['中盘股'] += value
                else:
                    cap_categories['小盘股'] += value
            
            # Convert to percentages
            for category in cap_categories:
                cap_categories[category] = round(cap_categories[category] / total_value * 100, 2) if total_value > 0 else 0
            
            return cap_categories
            
        except Exception as e:
            return {'大盘股': 0, '中盘股': 0, '小盘股': 0}
    
    def _calculate_geographic_exposure(self, holdings: List[Dict]) -> Dict[str, float]:
        """Calculate geographic exposure for Chinese stocks"""
        try:
            regions = {'沪市': 0, '深市': 0, '创业板': 0, '科创板': 0}
            total_value = sum(holding.get('value', 0) for holding in holdings)
            
            for holding in holdings:
                stock_code = holding.get('stock_code', '')
                value = holding.get('value', 0)
                
                # Categorize by stock code prefix
                if stock_code.startswith('60'):
                    regions['沪市'] += value
                elif stock_code.startswith('00'):
                    regions['深市'] += value
                elif stock_code.startswith('30'):
                    regions['创业板'] += value
                elif stock_code.startswith('68'):
                    regions['科创板'] += value
            
            # Convert to percentages
            for region in regions:
                regions[region] = round(regions[region] / total_value * 100, 2) if total_value > 0 else 0
            
            return regions
            
        except Exception as e:
            return {'沪市': 0, '深市': 0, '创业板': 0, '科创板': 0}
    
    def _calculate_diversification_score(self, holdings: List[Dict]) -> float:
        """Calculate overall diversification score"""
        try:
            # Factors: number of positions, sector diversity, correlation
            num_positions = len(holdings)
            
            # Position count score (0-40 points)
            position_score = min(num_positions * 2, 40)
            
            # Sector diversity score (0-30 points)
            sectors = set(holding.get('sector', '其他') for holding in holdings)
            sector_score = min(len(sectors) * 3, 30)
            
            # Concentration score (0-30 points)
            total_value = sum(holding.get('value', 0) for holding in holdings)
            max_position_pct = max(holding.get('value', 0) / total_value for holding in holdings) if holdings and total_value > 0 else 0
            concentration_score = max(0, 30 - max_position_pct * 100)
            
            total_score = position_score + sector_score + concentration_score
            return min(total_score, 100)
            
        except Exception as e:
            return 50  # Neutral score
    
    def _get_diversification_rating(self, score: float) -> str:
        """Get diversification rating based on score"""
        if score >= 80:
            return '优秀'
        elif score >= 60:
            return '良好'
        elif score >= 40:
            return '一般'
        else:
            return '较差'
    
    def _get_correlation_matrix(self, stock_codes: List[str]) -> Dict[str, Any]:
        """Get correlation matrix for stocks"""
        # Placeholder - would integrate with actual data provider
        n = len(stock_codes)
        correlations = [[0.5 if i != j else 1.0 for j in range(n)] for i in range(n)]
        return {'correlations': correlations}
    
    def _generate_risk_warnings(self, portfolio_metrics: Dict, risk_metrics: Dict, diversification: Dict) -> List[str]:
        """Generate risk warnings"""
        warnings = []
        
        # Concentration warnings
        if portfolio_metrics.get('max_position_weight', 0) > 20:
            warnings.append('投资组合存在过度集中风险')
        
        # Volatility warnings
        if risk_metrics.get('portfolio_volatility', 0) > 25:
            warnings.append('投资组合波动率较高')
        
        # Diversification warnings
        if diversification.get('diversification_score', 0) < 50:
            warnings.append('投资组合多样化程度不足')
        
        return warnings
    
    def _generate_risk_recommendations(self, risk_metrics: Dict, diversification: Dict) -> List[str]:
        """Generate risk management recommendations"""
        recommendations = []
        
        # Volatility recommendations
        if risk_metrics.get('portfolio_volatility', 0) > 25:
            recommendations.append('建议增加低波动率资产以降低组合风险')
        
        # Diversification recommendations
        if diversification.get('diversification_score', 0) < 60:
            recommendations.append('建议增加不同行业的股票以提高多样化')
        
        # Correlation recommendations
        avg_correlation = diversification.get('correlation_analysis', {}).get('average_correlation', 0)
        if avg_correlation > 0.5:
            recommendations.append('建议减少高相关性股票的持仓')
        
        return recommendations
    
    def _assess_stress_test_results(self, stress_results: List[Dict]) -> Dict[str, Any]:
        """Assess stress test results"""
        try:
            worst_loss = min(result['portfolio_change_pct'] for result in stress_results)
            
            if worst_loss < -30:
                risk_level = '高风险'
                recommendation = '建议增加防御性资产配置'
            elif worst_loss < -20:
                risk_level = '中等风险'
                recommendation = '建议适度调整投资组合结构'
            else:
                risk_level = '低风险'
                recommendation = '当前投资组合抗压能力较强'
            
            return {
                'risk_level': risk_level,
                'worst_case_loss': round(worst_loss, 2),
                'recommendation': recommendation
            }
            
        except Exception as e:
            return {'risk_level': '未知', 'recommendation': '无法评估风险'}
    
    def _calculate_risk_score(self, violations: List[Dict]) -> int:
        """Calculate overall risk score based on violations"""
        try:
            score = 0
            
            for violation in violations:
                severity = violation.get('severity', 'low')
                if severity == 'critical':
                    score += 25
                elif severity == 'high':
                    score += 15
                elif severity == 'medium':
                    score += 10
                else:
                    score += 5
            
            return min(score, 100)
            
        except Exception as e:
            return 0
    
    def _generate_compliance_recommendations(self, violations: List[Dict]) -> List[str]:
        """Generate compliance recommendations"""
        recommendations = []
        
        for violation in violations:
            if violation['type'] == 'concentration_violation':
                recommendations.append(f"减少{violation['stock_code']}的持仓至{violation['limit_pct']}%以下")
            elif violation['type'] == 'sector_concentration_violation':
                recommendations.append(f"减少{violation['sector']}行业的整体配置")
            elif violation['type'] == 'volatility_violation':
                recommendations.append('增加低波动率资产以降低组合风险')
            elif violation['type'] == 'drawdown_violation':
                recommendations.append('考虑止损或增加防御性资产')
        
        return recommendations