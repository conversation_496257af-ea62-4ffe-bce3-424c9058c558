import express, { Request, Response } from 'express';
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const router = express.Router();

// Helper function to call Python strategy engine
const callPythonStrategy = (action: string, data: any): Promise<any> => {
  return new Promise((resolve, reject) => {
    const pythonScript = path.join(__dirname, '../strategy/strategy_api.py');
    const python = spawn('python', [pythonScript, action, JSON.stringify(data)]);
    
    let result = '';
    let error = '';
    
    python.stdout.on('data', (data) => {
      result += data.toString();
    });
    
    python.stderr.on('data', (data) => {
      error += data.toString();
    });
    
    python.on('close', (code) => {
      if (code === 0) {
        try {
          resolve(JSON.parse(result));
        } catch (e) {
          reject(new Error('Invalid JSON response from Python script'));
        }
      } else {
        reject(new Error(error || 'Python script execution failed'));
      }
    });
  });
};

// Get all strategies
router.get('/strategies', async (req: Request, res: Response) => {
  try {
    const result = await callPythonStrategy('get_strategies', {});
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Create new strategy
router.post('/strategies', async (req: Request, res: Response) => {
  try {
    const strategyConfig = req.body;
    const result = await callPythonStrategy('create_strategy', strategyConfig);
    
    if (result.error) {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get strategy by ID
router.get('/strategies/:id', async (req: Request, res: Response) => {
  try {
    const strategyId = req.params.id;
    const result = await callPythonStrategy('get_strategy', { strategy_id: strategyId });
    
    if (result.error) {
      return res.status(404).json({
        success: false,
        error: result.error
      });
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Update strategy
router.put('/strategies/:id', async (req: Request, res: Response) => {
  try {
    const strategyId = req.params.id;
    const strategyConfig = { ...req.body, id: strategyId };
    const result = await callPythonStrategy('update_strategy', strategyConfig);
    
    if (result.error) {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Delete strategy
router.delete('/strategies/:id', async (req: Request, res: Response) => {
  try {
    const strategyId = req.params.id;
    const result = await callPythonStrategy('delete_strategy', { strategy_id: strategyId });
    
    if (result.error) {
      return res.status(404).json({
        success: false,
        error: result.error
      });
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Run backtest
router.post('/strategies/:id/backtest', async (req: Request, res: Response) => {
  try {
    const strategyId = req.params.id;
    const backtestConfig = req.body;
    const result = await callPythonStrategy('backtest_strategy', {
      strategy_id: strategyId,
      ...backtestConfig
    });
    
    if (result.error) {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get strategy performance
router.get('/strategies/:id/performance', async (req: Request, res: Response) => {
  try {
    const strategyId = req.params.id;
    const result = await callPythonStrategy('get_strategy_performance', { strategy_id: strategyId });
    
    if (result.error) {
      return res.status(404).json({
        success: false,
        error: result.error
      });
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get strategy signals
router.post('/strategies/:id/signals', async (req: Request, res: Response) => {
  try {
    const strategyId = req.params.id;
    const { stock_code, date } = req.body;
    const result = await callPythonStrategy('get_strategy_signals', {
      strategy_id: strategyId,
      stock_code,
      date
    });
    
    if (result.error) {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Optimize strategy
router.post('/strategies/:id/optimize', async (req: Request, res: Response) => {
  try {
    const strategyId = req.params.id;
    const optimizationConfig = req.body;
    const result = await callPythonStrategy('optimize_strategy', {
      strategy_id: strategyId,
      ...optimizationConfig
    });
    
    if (result.error) {
      return res.status(400).json({
        success: false,
        error: result.error
      });
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;