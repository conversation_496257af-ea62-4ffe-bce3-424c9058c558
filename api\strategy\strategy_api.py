#!/usr/bin/env python3
"""
Python API bridge for strategy engine
Handles communication between Node.js backend and Python strategy engine
"""

import sys
import json
import traceback
from typing import Dict, Any, Optional
from strategy_engine import StrategyEngine

class StrategyAPI:
    def __init__(self):
        self.engine = StrategyEngine()
        self.strategies = {}  # In-memory storage for demo purposes
        self.strategy_counter = 1
    
    def get_strategies(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get all strategies"""
        try:
            strategies_list = []
            for strategy_id, strategy in self.strategies.items():
                strategies_list.append({
                    'id': strategy_id,
                    'name': strategy.get('name', ''),
                    'description': strategy.get('description', ''),
                    'created_at': strategy.get('created_at', ''),
                    'updated_at': strategy.get('updated_at', ''),
                    'status': strategy.get('status', 'active')
                })
            
            return {
                'success': True,
                'strategies': strategies_list
            }
        except Exception as e:
            return {'error': str(e)}
    
    def create_strategy(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new strategy"""
        try:
            # Validate required fields
            required_fields = ['name', 'entry_rules', 'exit_rules']
            for field in required_fields:
                if field not in data:
                    return {'error': f'Missing required field: {field}'}
            
            # Create strategy using engine
            strategy_config = {
                'name': data['name'],
                'description': data.get('description', ''),
                'entry_rules': data['entry_rules'],
                'exit_rules': data['exit_rules'],
                'risk_management': data.get('risk_management', {})
            }
            
            result = self.engine.create_strategy(strategy_config)
            
            if result['success']:
                strategy_id = str(self.strategy_counter)
                self.strategy_counter += 1
                
                # Store strategy
                self.strategies[strategy_id] = {
                    **strategy_config,
                    'id': strategy_id,
                    'created_at': '2024-01-01T00:00:00Z',
                    'updated_at': '2024-01-01T00:00:00Z',
                    'status': 'active'
                }
                
                return {
                    'success': True,
                    'strategy': self.strategies[strategy_id]
                }
            else:
                return {'error': result.get('error', 'Failed to create strategy')}
                
        except Exception as e:
            return {'error': str(e)}
    
    def get_strategy(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get strategy by ID"""
        try:
            strategy_id = data.get('strategy_id')
            if not strategy_id:
                return {'error': 'Strategy ID is required'}
            
            if strategy_id not in self.strategies:
                return {'error': 'Strategy not found'}
            
            return {
                'success': True,
                'strategy': self.strategies[strategy_id]
            }
        except Exception as e:
            return {'error': str(e)}
    
    def update_strategy(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Update strategy"""
        try:
            strategy_id = data.get('id')
            if not strategy_id:
                return {'error': 'Strategy ID is required'}
            
            if strategy_id not in self.strategies:
                return {'error': 'Strategy not found'}
            
            # Update strategy
            strategy = self.strategies[strategy_id]
            for key, value in data.items():
                if key != 'id':
                    strategy[key] = value
            
            strategy['updated_at'] = '2024-01-01T00:00:00Z'
            
            return {
                'success': True,
                'strategy': strategy
            }
        except Exception as e:
            return {'error': str(e)}
    
    def delete_strategy(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Delete strategy"""
        try:
            strategy_id = data.get('strategy_id')
            if not strategy_id:
                return {'error': 'Strategy ID is required'}
            
            if strategy_id not in self.strategies:
                return {'error': 'Strategy not found'}
            
            del self.strategies[strategy_id]
            
            return {
                'success': True,
                'message': 'Strategy deleted successfully'
            }
        except Exception as e:
            return {'error': str(e)}
    
    def backtest_strategy(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Run backtest for strategy"""
        try:
            strategy_id = data.get('strategy_id')
            if not strategy_id:
                return {'error': 'Strategy ID is required'}
            
            if strategy_id not in self.strategies:
                return {'error': 'Strategy not found'}
            
            strategy = self.strategies[strategy_id]
            
            # Prepare backtest parameters
            backtest_params = {
                'start_date': data.get('start_date', '2023-01-01'),
                'end_date': data.get('end_date', '2024-01-01'),
                'initial_capital': data.get('initial_capital', 100000),
                'stocks': data.get('stocks', ['000001.SZ'])  # Default to Ping An Bank
            }
            
            # Run backtest using engine
            result = self.engine.backtest_strategy(strategy, backtest_params)
            
            if result['success']:
                return {
                    'success': True,
                    'backtest_result': result['result']
                }
            else:
                return {'error': result.get('error', 'Backtest failed')}
                
        except Exception as e:
            return {'error': str(e)}
    
    def get_strategy_performance(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get strategy performance metrics"""
        try:
            strategy_id = data.get('strategy_id')
            if not strategy_id:
                return {'error': 'Strategy ID is required'}
            
            if strategy_id not in self.strategies:
                return {'error': 'Strategy not found'}
            
            strategy = self.strategies[strategy_id]
            
            # Get performance using engine
            result = self.engine.get_strategy_performance(strategy)
            
            if result['success']:
                return {
                    'success': True,
                    'performance': result['performance']
                }
            else:
                return {'error': result.get('error', 'Failed to get performance')}
                
        except Exception as e:
            return {'error': str(e)}
    
    def get_strategy_signals(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get strategy signals for a stock"""
        try:
            strategy_id = data.get('strategy_id')
            stock_code = data.get('stock_code')
            date = data.get('date')
            
            if not all([strategy_id, stock_code]):
                return {'error': 'Strategy ID and stock code are required'}
            
            if strategy_id not in self.strategies:
                return {'error': 'Strategy not found'}
            
            strategy = self.strategies[strategy_id]
            
            # Get signals using engine
            result = self.engine.get_strategy_signals(strategy, stock_code, date)
            
            if result['success']:
                return {
                    'success': True,
                    'signals': result['signals']
                }
            else:
                return {'error': result.get('error', 'Failed to get signals')}
                
        except Exception as e:
            return {'error': str(e)}
    
    def optimize_strategy(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize strategy parameters"""
        try:
            strategy_id = data.get('strategy_id')
            if not strategy_id:
                return {'error': 'Strategy ID is required'}
            
            if strategy_id not in self.strategies:
                return {'error': 'Strategy not found'}
            
            strategy = self.strategies[strategy_id]
            optimization_params = data.get('optimization_params', {})
            
            # Optimize using engine
            result = self.engine.optimize_strategy(strategy, optimization_params)
            
            if result['success']:
                return {
                    'success': True,
                    'optimized_strategy': result['optimized_strategy']
                }
            else:
                return {'error': result.get('error', 'Optimization failed')}
                
        except Exception as e:
            return {'error': str(e)}

def main():
    """Main function to handle API calls"""
    try:
        if len(sys.argv) < 3:
            print(json.dumps({'error': 'Missing action and data parameters'}))
            sys.exit(1)
        
        action = sys.argv[1]
        data_str = sys.argv[2]
        
        try:
            data = json.loads(data_str)
        except json.JSONDecodeError:
            print(json.dumps({'error': 'Invalid JSON data'}))
            sys.exit(1)
        
        api = StrategyAPI()
        
        # Route to appropriate method
        method_map = {
            'get_strategies': api.get_strategies,
            'create_strategy': api.create_strategy,
            'get_strategy': api.get_strategy,
            'update_strategy': api.update_strategy,
            'delete_strategy': api.delete_strategy,
            'backtest_strategy': api.backtest_strategy,
            'get_strategy_performance': api.get_strategy_performance,
            'get_strategy_signals': api.get_strategy_signals,
            'optimize_strategy': api.optimize_strategy
        }
        
        if action not in method_map:
            print(json.dumps({'error': f'Unknown action: {action}'}))
            sys.exit(1)
        
        result = method_map[action](data)
        print(json.dumps(result))
        
    except Exception as e:
        error_result = {
            'error': str(e),
            'traceback': traceback.format_exc()
        }
        print(json.dumps(error_result))
        sys.exit(1)

if __name__ == '__main__':
    main()