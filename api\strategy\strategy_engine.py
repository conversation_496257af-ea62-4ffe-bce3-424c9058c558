import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
import json
import akshare as ak
from .technical_indicators import TechnicalIndicators

class StrategyEngine:
    """Strategy Building and Backtesting Engine"""
    
    def __init__(self, data_provider=None):
        self.logger = logging.getLogger(__name__)
        self.data_provider = data_provider
        self.strategies = {}
        self.backtest_results = {}
        self.technical_indicators = TechnicalIndicators()
        self.data_cache = {}
    
    def create_strategy(self, strategy_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new trading strategy"""
        try:
            strategy_id = strategy_config.get('id', f"strategy_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            
            strategy = {
                'id': strategy_id,
                'name': strategy_config.get('name', 'Unnamed Strategy'),
                'description': strategy_config.get('description', ''),
                'conditions': strategy_config.get('conditions', []),
                'entry_rules': strategy_config.get('entry_rules', []),
                'exit_rules': strategy_config.get('exit_rules', []),
                'risk_management': strategy_config.get('risk_management', {}),
                'position_sizing': strategy_config.get('position_sizing', {'type': 'fixed', 'value': 1000}),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'status': 'active'
            }
            
            # Validate strategy
            validation_result = self._validate_strategy(strategy)
            if not validation_result['valid']:
                return {'error': f'Strategy validation failed: {validation_result["errors"]}'}
            
            self.strategies[strategy_id] = strategy
            
            return {
                'strategy_id': strategy_id,
                'strategy': strategy,
                'message': 'Strategy created successfully'
            }
            
        except Exception as e:
            self.logger.error(f"Error creating strategy: {str(e)}")
            return {'error': f'Failed to create strategy: {str(e)}'}
    
    def backtest_strategy(self, strategy_id: str, backtest_config: Dict[str, Any]) -> Dict[str, Any]:
        """Backtest a trading strategy"""
        try:
            if strategy_id not in self.strategies:
                return {'error': 'Strategy not found'}
            
            strategy = self.strategies[strategy_id]
            
            # Get backtest parameters
            start_date = backtest_config.get('start_date', '2023-01-01')
            end_date = backtest_config.get('end_date', datetime.now().strftime('%Y-%m-%d'))
            initial_capital = backtest_config.get('initial_capital', 100000)
            stock_universe = backtest_config.get('stock_universe', ['000001', '000002', '600036'])
            
            # Run backtest
            backtest_result = self._run_backtest(
                strategy=strategy,
                start_date=start_date,
                end_date=end_date,
                initial_capital=initial_capital,
                stock_universe=stock_universe
            )
            
            # Store results
            backtest_id = f"backtest_{strategy_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.backtest_results[backtest_id] = backtest_result
            
            return {
                'backtest_id': backtest_id,
                'results': backtest_result
            }
            
        except Exception as e:
            self.logger.error(f"Error backtesting strategy: {str(e)}")
            return {'error': f'Failed to backtest strategy: {str(e)}'}
    
    def get_strategy_performance(self, strategy_id: str) -> Dict[str, Any]:
        """Get strategy performance metrics"""
        try:
            if strategy_id not in self.strategies:
                return {'error': 'Strategy not found'}
            
            # Find latest backtest results for this strategy
            strategy_backtests = {
                k: v for k, v in self.backtest_results.items() 
                if k.startswith(f"backtest_{strategy_id}")
            }
            
            if not strategy_backtests:
                return {'error': 'No backtest results found for this strategy'}
            
            # Get latest backtest
            latest_backtest = max(strategy_backtests.items(), key=lambda x: x[0])[1]
            
            return {
                'strategy_id': strategy_id,
                'performance': latest_backtest.get('performance_metrics', {}),
                'trades': latest_backtest.get('trades', []),
                'equity_curve': latest_backtest.get('equity_curve', []),
                'drawdown': latest_backtest.get('drawdown_analysis', {})
            }
            
        except Exception as e:
            self.logger.error(f"Error getting strategy performance: {str(e)}")
            return {'error': f'Failed to get strategy performance: {str(e)}'}
    
    def optimize_strategy(self, strategy_id: str, optimization_config: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize strategy parameters"""
        try:
            if strategy_id not in self.strategies:
                return {'error': 'Strategy not found'}
            
            strategy = self.strategies[strategy_id]
            
            # Get optimization parameters
            parameters_to_optimize = optimization_config.get('parameters', {})
            optimization_metric = optimization_config.get('metric', 'sharpe_ratio')
            
            # Run parameter optimization
            optimization_results = self._optimize_parameters(
                strategy=strategy,
                parameters=parameters_to_optimize,
                metric=optimization_metric,
                config=optimization_config
            )
            
            return {
                'strategy_id': strategy_id,
                'optimization_results': optimization_results,
                'best_parameters': optimization_results.get('best_parameters', {}),
                'best_performance': optimization_results.get('best_performance', {})
            }
            
        except Exception as e:
            self.logger.error(f"Error optimizing strategy: {str(e)}")
            return {'error': f'Failed to optimize strategy: {str(e)}'}
    
    def get_strategy_signals(self, strategy_id: str, stock_code: str, date: str = None) -> Dict[str, Any]:
        """Get trading signals for a specific stock based on strategy"""
        try:
            if strategy_id not in self.strategies:
                return {'error': 'Strategy not found'}
            
            strategy = self.strategies[strategy_id]
            
            if date is None:
                date = datetime.now().strftime('%Y-%m-%d')
            
            # Get stock data
            stock_data = self._get_stock_data(stock_code, date)
            
            if 'error' in stock_data:
                return stock_data
            
            # Generate signals
            signals = self._generate_signals(strategy, stock_data)
            
            return {
                'strategy_id': strategy_id,
                'stock_code': stock_code,
                'date': date,
                'signals': signals,
                'recommendations': self._get_recommendations(signals)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting strategy signals: {str(e)}")
            return {'error': f'Failed to get strategy signals: {str(e)}'}
    
    def _validate_strategy(self, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Validate strategy configuration"""
        errors = []
        
        # Check required fields
        required_fields = ['name', 'entry_rules', 'exit_rules']
        for field in required_fields:
            if not strategy.get(field):
                errors.append(f'Missing required field: {field}')
        
        # Validate entry rules
        if strategy.get('entry_rules'):
            for rule in strategy['entry_rules']:
                if not self._validate_rule(rule):
                    errors.append(f'Invalid entry rule: {rule}')
        
        # Validate exit rules
        if strategy.get('exit_rules'):
            for rule in strategy['exit_rules']:
                if not self._validate_rule(rule):
                    errors.append(f'Invalid exit rule: {rule}')
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    def _validate_rule(self, rule: Dict[str, Any]) -> bool:
        """Validate a single trading rule"""
        required_fields = ['indicator', 'condition', 'value']
        return all(field in rule for field in required_fields)
    
    def _run_backtest(self, strategy: Dict[str, Any], start_date: str, end_date: str, 
                     initial_capital: float, stock_universe: List[str]) -> Dict[str, Any]:
        """Run comprehensive strategy backtest with advanced risk management"""
        try:
            # Initialize backtest variables
            capital = initial_capital
            positions = {}
            trades = []
            equity_curve = []
            daily_returns = []
            peak_value = initial_capital
            drawdowns = []
            
            # Risk management parameters
            max_position_size = strategy.get('max_position_size', 0.2)  # Max 20% per position
            stop_loss_pct = strategy.get('stop_loss', 0.05)  # 5% stop loss
            take_profit_pct = strategy.get('take_profit', 0.15)  # 15% take profit
            max_portfolio_risk = strategy.get('max_portfolio_risk', 0.8)  # Max 80% invested
            
            # Generate date range
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            
            for date in date_range:
                date_str = date.strftime('%Y-%m-%d')
                
                # Skip weekends
                if date.weekday() >= 5:
                    continue
                
                daily_pnl = 0
                
                # First, check existing positions for stop loss/take profit
                positions_to_close = []
                for stock_code, position in positions.items():
                    stock_data = self._get_historical_stock_data(stock_code, date_str)
                    
                    if 'error' not in stock_data:
                        current_price = stock_data['price']
                        entry_price = position['entry_price']
                        price_change = (current_price - entry_price) / entry_price
                        
                        # Check stop loss
                        if price_change <= -stop_loss_pct:
                            positions_to_close.append((stock_code, 'stop_loss'))
                        # Check take profit
                        elif price_change >= take_profit_pct:
                            positions_to_close.append((stock_code, 'take_profit'))
                
                # Close positions that hit stop loss or take profit
                for stock_code, reason in positions_to_close:
                    stock_data = self._get_historical_stock_data(stock_code, date_str)
                    if 'error' not in stock_data:
                        position = positions[stock_code]
                        exit_value = position['shares'] * stock_data['price']
                        capital += exit_value
                        
                        # Calculate trade PnL
                        trade_pnl = exit_value - (position['shares'] * position['entry_price'])
                        daily_pnl += trade_pnl
                        
                        trades.append({
                            'stock_code': stock_code,
                            'action': 'sell',
                            'reason': reason,
                            'shares': position['shares'],
                            'price': stock_data['price'],
                            'date': date_str,
                            'value': exit_value,
                            'pnl': trade_pnl,
                            'pnl_pct': (trade_pnl / (position['shares'] * position['entry_price'])) * 100,
                            'hold_days': (pd.to_datetime(date_str) - pd.to_datetime(position['entry_date'])).days
                        })
                        
                        del positions[stock_code]
                
                # Process each stock in universe for new signals
                for stock_code in stock_universe:
                    # Get stock data for this date
                    stock_data = self._get_historical_stock_data(stock_code, date_str)
                    
                    if 'error' in stock_data:
                        continue
                    
                    # Generate signals
                    signals = self._generate_signals(strategy, stock_data)
                    
                    # Process entry signals
                    if signals.get('entry_signal') and stock_code not in positions:
                        # Calculate position size with risk management
                        max_position_value = capital * max_position_size
                        current_portfolio_value = sum(
                            pos['shares'] * self._get_current_price(stock, date_str)
                            for stock, pos in positions.items()
                        )
                        
                        # Check if we can add more positions (portfolio risk limit)
                        if current_portfolio_value < capital * max_portfolio_risk:
                            position_size = self._calculate_position_size(
                                strategy['position_sizing'], 
                                min(capital, max_position_value), 
                                stock_data['price']
                            )
                            
                            position_value = position_size * stock_data['price']
                            
                            if position_size > 0 and capital >= position_value:
                                positions[stock_code] = {
                                    'shares': position_size,
                                    'entry_price': stock_data['price'],
                                    'entry_date': date_str,
                                    'signal_strength': signals.get('signal_strength', 0)
                                }
                                capital -= position_value
                                
                                trades.append({
                                    'stock_code': stock_code,
                                    'action': 'buy',
                                    'reason': 'signal',
                                    'shares': position_size,
                                    'price': stock_data['price'],
                                    'date': date_str,
                                    'value': position_value,
                                    'signal_strength': signals.get('signal_strength', 0)
                                })
                    
                    # Process exit signals
                    elif signals.get('exit_signal') and stock_code in positions:
                        position = positions[stock_code]
                        exit_value = position['shares'] * stock_data['price']
                        capital += exit_value
                        
                        # Calculate trade PnL
                        trade_pnl = exit_value - (position['shares'] * position['entry_price'])
                        daily_pnl += trade_pnl
                        
                        trades.append({
                            'stock_code': stock_code,
                            'action': 'sell',
                            'reason': 'signal',
                            'shares': position['shares'],
                            'price': stock_data['price'],
                            'date': date_str,
                            'value': exit_value,
                            'pnl': trade_pnl,
                            'pnl_pct': (trade_pnl / (position['shares'] * position['entry_price'])) * 100,
                            'hold_days': (pd.to_datetime(date_str) - pd.to_datetime(position['entry_date'])).days,
                            'signal_strength': signals.get('signal_strength', 0)
                        })
                        
                        del positions[stock_code]
                    
                    # Update daily PnL for existing positions
                    if stock_code in positions:
                        position = positions[stock_code]
                        current_value = position['shares'] * stock_data['price']
                        entry_value = position['shares'] * position['entry_price']
                        unrealized_pnl = current_value - entry_value
                        daily_pnl += unrealized_pnl
                
                # Calculate total portfolio value
                portfolio_value = capital + sum(
                    pos['shares'] * self._get_current_price(stock, date_str)
                    for stock, pos in positions.items()
                )
                
                # Track peak and drawdown
                if portfolio_value > peak_value:
                    peak_value = portfolio_value
                
                drawdown = (peak_value - portfolio_value) / peak_value if peak_value > 0 else 0
                drawdowns.append(drawdown)
                
                equity_curve.append({
                    'date': date_str,
                    'portfolio_value': portfolio_value,
                    'cash': capital,
                    'positions_value': portfolio_value - capital,
                    'daily_pnl': daily_pnl,
                    'drawdown': drawdown,
                    'peak_value': peak_value,
                    'num_positions': len(positions)
                })
                
                # Calculate daily return
                if len(equity_curve) > 1:
                    prev_value = equity_curve[-2]['portfolio_value']
                    daily_return = (portfolio_value - prev_value) / prev_value if prev_value > 0 else 0
                    daily_returns.append(daily_return)
            
            # Calculate performance metrics
            performance_metrics = self._calculate_performance_metrics(
                equity_curve, daily_returns, initial_capital
            )
            
            # Calculate drawdown analysis
            drawdown_analysis = self._calculate_drawdown(equity_curve)
            
            # Calculate trade statistics
            trade_stats = self._calculate_trade_statistics(trades)
            
            return {
                'performance_metrics': performance_metrics,
                'trades': trades,
                'trade_statistics': trade_stats,
                'equity_curve': equity_curve,
                'drawdown_analysis': drawdown_analysis,
                'final_capital': capital,
                'positions': positions,
                'backtest_period': {'start': start_date, 'end': end_date},
                'risk_metrics': {
                    'max_drawdown': max(drawdowns) if drawdowns else 0,
                    'volatility': np.std(daily_returns) * np.sqrt(252) if daily_returns else 0,
                    'var_95': np.percentile(daily_returns, 5) if daily_returns else 0,
                    'max_positions': max([point.get('num_positions', 0) for point in equity_curve]),
                    'avg_position_size': np.mean([trade['value'] for trade in trades if trade['action'] == 'buy']) if trades else 0
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error running backtest: {str(e)}")
            return {'error': f'Backtest failed: {str(e)}'}
    
    def _generate_signals(self, strategy: Dict[str, Any], stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate trading signals based on strategy rules with advanced technical analysis"""
        signals = {
            'entry_signal': False,
            'exit_signal': False,
            'signal_strength': 0,
            'reasons': [],
            'technical_indicators': {},
            'market_conditions': {},
            'confidence_score': 0
        }
        
        try:
            # Extract technical indicators from stock data
            technical_indicators = {
                'rsi': stock_data.get('rsi', 50),
                'macd': stock_data.get('macd', 0),
                'macd_signal': stock_data.get('macd_signal', 0),
                'kdj_k': stock_data.get('kdj_k', 50),
                'kdj_d': stock_data.get('kdj_d', 50),
                'bb_position': self._calculate_bb_position(stock_data),
                'volume_ratio': stock_data.get('volume', 1000000) / 1000000,
                'price_momentum': stock_data.get('change_pct', 0)
            }
            
            signals['technical_indicators'] = technical_indicators
            
            # Assess market conditions
            market_conditions = self._assess_current_market_conditions(stock_data)
            signals['market_conditions'] = market_conditions
            
            # Check entry rules with enhanced logic
            entry_conditions_met = 0
            total_entry_conditions = len(strategy.get('entry_rules', []))
            entry_weights = []
            
            for rule in strategy.get('entry_rules', []):
                rule_result = self._evaluate_enhanced_rule(rule, stock_data, technical_indicators)
                if rule_result['triggered']:
                    entry_conditions_met += 1
                    entry_weights.append(rule_result['weight'])
                    signals['reasons'].append(f"Entry: {rule.get('description', rule.get('indicator', 'Condition met'))} (strength: {rule_result['weight']:.2f})")
            
            # Check exit rules with enhanced logic
            exit_conditions_met = 0
            total_exit_conditions = len(strategy.get('exit_rules', []))
            exit_weights = []
            
            for rule in strategy.get('exit_rules', []):
                rule_result = self._evaluate_enhanced_rule(rule, stock_data, technical_indicators)
                if rule_result['triggered']:
                    exit_conditions_met += 1
                    exit_weights.append(rule_result['weight'])
                    signals['reasons'].append(f"Exit: {rule.get('description', rule.get('indicator', 'Condition met'))} (strength: {rule_result['weight']:.2f})")
            
            # Calculate weighted signal strength
            if total_entry_conditions > 0:
                entry_threshold = strategy.get('entry_threshold', 0.8)
                base_strength = entry_conditions_met / total_entry_conditions
                weighted_strength = np.mean(entry_weights) if entry_weights else 0
                
                # Combine base and weighted strength
                combined_strength = (base_strength + weighted_strength) / 2
                
                # Apply market condition modifier
                market_modifier = market_conditions.get('trend_strength', 0.5)
                final_strength = combined_strength * (0.7 + 0.6 * market_modifier)
                
                if base_strength >= entry_threshold and final_strength >= 0.6:
                    signals['entry_signal'] = True
                    signals['signal_strength'] = final_strength
                    signals['confidence_score'] = self._calculate_confidence_score(technical_indicators, market_conditions)
            
            if total_exit_conditions > 0:
                exit_threshold = strategy.get('exit_threshold', 0.5)
                base_exit_strength = exit_conditions_met / total_exit_conditions
                weighted_exit_strength = np.mean(exit_weights) if exit_weights else 0
                
                combined_exit_strength = (base_exit_strength + weighted_exit_strength) / 2
                
                if base_exit_strength >= exit_threshold and combined_exit_strength >= 0.5:
                    signals['exit_signal'] = True
            
            # Add additional signal filters
            signals = self._apply_signal_filters(signals, strategy, stock_data)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Error generating signals: {str(e)}")
            return signals
    
    def _evaluate_rule(self, rule: Dict[str, Any], stock_data: Dict[str, Any]) -> bool:
        """Evaluate a single trading rule"""
        try:
            indicator = rule.get('indicator')
            condition = rule.get('condition')
            value = rule.get('value')
            
            # Get indicator value from stock data
            indicator_value = stock_data.get(indicator)
            
            if indicator_value is None:
                return False
            
            # Evaluate condition
            if condition == 'greater_than':
                return indicator_value > value
            elif condition == 'less_than':
                return indicator_value < value
            elif condition == 'equals':
                return indicator_value == value
            elif condition == 'crosses_above':
                # Simplified cross above logic
                return indicator_value > value
            elif condition == 'crosses_below':
                # Simplified cross below logic
                return indicator_value < value
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error evaluating rule: {str(e)}")
            return False
    
    def _calculate_position_size(self, position_sizing: Dict[str, Any], capital: float, price: float) -> int:
        """Calculate position size based on sizing rules"""
        try:
            sizing_type = position_sizing.get('type', 'fixed')
            
            if sizing_type == 'fixed':
                fixed_amount = position_sizing.get('value', 1000)
                return int(fixed_amount / price)
            elif sizing_type == 'percentage':
                percentage = position_sizing.get('value', 0.1)
                amount = capital * percentage
                return int(amount / price)
            elif sizing_type == 'risk_based':
                risk_amount = capital * position_sizing.get('risk_percentage', 0.02)
                stop_loss_pct = position_sizing.get('stop_loss_percentage', 0.05)
                position_value = risk_amount / stop_loss_pct
                return int(position_value / price)
            
            return 0
            
        except Exception as e:
            self.logger.error(f"Error calculating position size: {str(e)}")
            return 0
    
    def _get_stock_data(self, stock_code: str, date: str) -> Dict[str, Any]:
        """Get stock data with technical indicators for signal generation"""
        try:
            # Check cache first
            cache_key = f"{stock_code}_{date}"
            if cache_key in self.data_cache:
                return self.data_cache[cache_key]
            
            # Get historical data for technical analysis (need more data points)
            end_date = datetime.strptime(date, '%Y-%m-%d')
            start_date = end_date - timedelta(days=300)  # Get enough data for indicators
            
            try:
                # Try to get real data from akshare
                df = ak.stock_zh_a_hist(symbol=stock_code, 
                                      start_date=start_date.strftime('%Y%m%d'),
                                      end_date=end_date.strftime('%Y%m%d'),
                                      adjust="qfq")
                
                if df.empty:
                    raise Exception("No data available")
                
                # Rename columns to standard format
                df.columns = ['date', 'open', 'close', 'high', 'low', 'volume', 'turnover', 'amplitude', 'change_pct', 'change_amount', 'turnover_rate']
                df['date'] = pd.to_datetime(df['date'])
                df = df.sort_values('date')
                
                # Calculate all technical indicators
                indicators = self.technical_indicators.calculate_all_indicators(df)
                
                # Get the latest price data
                latest_data = df.iloc[-1]
                
                result = {
                    'price': float(latest_data['close']),
                    'open': float(latest_data['open']),
                    'high': float(latest_data['high']),
                    'low': float(latest_data['low']),
                    'volume': float(latest_data['volume']),
                    'change_pct': float(latest_data['change_pct']),
                    **indicators  # Add all technical indicators
                }
                
                # Cache the result
                self.data_cache[cache_key] = result
                return result
                
            except Exception as e:
                self.logger.warning(f"Failed to get real data for {stock_code}: {str(e)}, using mock data")
                # Fallback to mock data
                return self._get_mock_stock_data(stock_code, date)
                
        except Exception as e:
            self.logger.error(f"Error getting stock data: {str(e)}")
            return {'error': f'Failed to get stock data: {str(e)}'}
    
    def _get_mock_stock_data(self, stock_code: str, date: str) -> Dict[str, Any]:
        """Generate mock stock data with realistic technical indicators"""
        import random
        
        # Generate realistic mock data
        base_price = 10.0 + random.uniform(-2, 2)
        return {
            'price': round(base_price, 2),
            'open': round(base_price * random.uniform(0.98, 1.02), 2),
            'high': round(base_price * random.uniform(1.01, 1.05), 2),
            'low': round(base_price * random.uniform(0.95, 0.99), 2),
            'volume': random.randint(500000, 2000000),
            'change_pct': round(random.uniform(-5, 5), 2),
            'rsi': round(random.uniform(20, 80), 2),
            'macd': round(random.uniform(-0.5, 0.5), 3),
            'macd_signal': round(random.uniform(-0.3, 0.3), 3),
            'kdj_k': round(random.uniform(10, 90), 2),
            'kdj_d': round(random.uniform(10, 90), 2),
            'kdj_j': round(random.uniform(0, 100), 2),
            'bb_upper': round(base_price * 1.1, 2),
            'bb_middle': round(base_price, 2),
            'bb_lower': round(base_price * 0.9, 2),
            'sma_5': round(base_price * random.uniform(0.98, 1.02), 2),
            'sma_20': round(base_price * random.uniform(0.95, 1.05), 2),
            'ema_12': round(base_price * random.uniform(0.97, 1.03), 2),
            'adx': round(random.uniform(10, 50), 2),
            'atr': round(base_price * random.uniform(0.02, 0.08), 2),
            'magic_nine_buy': random.choice([True, False]),
            'magic_nine_sell': random.choice([True, False])
        }
    
    def _get_historical_stock_data(self, stock_code: str, date: str) -> Dict[str, Any]:
        """Get historical stock data for backtesting"""
        return self._get_stock_data(stock_code, date)
    
    def _get_current_price(self, stock_code: str, date: str) -> float:
        """Get current stock price"""
        # Placeholder
        return 10.50
    
    def _calculate_performance_metrics(self, equity_curve: List[Dict], daily_returns: List[float], initial_capital: float) -> Dict[str, Any]:
        """Calculate strategy performance metrics with advanced risk analysis"""
        try:
            if not equity_curve or not daily_returns:
                return {}
            
            final_value = equity_curve[-1]['portfolio_value']
            total_return = (final_value - initial_capital) / initial_capital
            trading_days = len(daily_returns)
            
            # Convert to numpy array for calculations
            returns_array = np.array(daily_returns)
            
            # Basic metrics
            annualized_return = (final_value / initial_capital) ** (252 / trading_days) - 1 if trading_days > 0 else 0
            volatility = np.std(returns_array) * np.sqrt(252)
            
            # Risk-adjusted returns
            risk_free_rate = 0.03  # Assume 3% risk-free rate
            excess_returns = returns_array - (risk_free_rate / 252)
            sharpe_ratio = np.mean(excess_returns) / np.std(returns_array) * np.sqrt(252) if np.std(returns_array) > 0 else 0
            
            # Downside deviation for Sortino ratio
            negative_returns = returns_array[returns_array < 0]
            downside_deviation = np.std(negative_returns) * np.sqrt(252) if len(negative_returns) > 0 else 0
            sortino_ratio = (annualized_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else 0
            
            # Advanced risk metrics
            var_95 = np.percentile(returns_array, 5) if len(returns_array) > 0 else 0  # Value at Risk (95%)
            cvar_95 = np.mean(returns_array[returns_array <= var_95]) if len(returns_array[returns_array <= var_95]) > 0 else 0  # Conditional VaR
            
            # Skewness and Kurtosis
            try:
                from scipy import stats
                skewness = stats.skew(returns_array) if len(returns_array) > 2 else 0
                kurtosis = stats.kurtosis(returns_array) if len(returns_array) > 3 else 0
            except ImportError:
                skewness = 0
                kurtosis = 0
            
            # Information ratio (assuming benchmark return of 8% annually)
            benchmark_return = 0.08 / 252  # Daily benchmark return
            tracking_error = np.std(returns_array - benchmark_return) * np.sqrt(252)
            information_ratio = (np.mean(returns_array) - benchmark_return) * 252 / tracking_error if tracking_error > 0 else 0
            
            # Calculate metrics
            max_drawdown = self._calculate_max_drawdown(equity_curve)
            win_rate = self._calculate_win_rate(equity_curve)
            profit_factor = self._calculate_profit_factor(daily_returns)
            
            # Calmar ratio
            calmar_ratio = annualized_return / (abs(max_drawdown) / 100) if max_drawdown != 0 else 0
            
            # Recovery factor
            recovery_factor = (total_return * 100) / abs(max_drawdown) if max_drawdown != 0 else 0
            
            # Ulcer Index (alternative to standard deviation)
            drawdowns = [point.get('drawdown', 0) for point in equity_curve]
            ulcer_index = np.sqrt(np.mean(np.array(drawdowns) ** 2)) if drawdowns else 0
            
            # Sterling ratio
            sterling_ratio = annualized_return / ulcer_index if ulcer_index > 0 else 0
            
            metrics = {
                # Basic Performance
                'total_return': round(total_return * 100, 2),
                'annualized_return': round(annualized_return * 100, 2),
                'cagr': round(annualized_return * 100, 2),
                
                # Risk Metrics
                'volatility': round(volatility * 100, 2),
                'max_drawdown': max_drawdown,
                'ulcer_index': round(ulcer_index * 100, 2),
                'var_95': round(var_95 * 100, 2),
                'cvar_95': round(cvar_95 * 100, 2),
                'skewness': round(skewness, 3),
                'kurtosis': round(kurtosis, 3),
                
                # Risk-Adjusted Returns
                'sharpe_ratio': round(sharpe_ratio, 2),
                'sortino_ratio': round(sortino_ratio, 2),
                'calmar_ratio': round(calmar_ratio, 2),
                'sterling_ratio': round(sterling_ratio, 2),
                'information_ratio': round(information_ratio, 2),
                'recovery_factor': round(recovery_factor, 2),
                
                # Trade Statistics
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                
                # Additional Metrics
                'trading_days': trading_days,
                'downside_deviation': round(downside_deviation * 100, 2)
            }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error calculating performance metrics: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_max_drawdown(self, equity_curve: List[Dict]) -> float:
        """Calculate maximum drawdown"""
        try:
            values = [point['portfolio_value'] for point in equity_curve]
            peak = values[0]
            max_dd = 0
            
            for value in values:
                if value > peak:
                    peak = value
                drawdown = (peak - value) / peak
                if drawdown > max_dd:
                    max_dd = drawdown
            
            return round(max_dd * 100, 2)
            
        except Exception as e:
            return 0
    
    def _calculate_drawdown(self, equity_curve: List[Dict]) -> Dict[str, Any]:
        """Calculate detailed drawdown analysis"""
        try:
            values = [point['portfolio_value'] for point in equity_curve]
            dates = [point['date'] for point in equity_curve]
            
            drawdowns = []
            peak = values[0]
            peak_date = dates[0]
            
            for i, (value, date) in enumerate(zip(values, dates)):
                if value > peak:
                    peak = value
                    peak_date = date
                
                drawdown_pct = (peak - value) / peak * 100
                drawdowns.append({
                    'date': date,
                    'drawdown_pct': round(drawdown_pct, 2),
                    'peak_value': peak,
                    'current_value': value
                })
            
            max_drawdown = max(drawdowns, key=lambda x: x['drawdown_pct'])
            
            return {
                'max_drawdown': max_drawdown,
                'drawdown_series': drawdowns,
                'recovery_time': self._calculate_recovery_time(drawdowns)
            }
            
        except Exception as e:
            return {}
    
    def _calculate_win_rate(self, equity_curve: List[Dict]) -> float:
        """Calculate win rate"""
        try:
            if len(equity_curve) < 2:
                return 0
            
            winning_days = 0
            total_days = len(equity_curve) - 1
            
            for i in range(1, len(equity_curve)):
                if equity_curve[i]['portfolio_value'] > equity_curve[i-1]['portfolio_value']:
                    winning_days += 1
            
            return round(winning_days / total_days * 100, 2) if total_days > 0 else 0
            
        except Exception as e:
            return 0
    
    def _calculate_profit_factor(self, daily_returns: List[float]) -> float:
        """Calculate profit factor"""
        try:
            positive_returns = [r for r in daily_returns if r > 0]
            negative_returns = [r for r in daily_returns if r < 0]
            
            gross_profit = sum(positive_returns)
            gross_loss = abs(sum(negative_returns))
            
            return round(gross_profit / gross_loss, 2) if gross_loss > 0 else 0
            
        except Exception as e:
            return 0
    
    def _calculate_sortino_ratio(self, returns_array: np.ndarray) -> float:
        """Calculate Sortino ratio"""
        try:
            negative_returns = returns_array[returns_array < 0]
            downside_deviation = np.std(negative_returns) if len(negative_returns) > 0 else 0
            
            return round(np.mean(returns_array) / downside_deviation * np.sqrt(252), 2) if downside_deviation > 0 else 0
            
        except Exception as e:
            return 0
    
    def _calculate_recovery_time(self, drawdowns: List[Dict]) -> int:
        """Calculate average recovery time from drawdowns"""
        try:
            recovery_times = []
            in_drawdown = False
            drawdown_start = None
            
            for i, dd in enumerate(drawdowns):
                if dd['drawdown_pct'] > 1 and not in_drawdown:  # Start of drawdown (>1%)
                    in_drawdown = True
                    drawdown_start = i
                elif dd['drawdown_pct'] <= 0.1 and in_drawdown:  # Recovery (<=0.1%)
                    in_drawdown = False
                    if drawdown_start is not None:
                        recovery_time = i - drawdown_start
                        recovery_times.append(recovery_time)
            
            return int(np.mean(recovery_times)) if recovery_times else 0
            
        except Exception as e:
            return 30  # Default fallback
    
    def _optimize_parameters(self, strategy: Dict[str, Any], parameters: Dict[str, Any], 
                           metric: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize strategy parameters using grid search"""
        try:
            optimization_results = []
            best_score = float('-inf')
            best_parameters = parameters.copy()
            best_performance = {}
            
            # Get optimization configuration
            start_date = config.get('start_date', '2023-01-01')
            end_date = config.get('end_date', datetime.now().strftime('%Y-%m-%d'))
            initial_capital = config.get('initial_capital', 100000)
            stock_universe = config.get('stock_universe', ['000001', '000002'])
            
            # Generate parameter combinations (simplified grid search)
            param_combinations = self._generate_parameter_combinations(parameters)
            
            for i, param_set in enumerate(param_combinations[:20]):  # Limit to 20 combinations
                # Create temporary strategy with new parameters
                temp_strategy = strategy.copy()
                temp_strategy.update(param_set)
                
                # Run backtest with these parameters
                backtest_result = self._run_backtest(
                    strategy=temp_strategy,
                    start_date=start_date,
                    end_date=end_date,
                    initial_capital=initial_capital,
                    stock_universe=stock_universe
                )
                
                if 'error' not in backtest_result:
                    performance = backtest_result.get('performance_metrics', {})
                    score = performance.get(metric, 0)
                    
                    optimization_results.append({
                        'parameters': param_set,
                        'performance': performance,
                        'score': score
                    })
                    
                    if score > best_score:
                        best_score = score
                        best_parameters = param_set
                        best_performance = performance
            
            return {
                'best_parameters': best_parameters,
                'best_performance': best_performance,
                'best_score': best_score,
                'optimization_metric': metric,
                'optimization_results': optimization_results,
                'total_combinations_tested': len(optimization_results)
            }
            
        except Exception as e:
            self.logger.error(f"Error optimizing parameters: {str(e)}")
            return {
                'best_parameters': parameters,
                'best_performance': {'error': str(e)},
                'optimization_results': []
            }
    
    def _get_recommendations(self, signals: Dict[str, Any]) -> List[str]:
        """Get trading recommendations based on signals"""
        recommendations = []
        
        if signals.get('entry_signal'):
            strength = signals.get('signal_strength', 0)
            if strength > 0.8:
                recommendations.append('强烈建议买入')
            else:
                recommendations.append('建议买入')
        
        if signals.get('exit_signal'):
            recommendations.append('建议卖出')
        
        if not signals.get('entry_signal') and not signals.get('exit_signal'):
            recommendations.append('建议观望')
        
        return recommendations
    
    def _generate_parameter_combinations(self, parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate parameter combinations for optimization"""
        try:
            combinations = []
            param_names = list(parameters.keys())
            
            if not param_names:
                return [{}]
            
            # Simple grid search implementation
            import itertools
            
            param_values = []
            for param_name, param_config in parameters.items():
                if isinstance(param_config, dict):
                    start = param_config.get('start', 0)
                    end = param_config.get('end', 1)
                    step = param_config.get('step', 0.1)
                    values = [start + i * step for i in range(int((end - start) / step) + 1)]
                    param_values.append(values[:10])  # Limit to 10 values per parameter
                else:
                    param_values.append([param_config])
            
            # Generate all combinations
            for combination in itertools.product(*param_values):
                param_dict = dict(zip(param_names, combination))
                combinations.append(param_dict)
            
            return combinations[:50]  # Limit total combinations
            
        except Exception as e:
            self.logger.error(f"Error generating parameter combinations: {str(e)}")
            return [{}]
    
    def _calculate_trade_statistics(self, trades: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate detailed trade statistics"""
        try:
            if not trades:
                return {}
            
            buy_trades = [t for t in trades if t['action'] == 'buy']
            sell_trades = [t for t in trades if t['action'] == 'sell' and 'pnl' in t]
            
            if not sell_trades:
                return {'total_trades': len(buy_trades), 'completed_trades': 0}
            
            # Calculate PnL statistics
            pnls = [t['pnl'] for t in sell_trades]
            winning_trades = [t for t in sell_trades if t['pnl'] > 0]
            losing_trades = [t for t in sell_trades if t['pnl'] < 0]
            
            # Calculate hold time statistics
            hold_times = [t.get('hold_days', 0) for t in sell_trades if 'hold_days' in t]
            
            return {
                'total_trades': len(buy_trades),
                'completed_trades': len(sell_trades),
                'winning_trades': len(winning_trades),
                'losing_trades': len(losing_trades),
                'win_rate': (len(winning_trades) / len(sell_trades) * 100) if sell_trades else 0,
                'avg_win': np.mean([t['pnl'] for t in winning_trades]) if winning_trades else 0,
                'avg_loss': np.mean([t['pnl'] for t in losing_trades]) if losing_trades else 0,
                'largest_win': max(pnls) if pnls else 0,
                'largest_loss': min(pnls) if pnls else 0,
                'profit_factor': (sum([t['pnl'] for t in winning_trades]) / abs(sum([t['pnl'] for t in losing_trades]))) if losing_trades else 0,
                'avg_hold_time': np.mean(hold_times) if hold_times else 0,
                'max_hold_time': max(hold_times) if hold_times else 0,
                'min_hold_time': min(hold_times) if hold_times else 0,
                'total_pnl': sum(pnls),
                'avg_pnl_per_trade': np.mean(pnls) if pnls else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating trade statistics: {str(e)}")
            return {}
    
    def _evaluate_enhanced_rule(self, rule: Dict[str, Any], stock_data: Dict[str, Any], 
                               market_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Enhanced rule evaluation with technical indicators and market context"""
        try:
            indicator = rule.get('indicator')
            condition = rule.get('condition')
            value = rule.get('value')
            weight = rule.get('weight', 1.0)
            
            # Get indicator value
            indicator_value = stock_data.get(indicator)
            if indicator_value is None:
                return {'result': False, 'confidence': 0, 'reason': f'Indicator {indicator} not available'}
            
            # Evaluate basic condition
            basic_result = False
            if condition == 'greater_than':
                basic_result = indicator_value > value
            elif condition == 'less_than':
                basic_result = indicator_value < value
            elif condition == 'equals':
                basic_result = abs(indicator_value - value) < 0.01
            elif condition == 'crosses_above':
                # Enhanced cross detection with historical data
                prev_value = stock_data.get(f'{indicator}_prev', indicator_value)
                basic_result = prev_value <= value and indicator_value > value
            elif condition == 'crosses_below':
                prev_value = stock_data.get(f'{indicator}_prev', indicator_value)
                basic_result = prev_value >= value and indicator_value < value
            elif condition == 'between':
                range_values = value if isinstance(value, list) else [value * 0.9, value * 1.1]
                basic_result = range_values[0] <= indicator_value <= range_values[1]
            
            # Calculate confidence based on indicator strength and market conditions
            confidence = self._get_indicator_confidence(indicator, indicator_value, stock_data, market_conditions)
            
            # Apply market condition filters
            market_filter = self._apply_market_condition_filter(rule, market_conditions)
            
            final_result = basic_result and market_filter
            
            return {
                'result': final_result,
                'confidence': confidence * weight,
                'reason': f'{indicator}={indicator_value:.3f} {condition} {value}',
                'market_filter': market_filter,
                'weight': weight
            }
            
        except Exception as e:
            self.logger.error(f"Error in enhanced rule evaluation: {str(e)}")
            return {'result': False, 'confidence': 0, 'reason': f'Error: {str(e)}'}
    
    def _calculate_bb_position(self, price: float, bb_upper: float, bb_middle: float, bb_lower: float) -> float:
        """Calculate position within Bollinger Bands (0=lower, 0.5=middle, 1=upper)"""
        try:
            if bb_upper == bb_lower:
                return 0.5
            return (price - bb_lower) / (bb_upper - bb_lower)
        except:
            return 0.5
    
    def _assess_current_market_conditions(self, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess current market conditions for context-aware trading"""
        try:
            conditions = {
                'trend': 'neutral',
                'volatility': 'normal',
                'momentum': 'neutral',
                'volume_profile': 'normal',
                'risk_level': 'medium'
            }
            
            # Trend analysis
            sma_5 = stock_data.get('sma_5', 0)
            sma_20 = stock_data.get('sma_20', 0)
            price = stock_data.get('price', 0)
            
            if sma_5 > sma_20 and price > sma_5:
                conditions['trend'] = 'bullish'
            elif sma_5 < sma_20 and price < sma_5:
                conditions['trend'] = 'bearish'
            
            # Volatility analysis
            atr = stock_data.get('atr', 0)
            price = stock_data.get('price', 1)
            atr_pct = (atr / price) * 100 if price > 0 else 0
            
            if atr_pct > 5:
                conditions['volatility'] = 'high'
            elif atr_pct < 2:
                conditions['volatility'] = 'low'
            
            # Momentum analysis
            rsi = stock_data.get('rsi', 50)
            macd = stock_data.get('macd', 0)
            
            if rsi > 70 and macd > 0:
                conditions['momentum'] = 'strong_bullish'
            elif rsi < 30 and macd < 0:
                conditions['momentum'] = 'strong_bearish'
            elif rsi > 60:
                conditions['momentum'] = 'bullish'
            elif rsi < 40:
                conditions['momentum'] = 'bearish'
            
            # Volume analysis
            volume = stock_data.get('volume', 0)
            avg_volume = stock_data.get('avg_volume', volume)
            
            if volume > avg_volume * 1.5:
                conditions['volume_profile'] = 'high'
            elif volume < avg_volume * 0.5:
                conditions['volume_profile'] = 'low'
            
            # Risk assessment
            if conditions['volatility'] == 'high' or conditions['momentum'] in ['strong_bearish']:
                conditions['risk_level'] = 'high'
            elif conditions['volatility'] == 'low' and conditions['trend'] == 'bullish':
                conditions['risk_level'] = 'low'
            
            return conditions
            
        except Exception as e:
            self.logger.error(f"Error assessing market conditions: {str(e)}")
            return {'trend': 'neutral', 'volatility': 'normal', 'momentum': 'neutral', 
                   'volume_profile': 'normal', 'risk_level': 'medium'}
    
    def _calculate_confidence_score(self, rule_results: List[Dict[str, Any]], 
                                   market_conditions: Dict[str, Any]) -> float:
        """Calculate overall confidence score for signal"""
        try:
            if not rule_results:
                return 0.0
            
            # Base confidence from rule results
            total_weight = sum(r.get('weight', 1.0) for r in rule_results)
            weighted_confidence = sum(r.get('confidence', 0) * r.get('weight', 1.0) for r in rule_results)
            base_confidence = weighted_confidence / total_weight if total_weight > 0 else 0
            
            # Market condition adjustments
            market_multiplier = 1.0
            
            # Trend alignment bonus
            if market_conditions.get('trend') == 'bullish':
                market_multiplier *= 1.1
            elif market_conditions.get('trend') == 'bearish':
                market_multiplier *= 0.9
            
            # Volatility adjustment
            if market_conditions.get('volatility') == 'high':
                market_multiplier *= 0.8  # Reduce confidence in high volatility
            elif market_conditions.get('volatility') == 'low':
                market_multiplier *= 1.05
            
            # Volume confirmation
            if market_conditions.get('volume_profile') == 'high':
                market_multiplier *= 1.1  # High volume confirms signals
            elif market_conditions.get('volume_profile') == 'low':
                market_multiplier *= 0.9
            
            final_confidence = min(base_confidence * market_multiplier, 1.0)
            return round(final_confidence, 3)
            
        except Exception as e:
            self.logger.error(f"Error calculating confidence score: {str(e)}")
            return 0.5
    
    def _get_indicator_confidence(self, indicator: str, value: float, 
                                 stock_data: Dict[str, Any], market_conditions: Dict[str, Any]) -> float:
        """Get confidence level for specific indicator"""
        try:
            confidence = 0.5  # Base confidence
            
            if indicator == 'rsi':
                # RSI confidence based on extreme values
                if value > 80 or value < 20:
                    confidence = 0.9
                elif value > 70 or value < 30:
                    confidence = 0.7
                else:
                    confidence = 0.4
            
            elif indicator == 'macd':
                # MACD confidence based on signal strength
                macd_signal = stock_data.get('macd_signal', 0)
                divergence = abs(value - macd_signal)
                confidence = min(divergence * 10, 0.9)
            
            elif indicator in ['kdj_k', 'kdj_d', 'kdj_j']:
                # KDJ confidence based on extreme values
                if value > 90 or value < 10:
                    confidence = 0.8
                elif value > 80 or value < 20:
                    confidence = 0.6
                else:
                    confidence = 0.3
            
            elif 'sma' in indicator or 'ema' in indicator:
                # Moving average confidence based on price distance
                price = stock_data.get('price', 0)
                if price > 0:
                    distance_pct = abs(price - value) / price
                    confidence = min(distance_pct * 5, 0.8)
            
            elif indicator == 'adx':
                # ADX confidence based on trend strength
                if value > 40:
                    confidence = 0.9
                elif value > 25:
                    confidence = 0.7
                else:
                    confidence = 0.3
            
            # Market condition adjustments
            if market_conditions.get('volatility') == 'high':
                confidence *= 0.8
            
            return round(confidence, 3)
            
        except Exception as e:
            return 0.5
    
    def _apply_signal_filters(self, signal_type: str, stock_data: Dict[str, Any], 
                             market_conditions: Dict[str, Any], last_signal_date: str = None) -> bool:
        """Apply various filters to signals"""
        try:
            # Time-based filter (minimum gap between signals)
            if last_signal_date:
                from datetime import datetime, timedelta
                last_date = datetime.strptime(last_signal_date, '%Y-%m-%d')
                current_date = datetime.now()
                if (current_date - last_date).days < 3:  # Minimum 3 days gap
                    return False
            
            # Volume filter
            volume = stock_data.get('volume', 0)
            avg_volume = stock_data.get('avg_volume', volume)
            if volume < avg_volume * 0.3:  # Too low volume
                return False
            
            # Volatility filter
            if market_conditions.get('volatility') == 'high' and signal_type == 'buy':
                # Be more cautious with buy signals in high volatility
                rsi = stock_data.get('rsi', 50)
                if rsi > 60:  # Don't buy when RSI is high in volatile markets
                    return False
            
            # Risk filter
            if market_conditions.get('risk_level') == 'high':
                # Apply stricter filters in high-risk conditions
                confidence_threshold = 0.7
            else:
                confidence_threshold = 0.5
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error applying signal filters: {str(e)}")
            return True  # Default to allowing signal
    
    def _apply_market_condition_filter(self, rule: Dict[str, Any], market_conditions: Dict[str, Any]) -> bool:
        """Apply market condition filters to rules"""
        try:
            # Get rule-specific market requirements
            required_trend = rule.get('required_trend')
            required_volatility = rule.get('required_volatility')
            required_volume = rule.get('required_volume')
            
            # Check trend requirement
            if required_trend and market_conditions.get('trend') != required_trend:
                return False
            
            # Check volatility requirement
            if required_volatility and market_conditions.get('volatility') != required_volatility:
                return False
            
            # Check volume requirement
            if required_volume and market_conditions.get('volume_profile') != required_volume:
                return False
            
            return True
            
        except Exception as e:
            return True  # Default to allowing rule