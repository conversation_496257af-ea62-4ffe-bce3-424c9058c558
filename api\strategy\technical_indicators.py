import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import talib
from datetime import datetime, timedelta

class TechnicalIndicators:
    """Comprehensive Technical Indicators for Chinese Stock Market Analysis"""
    
    def __init__(self):
        self.indicators_cache = {}
    
    def calculate_all_indicators(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Calculate all technical indicators for given stock data
        
        Args:
            data: DataFrame with columns ['open', 'high', 'low', 'close', 'volume']
            
        Returns:
            Dictionary containing all calculated indicators
        """
        try:
            if data.empty or len(data) < 20:
                return {}
            
            indicators = {}
            
            # Price data
            high = data['high'].values
            low = data['low'].values
            close = data['close'].values
            open_price = data['open'].values
            volume = data['volume'].values
            
            # Trend Indicators
            indicators.update(self._calculate_trend_indicators(high, low, close, open_price))
            
            # Momentum Indicators
            indicators.update(self._calculate_momentum_indicators(high, low, close))
            
            # Volume Indicators
            indicators.update(self._calculate_volume_indicators(close, volume))
            
            # Volatility Indicators
            indicators.update(self._calculate_volatility_indicators(high, low, close))
            
            # Chinese Market Specific Indicators
            indicators.update(self._calculate_chinese_indicators(data))
            
            # Pattern Recognition
            indicators.update(self._calculate_patterns(open_price, high, low, close))
            
            # Support and Resistance
            indicators.update(self._calculate_support_resistance(high, low, close))
            
            return indicators
            
        except Exception as e:
            print(f"Error calculating indicators: {str(e)}")
            return {}
    
    def _calculate_trend_indicators(self, high: np.ndarray, low: np.ndarray, 
                                  close: np.ndarray, open_price: np.ndarray) -> Dict[str, Any]:
        """Calculate trend-following indicators"""
        indicators = {}
        
        try:
            # Moving Averages
            indicators['sma_5'] = talib.SMA(close, timeperiod=5)[-1] if len(close) >= 5 else None
            indicators['sma_10'] = talib.SMA(close, timeperiod=10)[-1] if len(close) >= 10 else None
            indicators['sma_20'] = talib.SMA(close, timeperiod=20)[-1] if len(close) >= 20 else None
            indicators['sma_60'] = talib.SMA(close, timeperiod=60)[-1] if len(close) >= 60 else None
            indicators['ema_12'] = talib.EMA(close, timeperiod=12)[-1] if len(close) >= 12 else None
            indicators['ema_26'] = talib.EMA(close, timeperiod=26)[-1] if len(close) >= 26 else None
            
            # MACD
            if len(close) >= 26:
                macd, macd_signal, macd_hist = talib.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)
                indicators['macd'] = macd[-1] if not np.isnan(macd[-1]) else None
                indicators['macd_signal'] = macd_signal[-1] if not np.isnan(macd_signal[-1]) else None
                indicators['macd_histogram'] = macd_hist[-1] if not np.isnan(macd_hist[-1]) else None
                
                # MACD Divergence Detection
                indicators['macd_divergence'] = self._detect_macd_divergence(close, macd)
            
            # Parabolic SAR
            if len(high) >= 20:
                sar = talib.SAR(high, low, acceleration=0.02, maximum=0.2)
                indicators['sar'] = sar[-1] if not np.isnan(sar[-1]) else None
            
            # ADX (Average Directional Index)
            if len(close) >= 14:
                adx = talib.ADX(high, low, close, timeperiod=14)
                indicators['adx'] = adx[-1] if not np.isnan(adx[-1]) else None
                
                # Plus and Minus DI
                plus_di = talib.PLUS_DI(high, low, close, timeperiod=14)
                minus_di = talib.MINUS_DI(high, low, close, timeperiod=14)
                indicators['plus_di'] = plus_di[-1] if not np.isnan(plus_di[-1]) else None
                indicators['minus_di'] = minus_di[-1] if not np.isnan(minus_di[-1]) else None
            
        except Exception as e:
            print(f"Error calculating trend indicators: {str(e)}")
        
        return indicators
    
    def _calculate_momentum_indicators(self, high: np.ndarray, low: np.ndarray, 
                                     close: np.ndarray) -> Dict[str, Any]:
        """Calculate momentum oscillators"""
        indicators = {}
        
        try:
            # RSI
            if len(close) >= 14:
                rsi = talib.RSI(close, timeperiod=14)
                indicators['rsi'] = rsi[-1] if not np.isnan(rsi[-1]) else None
                indicators['rsi_6'] = talib.RSI(close, timeperiod=6)[-1] if len(close) >= 6 else None
            
            # Stochastic Oscillator
            if len(close) >= 14:
                slowk, slowd = talib.STOCH(high, low, close, fastk_period=14, slowk_period=3, slowd_period=3)
                indicators['stoch_k'] = slowk[-1] if not np.isnan(slowk[-1]) else None
                indicators['stoch_d'] = slowd[-1] if not np.isnan(slowd[-1]) else None
            
            # Williams %R
            if len(close) >= 14:
                willr = talib.WILLR(high, low, close, timeperiod=14)
                indicators['williams_r'] = willr[-1] if not np.isnan(willr[-1]) else None
            
            # CCI (Commodity Channel Index)
            if len(close) >= 14:
                cci = talib.CCI(high, low, close, timeperiod=14)
                indicators['cci'] = cci[-1] if not np.isnan(cci[-1]) else None
            
            # ROC (Rate of Change)
            if len(close) >= 10:
                roc = talib.ROC(close, timeperiod=10)
                indicators['roc'] = roc[-1] if not np.isnan(roc[-1]) else None
            
        except Exception as e:
            print(f"Error calculating momentum indicators: {str(e)}")
        
        return indicators
    
    def _calculate_volume_indicators(self, close: np.ndarray, volume: np.ndarray) -> Dict[str, Any]:
        """Calculate volume-based indicators"""
        indicators = {}
        
        try:
            # On Balance Volume
            if len(close) >= 2:
                obv = talib.OBV(close, volume)
                indicators['obv'] = obv[-1] if not np.isnan(obv[-1]) else None
            
            # Volume SMA
            if len(volume) >= 20:
                vol_sma = talib.SMA(volume, timeperiod=20)
                indicators['volume_sma_20'] = vol_sma[-1] if not np.isnan(vol_sma[-1]) else None
                indicators['volume_ratio'] = volume[-1] / vol_sma[-1] if vol_sma[-1] > 0 else None
            
            # Accumulation/Distribution Line
            if len(close) >= 2:
                ad = talib.AD(close, close, close, volume)  # Simplified for missing high/low
                indicators['ad_line'] = ad[-1] if not np.isnan(ad[-1]) else None
            
        except Exception as e:
            print(f"Error calculating volume indicators: {str(e)}")
        
        return indicators
    
    def _calculate_volatility_indicators(self, high: np.ndarray, low: np.ndarray, 
                                       close: np.ndarray) -> Dict[str, Any]:
        """Calculate volatility indicators"""
        indicators = {}
        
        try:
            # Bollinger Bands
            if len(close) >= 20:
                bb_upper, bb_middle, bb_lower = talib.BBANDS(close, timeperiod=20, nbdevup=2, nbdevdn=2)
                indicators['bb_upper'] = bb_upper[-1] if not np.isnan(bb_upper[-1]) else None
                indicators['bb_middle'] = bb_middle[-1] if not np.isnan(bb_middle[-1]) else None
                indicators['bb_lower'] = bb_lower[-1] if not np.isnan(bb_lower[-1]) else None
                
                # Bollinger Band Width
                if indicators['bb_upper'] and indicators['bb_lower'] and indicators['bb_middle']:
                    indicators['bb_width'] = (indicators['bb_upper'] - indicators['bb_lower']) / indicators['bb_middle']
                    indicators['bb_position'] = (close[-1] - indicators['bb_lower']) / (indicators['bb_upper'] - indicators['bb_lower'])
            
            # Average True Range
            if len(close) >= 14:
                atr = talib.ATR(high, low, close, timeperiod=14)
                indicators['atr'] = atr[-1] if not np.isnan(atr[-1]) else None
            
            # Keltner Channels
            if len(close) >= 20:
                ema_20 = talib.EMA(close, timeperiod=20)
                atr_10 = talib.ATR(high, low, close, timeperiod=10)
                if not np.isnan(ema_20[-1]) and not np.isnan(atr_10[-1]):
                    indicators['keltner_upper'] = ema_20[-1] + (2 * atr_10[-1])
                    indicators['keltner_middle'] = ema_20[-1]
                    indicators['keltner_lower'] = ema_20[-1] - (2 * atr_10[-1])
            
        except Exception as e:
            print(f"Error calculating volatility indicators: {str(e)}")
        
        return indicators
    
    def _calculate_chinese_indicators(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate Chinese market specific indicators"""
        indicators = {}
        
        try:
            high = data['high'].values
            low = data['low'].values
            close = data['close'].values
            
            # KDJ Indicator (Chinese version of Stochastic)
            kdj = self._calculate_kdj(high, low, close)
            indicators.update(kdj)
            
            # Magic Nine Turns (神奇九转)
            magic_nine = self._calculate_magic_nine_turns(close)
            indicators.update(magic_nine)
            
            # BOLL with Chinese parameters
            if len(close) >= 26:
                boll_upper, boll_middle, boll_lower = talib.BBANDS(close, timeperiod=26, nbdevup=2, nbdevdn=2)
                indicators['boll_upper_26'] = boll_upper[-1] if not np.isnan(boll_upper[-1]) else None
                indicators['boll_middle_26'] = boll_middle[-1] if not np.isnan(boll_middle[-1]) else None
                indicators['boll_lower_26'] = boll_lower[-1] if not np.isnan(boll_lower[-1]) else None
            
            # Chinese style moving averages
            if len(close) >= 250:
                indicators['ma_250'] = talib.SMA(close, timeperiod=250)[-1]
            
        except Exception as e:
            print(f"Error calculating Chinese indicators: {str(e)}")
        
        return indicators
    
    def _calculate_kdj(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, 
                      period: int = 9, k_period: int = 3, d_period: int = 3) -> Dict[str, Any]:
        """Calculate KDJ indicator (Chinese version of Stochastic)"""
        try:
            if len(close) < period:
                return {'kdj_k': None, 'kdj_d': None, 'kdj_j': None}
            
            # Calculate RSV (Raw Stochastic Value)
            rsv = []
            for i in range(period - 1, len(close)):
                period_high = np.max(high[i - period + 1:i + 1])
                period_low = np.min(low[i - period + 1:i + 1])
                
                if period_high == period_low:
                    rsv_value = 50
                else:
                    rsv_value = (close[i] - period_low) / (period_high - period_low) * 100
                rsv.append(rsv_value)
            
            # Calculate K, D, J
            k_values = []
            d_values = []
            j_values = []
            
            # Initial values
            k = 50
            d = 50
            
            for rsv_value in rsv:
                k = (2 * k + rsv_value) / 3
                d = (2 * d + k) / 3
                j = 3 * k - 2 * d
                
                k_values.append(k)
                d_values.append(d)
                j_values.append(j)
            
            return {
                'kdj_k': k_values[-1] if k_values else None,
                'kdj_d': d_values[-1] if d_values else None,
                'kdj_j': j_values[-1] if j_values else None
            }
            
        except Exception as e:
            print(f"Error calculating KDJ: {str(e)}")
            return {'kdj_k': None, 'kdj_d': None, 'kdj_j': None}
    
    def _calculate_magic_nine_turns(self, close: np.ndarray) -> Dict[str, Any]:
        """Calculate Magic Nine Turns indicator (神奇九转)"""
        try:
            if len(close) < 13:
                return {'magic_nine_buy': None, 'magic_nine_sell': None, 'magic_nine_count': 0}
            
            buy_count = 0
            sell_count = 0
            
            # Check for 9 consecutive closes higher than 4 periods ago (buy signal)
            for i in range(4, len(close)):
                if close[i] > close[i - 4]:
                    buy_count += 1
                    if buy_count >= 9:
                        break
                else:
                    buy_count = 0
            
            # Check for 9 consecutive closes lower than 4 periods ago (sell signal)
            for i in range(4, len(close)):
                if close[i] < close[i - 4]:
                    sell_count += 1
                    if sell_count >= 9:
                        break
                else:
                    sell_count = 0
            
            return {
                'magic_nine_buy': buy_count >= 9,
                'magic_nine_sell': sell_count >= 9,
                'magic_nine_count': max(buy_count, sell_count)
            }
            
        except Exception as e:
            print(f"Error calculating Magic Nine Turns: {str(e)}")
            return {'magic_nine_buy': None, 'magic_nine_sell': None, 'magic_nine_count': 0}
    
    def _calculate_patterns(self, open_price: np.ndarray, high: np.ndarray, 
                          low: np.ndarray, close: np.ndarray) -> Dict[str, Any]:
        """Calculate candlestick patterns"""
        indicators = {}
        
        try:
            if len(close) >= 5:
                # Doji
                doji = talib.CDLDOJI(open_price, high, low, close)
                indicators['pattern_doji'] = doji[-1] != 0 if not np.isnan(doji[-1]) else False
                
                # Hammer
                hammer = talib.CDLHAMMER(open_price, high, low, close)
                indicators['pattern_hammer'] = hammer[-1] != 0 if not np.isnan(hammer[-1]) else False
                
                # Shooting Star
                shooting_star = talib.CDLSHOOTINGSTAR(open_price, high, low, close)
                indicators['pattern_shooting_star'] = shooting_star[-1] != 0 if not np.isnan(shooting_star[-1]) else False
                
                # Engulfing patterns
                bullish_engulfing = talib.CDLENGULFING(open_price, high, low, close)
                indicators['pattern_bullish_engulfing'] = bullish_engulfing[-1] > 0 if not np.isnan(bullish_engulfing[-1]) else False
                indicators['pattern_bearish_engulfing'] = bullish_engulfing[-1] < 0 if not np.isnan(bullish_engulfing[-1]) else False
            
        except Exception as e:
            print(f"Error calculating patterns: {str(e)}")
        
        return indicators
    
    def _calculate_support_resistance(self, high: np.ndarray, low: np.ndarray, 
                                    close: np.ndarray, window: int = 20) -> Dict[str, Any]:
        """Calculate support and resistance levels"""
        try:
            if len(close) < window:
                return {'support': None, 'resistance': None}
            
            # Simple support/resistance based on recent highs and lows
            recent_high = np.max(high[-window:])
            recent_low = np.min(low[-window:])
            
            return {
                'support': recent_low,
                'resistance': recent_high,
                'support_distance': (close[-1] - recent_low) / close[-1] * 100,
                'resistance_distance': (recent_high - close[-1]) / close[-1] * 100
            }
            
        except Exception as e:
            print(f"Error calculating support/resistance: {str(e)}")
            return {'support': None, 'resistance': None}
    
    def _detect_macd_divergence(self, close: np.ndarray, macd: np.ndarray, window: int = 20) -> str:
        """Detect MACD divergence patterns"""
        try:
            if len(close) < window or len(macd) < window:
                return 'none'
            
            # Simple divergence detection
            recent_close = close[-window:]
            recent_macd = macd[-window:]
            
            # Check if price is making higher highs while MACD makes lower highs (bearish divergence)
            if (recent_close[-1] > recent_close[0] and 
                recent_macd[-1] < recent_macd[0]):
                return 'bearish'
            
            # Check if price is making lower lows while MACD makes higher lows (bullish divergence)
            if (recent_close[-1] < recent_close[0] and 
                recent_macd[-1] > recent_macd[0]):
                return 'bullish'
            
            return 'none'
            
        except Exception as e:
            return 'none'
    
    def get_signal_strength(self, indicators: Dict[str, Any]) -> Dict[str, float]:
        """Calculate overall signal strength based on multiple indicators"""
        try:
            bullish_signals = 0
            bearish_signals = 0
            total_signals = 0
            
            # RSI signals
            if indicators.get('rsi'):
                total_signals += 1
                if indicators['rsi'] < 30:
                    bullish_signals += 1
                elif indicators['rsi'] > 70:
                    bearish_signals += 1
            
            # MACD signals
            if indicators.get('macd') and indicators.get('macd_signal'):
                total_signals += 1
                if indicators['macd'] > indicators['macd_signal']:
                    bullish_signals += 1
                else:
                    bearish_signals += 1
            
            # KDJ signals
            if indicators.get('kdj_k') and indicators.get('kdj_d'):
                total_signals += 1
                if indicators['kdj_k'] < 20 and indicators['kdj_k'] > indicators['kdj_d']:
                    bullish_signals += 1
                elif indicators['kdj_k'] > 80 and indicators['kdj_k'] < indicators['kdj_d']:
                    bearish_signals += 1
            
            # Bollinger Bands signals
            if (indicators.get('bb_upper') and indicators.get('bb_lower') and 
                indicators.get('bb_position')):
                total_signals += 1
                if indicators['bb_position'] < 0.2:
                    bullish_signals += 1
                elif indicators['bb_position'] > 0.8:
                    bearish_signals += 1
            
            # Magic Nine Turns
            if indicators.get('magic_nine_buy') or indicators.get('magic_nine_sell'):
                total_signals += 1
                if indicators.get('magic_nine_buy'):
                    bullish_signals += 1
                elif indicators.get('magic_nine_sell'):
                    bearish_signals += 1
            
            if total_signals == 0:
                return {'bullish_strength': 0, 'bearish_strength': 0, 'neutral_strength': 1}
            
            bullish_strength = bullish_signals / total_signals
            bearish_strength = bearish_signals / total_signals
            neutral_strength = 1 - bullish_strength - bearish_strength
            
            return {
                'bullish_strength': round(bullish_strength, 2),
                'bearish_strength': round(bearish_strength, 2),
                'neutral_strength': round(neutral_strength, 2)
            }
            
        except Exception as e:
            print(f"Error calculating signal strength: {str(e)}")
            return {'bullish_strength': 0, 'bearish_strength': 0, 'neutral_strength': 1}