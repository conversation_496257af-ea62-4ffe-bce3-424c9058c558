"""
Unit tests for database operations
Tests the DatabaseManager class functionality
"""

import pytest
import psycopg2
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_manager import DatabaseManager
from config import DatabaseConfig

class TestDatabaseManager:
    """Test cases for DatabaseManager"""
    
    @pytest.fixture
    def mock_config(self):
        """Mock database configuration"""
        config = DatabaseConfig(
            host='localhost',
            port=5432,
            database='test_db',
            user='test_user',
            password='test_password'
        )
        return config
    
    @pytest.fixture
    def db_manager(self, mock_config):
        """Create DatabaseManager instance with mocked dependencies"""
        with patch('database.db_manager.psycopg2.pool.ThreadedConnectionPool'):
            manager = DatabaseManager(mock_config)
            return manager
    
    @pytest.fixture
    def mock_connection(self):
        """Mock database connection"""
        connection = Mock()
        cursor = Mock()
        connection.cursor.return_value.__enter__.return_value = cursor
        connection.cursor.return_value.__exit__.return_value = None
        return connection, cursor
    
    def test_init_with_valid_config(self, mock_config):
        """Test DatabaseManager initialization with valid config"""
        with patch('database.db_manager.psycopg2.pool.ThreadedConnectionPool') as mock_pool:
            manager = DatabaseManager(mock_config)
            assert manager.config == mock_config
            mock_pool.assert_called_once()
    
    def test_init_with_invalid_config(self):
        """Test DatabaseManager initialization with invalid config"""
        invalid_config = DatabaseConfig(
            host='localhost',
            port=5432,
            database='test_db',
            user='test_user',
            password=''  # Invalid empty password
        )
        
        with pytest.raises(ValueError):
            DatabaseManager(invalid_config)
    
    def test_save_stock_info_success(self, db_manager, mock_connection):
        """Test successful stock info saving"""
        connection, cursor = mock_connection
        
        with patch.object(db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = connection
            mock_get_conn.return_value.__exit__.return_value = None
            
            stock_info = {
                'stock_code': '000001',
                'stock_name': '平安银行',
                'market': 'SZ',
                'industry': '银行',
                'market_cap': 1000000000
            }
            
            result = db_manager.save_stock_info(stock_info)
            
            assert result is True
            cursor.execute.assert_called_once()
            connection.commit.assert_called_once()
    
    def test_save_stock_info_failure(self, db_manager, mock_connection):
        """Test stock info saving failure"""
        connection, cursor = mock_connection
        cursor.execute.side_effect = psycopg2.Error("Database error")
        
        with patch.object(db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = connection
            mock_get_conn.return_value.__exit__.return_value = None
            
            stock_info = {
                'stock_code': '000001',
                'stock_name': '平安银行',
                'market': 'SZ'
            }
            
            result = db_manager.save_stock_info(stock_info)
            
            assert result is False
    
    def test_save_price_data_batch_success(self, db_manager, mock_connection):
        """Test successful batch price data saving"""
        connection, cursor = mock_connection
        
        with patch.object(db_manager, 'get_connection') as mock_get_conn:
            with patch('database.db_manager.execute_values') as mock_execute_values:
                mock_get_conn.return_value.__enter__.return_value = connection
                mock_get_conn.return_value.__exit__.return_value = None
                
                price_data = [
                    {
                        'date': '2023-01-01',
                        'open': 10.0,
                        'high': 11.0,
                        'low': 9.5,
                        'close': 10.5,
                        'volume': 1000000
                    },
                    {
                        'date': '2023-01-02',
                        'open': 10.5,
                        'high': 11.5,
                        'low': 10.0,
                        'close': 11.0,
                        'volume': 1200000
                    }
                ]
                
                result = db_manager.save_price_data('000001', price_data)
                
                assert result is True
                mock_execute_values.assert_called_once()
                connection.commit.assert_called_once()
    
    def test_save_price_data_empty_list(self, db_manager):
        """Test saving empty price data list"""
        result = db_manager.save_price_data('000001', [])
        assert result is True
    
    def test_get_price_data_success(self, db_manager, mock_connection):
        """Test successful price data retrieval"""
        connection, cursor = mock_connection
        
        # Mock cursor to return RealDictCursor-like behavior
        mock_rows = [
            {
                'trade_date': datetime(2023, 1, 1).date(),
                'open_price': 10.0,
                'high_price': 11.0,
                'low_price': 9.5,
                'close_price': 10.5,
                'volume': 1000000
            }
        ]
        cursor.fetchall.return_value = mock_rows
        
        with patch.object(db_manager, 'get_connection') as mock_get_conn:
            with patch('database.db_manager.psycopg2.extras.RealDictCursor'):
                mock_get_conn.return_value.__enter__.return_value = connection
                mock_get_conn.return_value.__exit__.return_value = None
                
                result = db_manager.get_price_data('000001', limit=100)
                
                assert len(result) == 1
                assert result[0]['date'] == '2023-01-01'
                assert result[0]['open'] == 10.0
                assert result[0]['close'] == 10.5
    
    def test_get_price_data_with_date_range(self, db_manager, mock_connection):
        """Test price data retrieval with date range"""
        connection, cursor = mock_connection
        cursor.fetchall.return_value = []
        
        with patch.object(db_manager, 'get_connection') as mock_get_conn:
            with patch('database.db_manager.psycopg2.extras.RealDictCursor'):
                mock_get_conn.return_value.__enter__.return_value = connection
                mock_get_conn.return_value.__exit__.return_value = None
                
                result = db_manager.get_price_data(
                    '000001',
                    start_date='2023-01-01',
                    end_date='2023-01-31',
                    limit=100
                )
                
                # Verify SQL query includes date filters
                cursor.execute.assert_called_once()
                sql_query = cursor.execute.call_args[0][0]
                assert 'trade_date >= %s' in sql_query
                assert 'trade_date <= %s' in sql_query
    
    def test_save_analysis_result_success(self, db_manager, mock_connection):
        """Test successful analysis result saving"""
        connection, cursor = mock_connection
        
        with patch.object(db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = connection
            mock_get_conn.return_value.__exit__.return_value = None
            
            analysis_data = {'rsi': 65.5, 'macd': 0.25}
            signals = [{'type': 'buy', 'strength': 0.8}]
            
            result = db_manager.save_analysis_result(
                user_id=1,
                stock_code='000001',
                analysis_type='technical',
                analysis_data=analysis_data,
                signals=signals
            )
            
            assert result is True
            cursor.execute.assert_called_once()
            connection.commit.assert_called_once()
    
    def test_get_analysis_history_success(self, db_manager, mock_connection):
        """Test successful analysis history retrieval"""
        connection, cursor = mock_connection
        
        mock_rows = [
            {
                'id': 1,
                'user_id': 1,
                'stock_code': '000001',
                'analysis_result': '{"rsi": 65.5}',
                'signals': '[{"type": "buy"}]',
                'created_at': datetime.now()
            }
        ]
        cursor.fetchall.return_value = mock_rows
        
        with patch.object(db_manager, 'get_connection') as mock_get_conn:
            with patch('database.db_manager.psycopg2.extras.RealDictCursor'):
                mock_get_conn.return_value.__enter__.return_value = connection
                mock_get_conn.return_value.__exit__.return_value = None
                
                result = db_manager.get_analysis_history(user_id=1, limit=10)
                
                assert len(result) == 1
                assert result[0]['stock_code'] == '000001'
                assert 'analysis_data' in result[0]
                assert 'signals_generated' in result[0]
    
    def test_cleanup_old_data_success(self, db_manager, mock_connection):
        """Test successful old data cleanup"""
        connection, cursor = mock_connection
        
        with patch.object(db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = connection
            mock_get_conn.return_value.__exit__.return_value = None
            
            db_manager.cleanup_old_data(days_to_keep=30)
            
            # Should execute DELETE statements for both tables
            assert cursor.execute.call_count == 2
            connection.commit.assert_called_once()
    
    def test_get_database_stats_success(self, db_manager, mock_connection):
        """Test successful database statistics retrieval"""
        connection, cursor = mock_connection
        
        # Mock cursor to return different values for different queries
        cursor.fetchone.side_effect = [100, 50, 1000, 200, 10, 5, '10 MB']
        
        with patch.object(db_manager, 'get_connection') as mock_get_conn:
            mock_get_conn.return_value.__enter__.return_value = connection
            mock_get_conn.return_value.__exit__.return_value = None
            
            stats = db_manager.get_database_stats()
            
            assert 'users_count' in stats
            assert 'stock_info_count' in stats
            assert 'db_size' in stats
            assert stats['db_size'] == '10 MB'

if __name__ == '__main__':
    pytest.main([__file__])
