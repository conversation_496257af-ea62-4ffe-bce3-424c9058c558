"""
Unit tests for input validation utilities
Tests the validation functions and classes
"""

import pytest
from datetime import date, datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.validation import InputValidator, ValidationError, validate_request_data

class TestInputValidator:
    """Test cases for InputValidator class"""
    
    def test_validate_stock_code_valid_sh(self):
        """Test validation of valid Shanghai stock code"""
        result = InputValidator.validate_stock_code('600000')
        assert result == '600000'
        
        result = InputValidator.validate_stock_code('SH600000')
        assert result == '600000'
    
    def test_validate_stock_code_valid_sz(self):
        """Test validation of valid Shenzhen stock code"""
        result = InputValidator.validate_stock_code('000001')
        assert result == '000001'
        
        result = InputValidator.validate_stock_code('SZ000001')
        assert result == '000001'
    
    def test_validate_stock_code_valid_cyb(self):
        """Test validation of valid ChiNext stock code"""
        result = InputValidator.validate_stock_code('300001')
        assert result == '300001'
    
    def test_validate_stock_code_valid_kc(self):
        """Test validation of valid STAR Market stock code"""
        result = InputValidator.validate_stock_code('688001')
        assert result == '688001'
    
    def test_validate_stock_code_empty(self):
        """Test validation of empty stock code"""
        with pytest.raises(ValidationError, match="Stock code must be a non-empty string"):
            InputValidator.validate_stock_code('')
        
        with pytest.raises(ValidationError, match="Stock code must be a non-empty string"):
            InputValidator.validate_stock_code(None)
    
    def test_validate_stock_code_invalid_length(self):
        """Test validation of stock code with invalid length"""
        with pytest.raises(ValidationError, match="Stock code must be 6-8 characters long"):
            InputValidator.validate_stock_code('12345')
        
        with pytest.raises(ValidationError, match="Stock code must be 6-8 characters long"):
            InputValidator.validate_stock_code('123456789')
    
    def test_validate_stock_code_invalid_digits(self):
        """Test validation of stock code with invalid digits"""
        with pytest.raises(ValidationError, match="Stock code must contain exactly 6 digits"):
            InputValidator.validate_stock_code('ABCDEF')
        
        with pytest.raises(ValidationError, match="Stock code must contain exactly 6 digits"):
            InputValidator.validate_stock_code('12345A')
    
    def test_validate_date_string_valid(self):
        """Test validation of valid date string"""
        result = InputValidator.validate_date_string('2023-01-01')
        assert result == '2023-01-01'
        
        result = InputValidator.validate_date_string('2023-12-31')
        assert result == '2023-12-31'
    
    def test_validate_date_string_invalid_format(self):
        """Test validation of invalid date format"""
        with pytest.raises(ValidationError, match="Invalid date format"):
            InputValidator.validate_date_string('2023/01/01')
        
        with pytest.raises(ValidationError, match="Invalid date format"):
            InputValidator.validate_date_string('01-01-2023')
        
        with pytest.raises(ValidationError, match="Invalid date format"):
            InputValidator.validate_date_string('invalid-date')
    
    def test_validate_date_string_future_date(self):
        """Test validation of future date"""
        future_date = '2030-01-01'
        with pytest.raises(ValidationError, match="Date cannot be in the future"):
            InputValidator.validate_date_string(future_date)
    
    def test_validate_integer_valid(self):
        """Test validation of valid integers"""
        assert InputValidator.validate_integer(42) == 42
        assert InputValidator.validate_integer('42') == 42
        assert InputValidator.validate_integer(42.0) == 42
    
    def test_validate_integer_invalid(self):
        """Test validation of invalid integers"""
        with pytest.raises(ValidationError, match="value must be a valid integer"):
            InputValidator.validate_integer('not_a_number')
        
        with pytest.raises(ValidationError, match="value must be a valid integer"):
            InputValidator.validate_integer(None)
    
    def test_validate_integer_range(self):
        """Test validation of integer with range constraints"""
        assert InputValidator.validate_integer(50, min_val=0, max_val=100) == 50
        
        with pytest.raises(ValidationError, match="value must be at least 0"):
            InputValidator.validate_integer(-10, min_val=0)
        
        with pytest.raises(ValidationError, match="value must be at most 100"):
            InputValidator.validate_integer(150, max_val=100)
    
    def test_validate_float_valid(self):
        """Test validation of valid floats"""
        assert InputValidator.validate_float(42.5) == 42.5
        assert InputValidator.validate_float('42.5') == 42.5
        assert InputValidator.validate_float(42) == 42.0
    
    def test_validate_float_invalid(self):
        """Test validation of invalid floats"""
        with pytest.raises(ValidationError, match="value must be a valid number"):
            InputValidator.validate_float('not_a_number')
    
    def test_validate_float_range(self):
        """Test validation of float with range constraints"""
        assert InputValidator.validate_float(50.5, min_val=0.0, max_val=100.0) == 50.5
        
        with pytest.raises(ValidationError, match="value must be at least 0.0"):
            InputValidator.validate_float(-10.5, min_val=0.0)
    
    def test_validate_string_valid(self):
        """Test validation of valid strings"""
        assert InputValidator.validate_string('hello') == 'hello'
        assert InputValidator.validate_string('  hello  ') == 'hello'  # Should strip whitespace
    
    def test_validate_string_invalid_type(self):
        """Test validation of non-string input"""
        with pytest.raises(ValidationError, match="value must be a string"):
            InputValidator.validate_string(123)
    
    def test_validate_string_length(self):
        """Test validation of string length constraints"""
        with pytest.raises(ValidationError, match="value must be at least 5 characters long"):
            InputValidator.validate_string('hi', min_length=5)
        
        with pytest.raises(ValidationError, match="value must be at most 3 characters long"):
            InputValidator.validate_string('hello', max_length=3)
    
    def test_validate_string_pattern(self):
        """Test validation of string pattern matching"""
        # Valid email pattern
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        assert InputValidator.validate_string('<EMAIL>', pattern=email_pattern) == '<EMAIL>'
        
        with pytest.raises(ValidationError, match="value format is invalid"):
            InputValidator.validate_string('invalid-email', pattern=email_pattern)
    
    def test_validate_list_valid(self):
        """Test validation of valid lists"""
        assert InputValidator.validate_list(['a', 'b', 'c']) == ['a', 'b', 'c']
        assert InputValidator.validate_list([1, 2, 3], item_type=int) == [1, 2, 3]
    
    def test_validate_list_invalid_type(self):
        """Test validation of non-list input"""
        with pytest.raises(ValidationError, match="list must be a list"):
            InputValidator.validate_list('not_a_list')
    
    def test_validate_list_size_constraints(self):
        """Test validation of list size constraints"""
        with pytest.raises(ValidationError, match="list must contain at least 2 items"):
            InputValidator.validate_list(['a'], min_items=2)
        
        with pytest.raises(ValidationError, match="list must contain at most 2 items"):
            InputValidator.validate_list(['a', 'b', 'c'], max_items=2)
    
    def test_validate_list_item_types(self):
        """Test validation of list item types"""
        with pytest.raises(ValidationError, match="list\\[1\\] must be of type str"):
            InputValidator.validate_list(['a', 123, 'c'], item_type=str)
    
    def test_sanitize_sql_input(self):
        """Test SQL input sanitization"""
        assert InputValidator.sanitize_sql_input("normal text") == "normal text"
        assert InputValidator.sanitize_sql_input("text with 'quotes'") == "text with quotes"
        assert InputValidator.sanitize_sql_input("text; DROP TABLE users;") == "text DROP TABLE users"
        assert InputValidator.sanitize_sql_input("text /* comment */") == "text  comment "

class TestValidateRequestData:
    """Test cases for validate_request_data function"""
    
    def test_validate_request_data_valid(self):
        """Test validation of valid request data"""
        data = {
            'stock_code': '000001',
            'days': 100,
            'indicators': ['RSI', 'MACD']
        }
        
        schema = {
            'stock_code': {'type': 'stock_code', 'required': True},
            'days': {'type': 'integer', 'min_val': 1, 'max_val': 1000, 'required': False},
            'indicators': {'type': 'list', 'item_type': str, 'required': False}
        }
        
        result = validate_request_data(data, schema)
        
        assert result['stock_code'] == '000001'
        assert result['days'] == 100
        assert result['indicators'] == ['RSI', 'MACD']
    
    def test_validate_request_data_missing_required(self):
        """Test validation with missing required field"""
        data = {
            'days': 100
        }
        
        schema = {
            'stock_code': {'type': 'stock_code', 'required': True},
            'days': {'type': 'integer', 'required': False}
        }
        
        with pytest.raises(ValidationError, match="Field 'stock_code' is required"):
            validate_request_data(data, schema)
    
    def test_validate_request_data_optional_fields(self):
        """Test validation with optional fields"""
        data = {
            'stock_code': '000001'
        }
        
        schema = {
            'stock_code': {'type': 'stock_code', 'required': True},
            'days': {'type': 'integer', 'required': False},
            'optional_field': {'type': 'string', 'required': False}
        }
        
        result = validate_request_data(data, schema)
        
        assert result['stock_code'] == '000001'
        assert 'days' not in result
        assert 'optional_field' not in result
    
    def test_validate_request_data_invalid_field(self):
        """Test validation with invalid field value"""
        data = {
            'stock_code': 'invalid_code',
            'days': -10
        }
        
        schema = {
            'stock_code': {'type': 'stock_code', 'required': True},
            'days': {'type': 'integer', 'min_val': 1, 'required': True}
        }
        
        with pytest.raises(ValidationError):
            validate_request_data(data, schema)
    
    def test_validate_request_data_date_field(self):
        """Test validation with date field"""
        data = {
            'start_date': '2023-01-01',
            'end_date': '2023-12-31'
        }
        
        schema = {
            'start_date': {'type': 'date', 'required': True},
            'end_date': {'type': 'date', 'required': True}
        }
        
        result = validate_request_data(data, schema)
        
        assert result['start_date'] == '2023-01-01'
        assert result['end_date'] == '2023-12-31'

if __name__ == '__main__':
    pytest.main([__file__])
