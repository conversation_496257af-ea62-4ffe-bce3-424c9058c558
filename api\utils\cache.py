"""
Caching utilities for Trading Agent API
Provides in-memory and Redis-based caching with TTL support
"""

import json
import time
import hashlib
import logging
from typing import Any, Optional, Dict, Callable
from datetime import datetime, timedelta
from functools import wraps

logger = logging.getLogger(__name__)

class InMemoryCache:
    """Simple in-memory cache with TTL support"""
    
    def __init__(self, default_ttl: int = 300):
        self.cache: Dict[str, tuple] = {}
        self.default_ttl = default_ttl
        self.stats = {'hits': 0, 'misses': 0, 'sets': 0, 'deletes': 0}
    
    def _is_expired(self, timestamp: float, ttl: int) -> bool:
        """Check if cache entry is expired"""
        return time.time() - timestamp > ttl
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        if key not in self.cache:
            self.stats['misses'] += 1
            return None
        
        value, timestamp, ttl = self.cache[key]
        
        if self._is_expired(timestamp, ttl):
            del self.cache[key]
            self.stats['misses'] += 1
            return None
        
        self.stats['hits'] += 1
        return value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache"""
        if ttl is None:
            ttl = self.default_ttl
        
        self.cache[key] = (value, time.time(), ttl)
        self.stats['sets'] += 1
    
    def delete(self, key: str) -> bool:
        """Delete value from cache"""
        if key in self.cache:
            del self.cache[key]
            self.stats['deletes'] += 1
            return True
        return False
    
    def clear(self) -> None:
        """Clear all cache entries"""
        self.cache.clear()
        self.stats = {'hits': 0, 'misses': 0, 'sets': 0, 'deletes': 0}
    
    def cleanup_expired(self) -> int:
        """Remove expired entries and return count"""
        current_time = time.time()
        expired_keys = []
        
        for key, (value, timestamp, ttl) in self.cache.items():
            if current_time - timestamp > ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self.stats,
            'total_entries': len(self.cache),
            'hit_rate_percent': round(hit_rate, 2)
        }

class RedisCache:
    """Redis-based cache (optional, requires redis-py)"""
    
    def __init__(self, redis_client=None, default_ttl: int = 300, key_prefix: str = 'trading_agent:'):
        self.redis = redis_client
        self.default_ttl = default_ttl
        self.key_prefix = key_prefix
        self.available = redis_client is not None
        
        if not self.available:
            logger.warning("Redis client not available, falling back to in-memory cache")
    
    def _make_key(self, key: str) -> str:
        """Add prefix to key"""
        return f"{self.key_prefix}{key}"
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from Redis cache"""
        if not self.available:
            return None
        
        try:
            value = self.redis.get(self._make_key(key))
            if value is None:
                return None
            
            return json.loads(value.decode('utf-8'))
        except Exception as e:
            logger.error(f"Redis get error: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in Redis cache"""
        if not self.available:
            return False
        
        try:
            if ttl is None:
                ttl = self.default_ttl
            
            serialized_value = json.dumps(value, default=str)
            return self.redis.setex(
                self._make_key(key),
                ttl,
                serialized_value
            )
        except Exception as e:
            logger.error(f"Redis set error: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete value from Redis cache"""
        if not self.available:
            return False
        
        try:
            return bool(self.redis.delete(self._make_key(key)))
        except Exception as e:
            logger.error(f"Redis delete error: {e}")
            return False
    
    def clear(self, pattern: str = "*") -> int:
        """Clear cache entries matching pattern"""
        if not self.available:
            return 0
        
        try:
            keys = self.redis.keys(self._make_key(pattern))
            if keys:
                return self.redis.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"Redis clear error: {e}")
            return 0

class CacheManager:
    """Unified cache manager that can use Redis or in-memory cache"""
    
    def __init__(self, redis_client=None, default_ttl: int = 300):
        self.redis_cache = RedisCache(redis_client, default_ttl) if redis_client else None
        self.memory_cache = InMemoryCache(default_ttl)
        self.default_ttl = default_ttl
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache (Redis first, then memory)"""
        # Try Redis first
        if self.redis_cache and self.redis_cache.available:
            value = self.redis_cache.get(key)
            if value is not None:
                return value
        
        # Fallback to memory cache
        return self.memory_cache.get(key)
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache (both Redis and memory)"""
        # Set in Redis if available
        if self.redis_cache and self.redis_cache.available:
            self.redis_cache.set(key, value, ttl)
        
        # Always set in memory cache as fallback
        self.memory_cache.set(key, value, ttl)
    
    def delete(self, key: str) -> bool:
        """Delete value from cache"""
        redis_deleted = False
        memory_deleted = False
        
        if self.redis_cache and self.redis_cache.available:
            redis_deleted = self.redis_cache.delete(key)
        
        memory_deleted = self.memory_cache.delete(key)
        
        return redis_deleted or memory_deleted
    
    def clear(self) -> None:
        """Clear all cache entries"""
        if self.redis_cache and self.redis_cache.available:
            self.redis_cache.clear()
        
        self.memory_cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        stats = {
            'memory_cache': self.memory_cache.get_stats(),
            'redis_available': self.redis_cache.available if self.redis_cache else False
        }
        
        return stats

# Global cache instance
cache_manager = CacheManager()

def make_cache_key(*args, **kwargs) -> str:
    """Generate cache key from arguments"""
    key_data = {
        'args': args,
        'kwargs': sorted(kwargs.items())
    }
    
    key_string = json.dumps(key_data, sort_keys=True, default=str)
    return hashlib.md5(key_string.encode()).hexdigest()

def cached(ttl: int = 300, key_prefix: str = ""):
    """
    Decorator for caching function results
    
    Args:
        ttl: Time to live in seconds
        key_prefix: Prefix for cache key
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = f"{key_prefix}{func.__name__}:{make_cache_key(*args, **kwargs)}"
            
            # Try to get from cache
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {func.__name__}")
                return cached_result
            
            # Execute function
            logger.debug(f"Cache miss for {func.__name__}")
            result = func(*args, **kwargs)
            
            # Cache result
            cache_manager.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator

def cache_stock_data(stock_code: str, period: str = 'daily', days: int = 100):
    """Generate cache key for stock data"""
    return f"stock_data:{stock_code}:{period}:{days}"

def cache_technical_indicators(stock_code: str, indicators: list, timeframe: str = '1d'):
    """Generate cache key for technical indicators"""
    indicators_str = ','.join(sorted(indicators)) if indicators else 'all'
    return f"technical:{stock_code}:{indicators_str}:{timeframe}"

def cache_market_data(market: str = 'all', limit: int = 100):
    """Generate cache key for market data"""
    return f"market_data:{market}:{limit}"

# Initialize cache cleanup task
def cleanup_expired_cache():
    """Clean up expired cache entries"""
    try:
        expired_count = cache_manager.memory_cache.cleanup_expired()
        if expired_count > 0:
            logger.info(f"Cleaned up {expired_count} expired cache entries")
    except Exception as e:
        logger.error(f"Error cleaning up cache: {e}")

# Schedule cleanup every 5 minutes (you might want to use a proper scheduler like APScheduler)
import threading
import time

def start_cache_cleanup_thread():
    """Start background thread for cache cleanup"""
    def cleanup_loop():
        while True:
            time.sleep(300)  # 5 minutes
            cleanup_expired_cache()
    
    cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
    cleanup_thread.start()
    logger.info("Cache cleanup thread started")
