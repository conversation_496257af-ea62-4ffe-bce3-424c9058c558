"""
Utility decorators for Flask routes
Provides consistent error handling, logging, and validation
"""

import functools
import logging
from typing import Callable, Any, Dict
from flask import jsonify, request
from utils.validation import validate_request_data, ValidationError

logger = logging.getLogger(__name__)

def handle_errors(f: Callable) -> Callable:
    """
    Decorator for consistent error handling in Flask routes
    
    Catches common exceptions and returns standardized error responses
    """
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValidationError as e:
            logger.warning(f"Validation error in {f.__name__}: {e}")
            return jsonify({
                'success': False,
                'error': 'Validation error',
                'message': str(e)
            }), 400
        except ValueError as e:
            logger.warning(f"Value error in {f.__name__}: {e}")
            return jsonify({
                'success': False,
                'error': 'Invalid input',
                'message': str(e)
            }), 400
        except FileNotFoundError as e:
            logger.warning(f"Resource not found in {f.__name__}: {e}")
            return jsonify({
                'success': False,
                'error': 'Resource not found',
                'message': 'The requested resource was not found'
            }), 404
        except PermissionError as e:
            logger.warning(f"Permission denied in {f.__name__}: {e}")
            return jsonify({
                'success': False,
                'error': 'Permission denied',
                'message': 'You do not have permission to access this resource'
            }), 403
        except ConnectionError as e:
            logger.error(f"Connection error in {f.__name__}: {e}")
            return jsonify({
                'success': False,
                'error': 'Service unavailable',
                'message': 'Unable to connect to external service'
            }), 503
        except TimeoutError as e:
            logger.error(f"Timeout error in {f.__name__}: {e}")
            return jsonify({
                'success': False,
                'error': 'Request timeout',
                'message': 'The request took too long to process'
            }), 504
        except Exception as e:
            logger.error(f"Unexpected error in {f.__name__}: {e}", exc_info=True)
            return jsonify({
                'success': False,
                'error': 'Internal server error',
                'message': 'An unexpected error occurred'
            }), 500
    
    return decorated_function

def validate_json(schema: Dict[str, Dict[str, Any]]):
    """
    Decorator for validating JSON request data
    
    Args:
        schema: Validation schema dictionary
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # Get JSON data
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'error': 'Invalid request',
                    'message': 'Request body must be valid JSON'
                }), 400
            
            try:
                # Validate data
                validated_data = validate_request_data(data, schema)
                # Add validated data to kwargs
                kwargs['validated_data'] = validated_data
                return f(*args, **kwargs)
            except ValidationError as e:
                logger.warning(f"Validation error in {f.__name__}: {e}")
                return jsonify({
                    'success': False,
                    'error': 'Validation error',
                    'message': str(e)
                }), 400
        
        return decorated_function
    return decorator

def require_components(*component_names):
    """
    Decorator to check if required components are available
    
    Args:
        component_names: Names of required components
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # Import here to avoid circular imports
            from app import trading_server
            
            missing_components = []
            for component_name in component_names:
                if not trading_server.components.get(component_name):
                    missing_components.append(component_name)
            
            if missing_components:
                logger.error(f"Missing components in {f.__name__}: {missing_components}")
                return jsonify({
                    'success': False,
                    'error': 'Service unavailable',
                    'message': f'Required services are not available: {", ".join(missing_components)}'
                }), 503
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def log_request(include_response: bool = False):
    """
    Decorator for logging requests and optionally responses
    
    Args:
        include_response: Whether to log response data
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # Log request
            logger.info(f"Request to {f.__name__}: {request.method} {request.path}")
            if request.is_json:
                logger.debug(f"Request data: {request.get_json()}")
            
            # Execute function
            result = f(*args, **kwargs)
            
            # Log response if requested
            if include_response:
                logger.debug(f"Response from {f.__name__}: {result}")
            
            return result
        
        return decorated_function
    return decorator

def rate_limit(max_requests: int = 100, window_seconds: int = 3600):
    """
    Simple rate limiting decorator (in-memory)
    
    Args:
        max_requests: Maximum requests allowed
        window_seconds: Time window in seconds
    
    Note: This is a simple in-memory implementation.
    For production, use Redis or a proper rate limiting service.
    """
    import time
    from collections import defaultdict, deque
    
    # In-memory storage for request timestamps
    request_times = defaultdict(deque)
    
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # Get client identifier (IP address)
            client_id = request.remote_addr
            current_time = time.time()
            
            # Clean old requests
            client_requests = request_times[client_id]
            while client_requests and client_requests[0] < current_time - window_seconds:
                client_requests.popleft()
            
            # Check rate limit
            if len(client_requests) >= max_requests:
                logger.warning(f"Rate limit exceeded for {client_id} on {f.__name__}")
                return jsonify({
                    'success': False,
                    'error': 'Rate limit exceeded',
                    'message': f'Too many requests. Limit: {max_requests} per {window_seconds} seconds'
                }), 429
            
            # Add current request
            client_requests.append(current_time)
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def cache_response(ttl_seconds: int = 300):
    """
    Simple response caching decorator (in-memory)
    
    Args:
        ttl_seconds: Time to live in seconds
    
    Note: This is a simple in-memory implementation.
    For production, use Redis or a proper caching service.
    """
    import time
    import hashlib
    import json
    
    # In-memory cache
    cache = {}
    
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # Create cache key from function name, args, and request data
            cache_key_data = {
                'function': f.__name__,
                'args': args,
                'kwargs': {k: v for k, v in kwargs.items() if k != 'validated_data'},
                'path': request.path,
                'query': request.args.to_dict()
            }
            
            if request.is_json:
                cache_key_data['json'] = request.get_json()
            
            cache_key = hashlib.md5(
                json.dumps(cache_key_data, sort_keys=True).encode()
            ).hexdigest()
            
            current_time = time.time()
            
            # Check cache
            if cache_key in cache:
                cached_data, timestamp = cache[cache_key]
                if current_time - timestamp < ttl_seconds:
                    logger.debug(f"Cache hit for {f.__name__}")
                    return cached_data
                else:
                    # Remove expired entry
                    del cache[cache_key]
            
            # Execute function and cache result
            result = f(*args, **kwargs)
            cache[cache_key] = (result, current_time)
            logger.debug(f"Cache miss for {f.__name__}, result cached")
            
            return result
        
        return decorated_function
    return decorator
