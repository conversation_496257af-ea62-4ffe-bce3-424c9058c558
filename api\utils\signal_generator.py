from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

class SignalGenerator:
    """
    Signal Generator for Trading Analysis
    
    This class combines Magic Nine Turns and MACD signals to generate
    comprehensive trading signals with strength ratings.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Signal strength weights
        self.magic_nine_weight = 0.6
        self.macd_weight = 0.4
        
        # Minimum strength thresholds
        self.min_signal_strength = 1
        self.strong_signal_threshold = 3
    
    def generate_signals(self, magic_nine_data: Dict[str, Any], 
                        macd_data: Dict[str, Any], 
                        price_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Generate combined trading signals from Magic Nine Turns and MACD
        
        Args:
            magic_nine_data: Magic Nine Turns calculation results
            macd_data: MACD calculation results with divergences
            price_data: Recent price data
            
        Returns:
            List of trading signals
        """
        signals = []
        
        try:
            # Get current Magic Nine Turns signal
            magic_nine_signal = self._get_current_magic_nine_signal(magic_nine_data)
            
            # Get recent MACD divergence signals
            macd_signals = self._get_recent_macd_signals(macd_data)
            
            # Combine signals
            combined_signals = self._combine_signals(magic_nine_signal, macd_signals, price_data)
            
            # Filter and rank signals
            filtered_signals = self._filter_and_rank_signals(combined_signals)
            
            return filtered_signals
            
        except Exception as e:
            self.logger.error(f"Error generating signals: {e}")
            return []
    
    def _get_current_magic_nine_signal(self, magic_nine_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Extract current Magic Nine Turns signal
        
        Args:
            magic_nine_data: Magic Nine Turns data
            
        Returns:
            Current signal or None
        """
        if not magic_nine_data or not magic_nine_data.get('last_signal'):
            return None
        
        last_signal = magic_nine_data['last_signal']
        current_count = magic_nine_data.get('current_count', 0)
        current_direction = magic_nine_data.get('current_direction')
        
        # Check if signal is recent and valid
        if current_count >= magic_nine_data.get('signal_threshold', 9):
            return {
                'source': 'magic_nine',
                'type': last_signal['type'],
                'strength': last_signal['strength'],
                'count': current_count,
                'direction': current_direction,
                'price': last_signal['price'],
                'date': last_signal['date'],
                'description': last_signal['description']
            }
        
        return None
    
    def _get_recent_macd_signals(self, macd_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract recent MACD divergence signals
        
        Args:
            macd_data: MACD data with divergences
            
        Returns:
            List of recent MACD signals
        """
        if not macd_data or not macd_data.get('divergences'):
            return []
        
        divergences = macd_data['divergences']
        recent_signals = []
        
        # Get divergences from the last 10 periods
        for divergence in divergences[-3:]:  # Last 3 divergences
            signal_type = 'buy' if divergence['type'] == 'bullish' else 'sell'
            
            recent_signals.append({
                'source': 'macd',
                'type': signal_type,
                'strength': divergence['strength'],
                'divergence_type': divergence['type'],
                'price': divergence['price'],
                'date': divergence['date'],
                'description': divergence['description']
            })
        
        return recent_signals
    
    def _combine_signals(self, magic_nine_signal: Optional[Dict[str, Any]], 
                        macd_signals: List[Dict[str, Any]], 
                        price_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Combine Magic Nine Turns and MACD signals
        
        Args:
            magic_nine_signal: Magic Nine Turns signal
            macd_signals: MACD signals
            price_data: Price data for context
            
        Returns:
            List of combined signals
        """
        combined_signals = []
        current_price = price_data[-1]['close'] if price_data else 0
        current_date = price_data[-1]['date'] if price_data else datetime.now().strftime('%Y-%m-%d')
        
        # Individual signals
        if magic_nine_signal:
            combined_signals.append(magic_nine_signal)
        
        for macd_signal in macd_signals:
            combined_signals.append(macd_signal)
        
        # Look for confluence signals (both indicators agreeing)
        if magic_nine_signal and macd_signals:
            for macd_signal in macd_signals:
                if magic_nine_signal['type'] == macd_signal['type']:
                    # Both indicators agree - create confluence signal
                    confluence_strength = self._calculate_confluence_strength(
                        magic_nine_signal, macd_signal
                    )
                    
                    combined_signals.append({
                        'source': 'confluence',
                        'type': magic_nine_signal['type'],
                        'strength': confluence_strength,
                        'price': current_price,
                        'date': current_date,
                        'description': f"Confluence {magic_nine_signal['type'].upper()} signal: Magic Nine Turns + MACD divergence",
                        'components': {
                            'magic_nine': magic_nine_signal,
                            'macd': macd_signal
                        }
                    })
        
        return combined_signals
    
    def _calculate_confluence_strength(self, magic_nine_signal: Dict[str, Any], 
                                     macd_signal: Dict[str, Any]) -> int:
        """
        Calculate strength of confluence signal
        
        Args:
            magic_nine_signal: Magic Nine Turns signal
            macd_signal: MACD signal
            
        Returns:
            Confluence strength (1-5)
        """
        magic_nine_strength = magic_nine_signal.get('strength', 1)
        macd_strength = macd_signal.get('strength', 1)
        
        # Weighted average with bonus for confluence
        weighted_strength = (
            magic_nine_strength * self.magic_nine_weight + 
            macd_strength * self.macd_weight
        )
        
        # Add confluence bonus
        confluence_bonus = 1.5
        final_strength = weighted_strength * confluence_bonus
        
        # Cap at 5 and ensure minimum of 1
        return max(1, min(5, int(round(final_strength))))
    
    def _filter_and_rank_signals(self, signals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filter and rank signals by strength and relevance
        
        Args:
            signals: List of signals to filter
            
        Returns:
            Filtered and ranked signals
        """
        # Filter out weak signals
        filtered_signals = [
            signal for signal in signals 
            if signal.get('strength', 0) >= self.min_signal_strength
        ]
        
        # Sort by strength (descending) and source priority
        def signal_priority(signal):
            strength = signal.get('strength', 0)
            source_priority = {
                'confluence': 3,
                'magic_nine': 2,
                'macd': 1
            }
            return (strength, source_priority.get(signal.get('source', ''), 0))
        
        filtered_signals.sort(key=signal_priority, reverse=True)
        
        # Add signal quality indicators
        for signal in filtered_signals:
            signal['quality'] = self._assess_signal_quality(signal)
            signal['confidence'] = self._calculate_confidence(signal)
        
        return filtered_signals
    
    def _assess_signal_quality(self, signal: Dict[str, Any]) -> str:
        """
        Assess signal quality based on strength and source
        
        Args:
            signal: Signal to assess
            
        Returns:
            Quality rating: 'excellent', 'good', 'fair', 'poor'
        """
        strength = signal.get('strength', 0)
        source = signal.get('source', '')
        
        if source == 'confluence' and strength >= 4:
            return 'excellent'
        elif source == 'confluence' and strength >= 3:
            return 'good'
        elif strength >= 4:
            return 'good'
        elif strength >= 3:
            return 'fair'
        else:
            return 'poor'
    
    def _calculate_confidence(self, signal: Dict[str, Any]) -> float:
        """
        Calculate confidence level for signal
        
        Args:
            signal: Signal to calculate confidence for
            
        Returns:
            Confidence level (0.0 to 1.0)
        """
        strength = signal.get('strength', 0)
        source = signal.get('source', '')
        
        base_confidence = strength / 5.0  # Normalize to 0-1
        
        # Adjust based on source
        if source == 'confluence':
            base_confidence *= 1.2  # 20% bonus for confluence
        elif source == 'magic_nine':
            base_confidence *= 1.0  # No adjustment
        elif source == 'macd':
            base_confidence *= 0.9  # Slight reduction for MACD alone
        
        return min(1.0, base_confidence)
    
    def meets_criteria(self, magic_nine_data: Dict[str, Any], 
                      macd_data: Dict[str, Any],
                      magic_nine_condition: Dict[str, Any], 
                      macd_condition: Dict[str, Any]) -> bool:
        """
        Check if stock meets screening criteria
        
        Args:
            magic_nine_data: Magic Nine Turns data
            macd_data: MACD data
            magic_nine_condition: Magic Nine Turns filtering conditions
            macd_condition: MACD filtering conditions
            
        Returns:
            True if stock meets criteria
        """
        try:
            # Check Magic Nine Turns criteria
            if magic_nine_condition:
                if not self._check_magic_nine_criteria(magic_nine_data, magic_nine_condition):
                    return False
            
            # Check MACD criteria
            if macd_condition:
                if not self._check_macd_criteria(macd_data, macd_condition):
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking criteria: {e}")
            return False
    
    def _check_magic_nine_criteria(self, magic_nine_data: Dict[str, Any], 
                                  condition: Dict[str, Any]) -> bool:
        """
        Check Magic Nine Turns criteria
        
        Args:
            magic_nine_data: Magic Nine Turns data
            condition: Filtering condition
            
        Returns:
            True if criteria met
        """
        min_count = condition.get('min_count', 0)
        max_count = condition.get('max_count', 20)
        required_direction = condition.get('direction')  # 'up', 'down', or None
        require_signal = condition.get('require_signal', False)
        
        current_count = magic_nine_data.get('current_count', 0)
        current_direction = magic_nine_data.get('current_direction')
        last_signal = magic_nine_data.get('last_signal')
        
        # Check count range
        if not (min_count <= current_count <= max_count):
            return False
        
        # Check direction
        if required_direction and current_direction != required_direction:
            return False
        
        # Check signal requirement
        if require_signal and not last_signal:
            return False
        
        return True
    
    def _check_macd_criteria(self, macd_data: Dict[str, Any], 
                           condition: Dict[str, Any]) -> bool:
        """
        Check MACD criteria
        
        Args:
            macd_data: MACD data
            condition: Filtering condition
            
        Returns:
            True if criteria met
        """
        require_divergence = condition.get('require_divergence', False)
        divergence_type = condition.get('divergence_type')  # 'bullish', 'bearish', or None
        min_divergence_strength = condition.get('min_divergence_strength', 1)
        trend_filter = condition.get('trend_filter')  # 'bullish', 'bearish', 'neutral', or None
        
        divergences = macd_data.get('divergences', [])
        trend = macd_data.get('trend', 'neutral')
        
        # Check divergence requirement
        if require_divergence and not divergences:
            return False
        
        # Check divergence type and strength
        if divergence_type or min_divergence_strength > 1:
            recent_divergences = divergences[-3:] if divergences else []
            
            matching_divergences = [
                div for div in recent_divergences
                if (not divergence_type or div['type'] == divergence_type) and
                   div.get('strength', 0) >= min_divergence_strength
            ]
            
            if not matching_divergences:
                return False
        
        # Check trend filter
        if trend_filter and trend != trend_filter:
            return False
        
        return True