"""
Input validation utilities for Trading Agent API
Provides secure input validation and sanitization functions
"""

import re
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, date
import logging

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Custom exception for validation errors"""
    pass

class InputValidator:
    """Input validation utilities"""
    
    # Stock code patterns for different markets
    STOCK_CODE_PATTERNS = {
        'SH': re.compile(r'^60\d{4}$'),  # Shanghai A-shares
        'SZ': re.compile(r'^00\d{4}$'),  # Shenzhen A-shares
        'CYB': re.compile(r'^30\d{4}$'), # ChiNext
        'KC': re.compile(r'^68\d{4}$'),  # STAR Market
        'BJ': re.compile(r'^[48]\d{5}$|^[48]\d{5}$'), # Beijing Stock Exchange
    }
    
    @staticmethod
    def validate_stock_code(stock_code: str) -> str:
        """
        Validate and normalize stock code
        
        Args:
            stock_code: Stock code to validate
            
        Returns:
            Normalized stock code
            
        Raises:
            ValidationError: If stock code is invalid
        """
        if not stock_code or not isinstance(stock_code, str):
            raise ValidationError("Stock code must be a non-empty string")
        
        # Remove whitespace and convert to uppercase
        stock_code = stock_code.strip().upper()
        
        # Check length
        if len(stock_code) < 6 or len(stock_code) > 8:
            raise ValidationError("Stock code must be 6-8 characters long")
        
        # Extract numeric part for validation
        numeric_part = re.sub(r'[^0-9]', '', stock_code)
        if len(numeric_part) != 6:
            raise ValidationError("Stock code must contain exactly 6 digits")
        
        # Validate against known patterns
        is_valid = False
        for market, pattern in InputValidator.STOCK_CODE_PATTERNS.items():
            if pattern.match(numeric_part):
                is_valid = True
                break
        
        if not is_valid:
            logger.warning(f"Stock code {stock_code} doesn't match known patterns, but allowing it")
        
        return numeric_part
    
    @staticmethod
    def validate_date_string(date_str: str, format_str: str = '%Y-%m-%d') -> str:
        """
        Validate date string format
        
        Args:
            date_str: Date string to validate
            format_str: Expected date format
            
        Returns:
            Validated date string
            
        Raises:
            ValidationError: If date format is invalid
        """
        if not date_str or not isinstance(date_str, str):
            raise ValidationError("Date must be a non-empty string")
        
        try:
            parsed_date = datetime.strptime(date_str, format_str)
            # Check if date is not in the future
            if parsed_date.date() > date.today():
                raise ValidationError("Date cannot be in the future")
            return date_str
        except ValueError as e:
            raise ValidationError(f"Invalid date format. Expected {format_str}: {e}")
    
    @staticmethod
    def validate_integer(value: Any, min_val: Optional[int] = None, 
                        max_val: Optional[int] = None, name: str = "value") -> int:
        """
        Validate integer value with optional range checking
        
        Args:
            value: Value to validate
            min_val: Minimum allowed value
            max_val: Maximum allowed value
            name: Name of the field for error messages
            
        Returns:
            Validated integer
            
        Raises:
            ValidationError: If value is invalid
        """
        try:
            int_val = int(value)
        except (ValueError, TypeError):
            raise ValidationError(f"{name} must be a valid integer")
        
        if min_val is not None and int_val < min_val:
            raise ValidationError(f"{name} must be at least {min_val}")
        
        if max_val is not None and int_val > max_val:
            raise ValidationError(f"{name} must be at most {max_val}")
        
        return int_val
    
    @staticmethod
    def validate_float(value: Any, min_val: Optional[float] = None, 
                      max_val: Optional[float] = None, name: str = "value") -> float:
        """
        Validate float value with optional range checking
        
        Args:
            value: Value to validate
            min_val: Minimum allowed value
            max_val: Maximum allowed value
            name: Name of the field for error messages
            
        Returns:
            Validated float
            
        Raises:
            ValidationError: If value is invalid
        """
        try:
            float_val = float(value)
        except (ValueError, TypeError):
            raise ValidationError(f"{name} must be a valid number")
        
        if min_val is not None and float_val < min_val:
            raise ValidationError(f"{name} must be at least {min_val}")
        
        if max_val is not None and float_val > max_val:
            raise ValidationError(f"{name} must be at most {max_val}")
        
        return float_val
    
    @staticmethod
    def validate_string(value: Any, min_length: int = 1, max_length: int = 255, 
                       pattern: Optional[str] = None, name: str = "value") -> str:
        """
        Validate string value with length and pattern checking
        
        Args:
            value: Value to validate
            min_length: Minimum string length
            max_length: Maximum string length
            pattern: Regex pattern to match
            name: Name of the field for error messages
            
        Returns:
            Validated string
            
        Raises:
            ValidationError: If value is invalid
        """
        if not isinstance(value, str):
            raise ValidationError(f"{name} must be a string")
        
        # Strip whitespace
        value = value.strip()
        
        if len(value) < min_length:
            raise ValidationError(f"{name} must be at least {min_length} characters long")
        
        if len(value) > max_length:
            raise ValidationError(f"{name} must be at most {max_length} characters long")
        
        if pattern and not re.match(pattern, value):
            raise ValidationError(f"{name} format is invalid")
        
        return value
    
    @staticmethod
    def validate_list(value: Any, item_type: type = str, min_items: int = 0, 
                     max_items: int = 100, name: str = "list") -> List[Any]:
        """
        Validate list with type and size checking
        
        Args:
            value: Value to validate
            item_type: Expected type of list items
            min_items: Minimum number of items
            max_items: Maximum number of items
            name: Name of the field for error messages
            
        Returns:
            Validated list
            
        Raises:
            ValidationError: If value is invalid
        """
        if not isinstance(value, list):
            raise ValidationError(f"{name} must be a list")
        
        if len(value) < min_items:
            raise ValidationError(f"{name} must contain at least {min_items} items")
        
        if len(value) > max_items:
            raise ValidationError(f"{name} must contain at most {max_items} items")
        
        # Validate item types
        for i, item in enumerate(value):
            if not isinstance(item, item_type):
                raise ValidationError(f"{name}[{i}] must be of type {item_type.__name__}")
        
        return value
    
    @staticmethod
    def sanitize_sql_input(value: str) -> str:
        """
        Basic SQL injection prevention
        
        Args:
            value: String to sanitize
            
        Returns:
            Sanitized string
        """
        if not isinstance(value, str):
            return str(value)
        
        # Remove or escape potentially dangerous characters
        dangerous_chars = ["'", '"', ';', '--', '/*', '*/', 'xp_', 'sp_']
        sanitized = value
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        
        return sanitized.strip()

def validate_request_data(data: Dict[str, Any], schema: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    """
    Validate request data against a schema
    
    Args:
        data: Request data to validate
        schema: Validation schema
        
    Returns:
        Validated data
        
    Raises:
        ValidationError: If validation fails
    """
    validated_data = {}
    validator = InputValidator()
    
    for field, rules in schema.items():
        value = data.get(field)
        
        # Check required fields
        if rules.get('required', False) and value is None:
            raise ValidationError(f"Field '{field}' is required")
        
        # Skip validation for optional fields that are None
        if value is None:
            continue
        
        # Apply validation based on type
        field_type = rules.get('type', 'string')
        
        try:
            if field_type == 'string':
                validated_data[field] = validator.validate_string(
                    value,
                    min_length=rules.get('min_length', 1),
                    max_length=rules.get('max_length', 255),
                    pattern=rules.get('pattern'),
                    name=field
                )
            elif field_type == 'integer':
                validated_data[field] = validator.validate_integer(
                    value,
                    min_val=rules.get('min_val'),
                    max_val=rules.get('max_val'),
                    name=field
                )
            elif field_type == 'float':
                validated_data[field] = validator.validate_float(
                    value,
                    min_val=rules.get('min_val'),
                    max_val=rules.get('max_val'),
                    name=field
                )
            elif field_type == 'stock_code':
                validated_data[field] = validator.validate_stock_code(value)
            elif field_type == 'date':
                validated_data[field] = validator.validate_date_string(value)
            elif field_type == 'list':
                validated_data[field] = validator.validate_list(
                    value,
                    item_type=rules.get('item_type', str),
                    min_items=rules.get('min_items', 0),
                    max_items=rules.get('max_items', 100),
                    name=field
                )
            else:
                validated_data[field] = value
                
        except ValidationError as e:
            raise ValidationError(f"Validation failed for field '{field}': {e}")
    
    return validated_data
