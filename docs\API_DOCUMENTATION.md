# Trading Agent API Documentation

## Overview

The Trading Agent API provides comprehensive stock market analysis capabilities for Chinese stock markets, including technical analysis, fundamental analysis, market sentiment, and risk management features.

**Base URL**: `http://localhost:5000/api`

**Version**: 2.0.0

## Authentication

Currently, the API does not require authentication for most endpoints. Future versions will include JWT-based authentication for user-specific features.

## Response Format

All API responses follow a consistent format:

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "error": null,
  "message": "Optional message"
}
```

For errors:

```json
{
  "success": false,
  "error": "Error type",
  "message": "Detailed error message"
}
```

## Endpoints

### Health Check

#### GET /health

Check API server health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2023-12-01T10:00:00Z",
  "version": "2.0.0",
  "components": {
    "database": "connected",
    "data_provider": "ready"
  }
}
```

### Stock Information

#### GET /stock/info/{stock_code}

Get comprehensive stock information including price data and technical analysis.

**Parameters:**
- `stock_code` (path): Stock code (e.g., "000001", "600036")
- `period` (query): Data period - "daily", "weekly", "monthly" (default: "daily")
- `days` (query): Number of days to fetch (default: 100, max: 1000)

**Example Request:**
```
GET /api/stock/info/000001?period=daily&days=100
```

**Response:**
```json
{
  "success": true,
  "data": {
    "code": "000001",
    "name": "平安银行",
    "price_data": [
      {
        "date": "2023-12-01",
        "open": 10.50,
        "high": 10.80,
        "low": 10.30,
        "close": 10.65,
        "volume": 1500000
      }
    ],
    "magic_nine": {
      "signals": [
        {
          "type": "buy",
          "count": 9,
          "date": "2023-12-01"
        }
      ]
    },
    "macd_data": {
      "macd": 0.25,
      "signal": 0.20,
      "histogram": 0.05
    },
    "signals": [
      {
        "type": "buy",
        "strength": 0.8,
        "description": "Strong buy signal from Magic Nine Turns"
      }
    ],
    "last_updated": "2023-12-01T10:00:00Z"
  }
}
```

### Technical Analysis

#### GET /technical/indicators/{stock_code}

Get technical indicators for a specific stock.

**Parameters:**
- `stock_code` (path): Stock code
- `indicators` (query): Comma-separated list of indicators (optional)
  - Available: "RSI", "MACD", "KDJ", "BOLL", "MAGIC_NINE"
- `timeframe` (query): Time frame - "1d", "1w", "1M" (default: "1d")

**Example Request:**
```
GET /api/technical/indicators/000001?indicators=RSI,MACD&timeframe=1d
```

**Response:**
```json
{
  "success": true,
  "data": {
    "stock_code": "000001",
    "indicators": {
      "rsi": 65.5,
      "macd": {
        "macd": 0.25,
        "signal": 0.20,
        "histogram": 0.05
      },
      "kdj": {
        "k": 75.2,
        "d": 68.9,
        "j": 87.8
      }
    }
  }
}
```

#### GET /technical/signals/{stock_code}

Get trading signals based on technical analysis.

**Parameters:**
- `stock_code` (path): Stock code
- `timeframe` (query): Time frame (default: "1d")

**Response:**
```json
{
  "success": true,
  "data": {
    "stock_code": "000001",
    "signals": [
      {
        "type": "buy",
        "strength": 0.8,
        "confidence": 0.75,
        "description": "Strong bullish signal",
        "indicators": ["magic_nine", "macd"],
        "timestamp": "2023-12-01T10:00:00Z"
      }
    ]
  }
}
```

### Analysis

#### POST /analysis/calculate

Calculate technical analysis for given parameters.

**Request Body:**
```json
{
  "stock_code": "000001",
  "indicators": ["magic_nine", "macd"],
  "start_date": "2023-01-01",
  "end_date": "2023-12-01"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "magic_nine": {
      "signals": [...]
    },
    "macd": {
      "macd": 0.25,
      "signal": 0.20,
      "histogram": 0.05
    }
  },
  "signals": [
    {
      "type": "buy",
      "strength": 0.8
    }
  ]
}
```

### Stock Screening

#### POST /screening/filter

Filter stocks based on technical analysis criteria.

**Request Body:**
```json
{
  "magic_nine_condition": {
    "signal_type": "buy",
    "min_count": 8
  },
  "macd_condition": {
    "histogram": "> 0",
    "signal_cross": "bullish"
  },
  "market_cap_range": {
    "min": 1000000000,
    "max": 100000000000
  },
  "industry_filter": ["银行", "保险"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_count": 25,
    "stocks": [
      {
        "code": "000001",
        "name": "平安银行",
        "price": 10.65,
        "change_pct": 2.5,
        "signals": [...],
        "signal_strength": 0.8
      }
    ],
    "execution_time": 2.5
  }
}
```

### Stock Lists

#### GET /stocks/list

Get list of available stocks.

**Parameters:**
- `market` (query): Market filter - "SZ", "SH", "all" (default: "all")
- `limit` (query): Maximum number of stocks (default: 100)

**Response:**
```json
{
  "success": true,
  "data": {
    "stocks": [
      {
        "code": "000001",
        "name": "平安银行",
        "market": "SZ",
        "industry": "银行"
      }
    ],
    "count": 100
  }
}
```

### Fundamental Analysis

#### GET /fundamental/analysis/{stock_code}

Get fundamental analysis data for a stock.

**Response:**
```json
{
  "success": true,
  "data": {
    "stock_code": "000001",
    "financial_health": {
      "debt_ratio": 0.65,
      "current_ratio": 1.2,
      "roe": 0.15
    },
    "valuation": {
      "pe_ratio": 8.5,
      "pb_ratio": 0.9,
      "ps_ratio": 2.1
    },
    "industry_analysis": {
      "industry_pe": 9.2,
      "industry_pb": 1.1,
      "rank_in_industry": 5
    }
  }
}
```

### Market Sentiment

#### GET /sentiment/overall

Get overall market sentiment data.

**Response:**
```json
{
  "success": true,
  "data": {
    "sentiment_score": 0.65,
    "fear_greed_index": 72,
    "market_trend": "bullish",
    "confidence_level": "high"
  }
}
```

#### GET /sentiment/capital-flow

Get capital flow data.

**Response:**
```json
{
  "success": true,
  "data": {
    "net_inflow": **********,
    "main_force_flow": 800000000,
    "retail_flow": 700000000,
    "sectors": [
      {
        "name": "银行",
        "flow": 200000000,
        "change_pct": 5.2
      }
    ]
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400  | Bad Request - Invalid input parameters |
| 404  | Not Found - Resource not found |
| 429  | Too Many Requests - Rate limit exceeded |
| 500  | Internal Server Error - Server error |
| 503  | Service Unavailable - External service unavailable |

## Rate Limiting

The API implements rate limiting to ensure fair usage:
- Default: 100 requests per hour per IP
- Configurable via environment variables

## Data Sources

- **Stock Data**: akshare library
- **Technical Indicators**: Custom implementations using TA-Lib
- **Fundamental Data**: Multiple financial data providers
- **Market Sentiment**: Aggregated from various sources

## Caching

The API implements intelligent caching:
- Stock data: 5 minutes TTL
- Technical indicators: 5 minutes TTL
- Market data: 10 minutes TTL
- Fundamental data: 1 hour TTL

## WebSocket Support (Future)

Real-time data streaming will be available via WebSocket connections:
- Real-time price updates
- Live signal notifications
- Market event streams

## SDK and Libraries

Official SDKs will be available for:
- Python
- JavaScript/TypeScript
- Java

## Support

For API support and questions:
- Documentation: [API Docs](./API_DOCUMENTATION.md)
- Issues: GitHub Issues
- Email: <EMAIL>
