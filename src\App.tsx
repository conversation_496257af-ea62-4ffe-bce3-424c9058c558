import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Home from "@/pages/Home";
import StockAnalysis from "@/pages/StockAnalysis";
import StockScreening from "@/pages/StockScreening";
import UserCenter from "@/pages/UserCenter";

export default function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/analysis" element={<StockAnalysis />} />
        <Route path="/screening" element={<StockScreening />} />
        <Route path="/user" element={<UserCenter />} />
      </Routes>
    </Router>
  );
}
