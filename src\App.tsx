import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Home from "@/pages/Home";
import StockAnalysis from "@/pages/StockAnalysis";
import TechnicalAnalysis from "@/pages/TechnicalAnalysis";
import FundamentalAnalysis from "@/pages/FundamentalAnalysis";
import MarketSentiment from "@/pages/MarketSentiment";
import StrategyBuilder from "@/pages/StrategyBuilder";
import RiskManagement from "@/pages/RiskManagement";
import StockScreening from "@/pages/StockScreening";
import UserCenter from "@/pages/UserCenter";

export default function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/analysis" element={<StockAnalysis />} />
        <Route path="/technical" element={<TechnicalAnalysis />} />
        <Route path="/fundamental" element={<FundamentalAnalysis />} />
        <Route path="/sentiment" element={<MarketSentiment />} />
        <Route path="/strategy" element={<StrategyBuilder />} />
        <Route path="/risk" element={<RiskManagement />} />
        <Route path="/screening" element={<StockScreening />} />
        <Route path="/user" element={<UserCenter />} />
      </Routes>
    </Router>
  );
}
