import React, { useState, useEffect, useMemo } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
  ScatterController,
  Bar<PERSON><PERSON>roller,
  LineController,
} from 'chart.js';
import { Line, Bar, Chart as ChartComponent } from 'react-chartjs-2';
import { CandlestickController, CandlestickElement, OhlcController, OhlcElement } from 'chartjs-chart-financial';
import 'chartjs-adapter-date-fns';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ScatterController,
  Bar<PERSON><PERSON>roller,
  LineController,
  CandlestickController,
  CandlestickElement,
  OhlcController,
  OhlcElement
);

// Technical indicator types
interface TechnicalIndicator {
  name: string;
  enabled: boolean;
  color?: string;
  yAxisID?: string;
}

interface CandlestickData {
  x: string | Date;
  o: number; // open
  h: number; // high
  l: number; // low
  c: number; // close
}

interface VolumeData {
  x: string | Date;
  y: number;
}

interface IndicatorData {
  x: string | Date;
  y: number;
}

interface TechnicalChartProps {
  stockCode: string;
  candlestickData: CandlestickData[];
  volumeData: VolumeData[];
  indicators: {
    macd?: {
      macd: IndicatorData[];
      signal: IndicatorData[];
      histogram: IndicatorData[];
    };
    rsi?: IndicatorData[];
    kdj?: {
      k: IndicatorData[];
      d: IndicatorData[];
      j: IndicatorData[];
    };
    bollingerBands?: {
      upper: IndicatorData[];
      middle: IndicatorData[];
      lower: IndicatorData[];
    };
    magicNine?: {
      signals: Array<{
        x: string | Date;
        type: 'buy' | 'sell';
        count: number;
      }>;
    };
  };
  enabledIndicators: TechnicalIndicator[];
  height?: number;
  onIndicatorToggle?: (indicator: string, enabled: boolean) => void;
}

interface ChartProps {
  type: 'line' | 'bar';
  data: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      borderColor?: string;
      backgroundColor?: string | string[];
      borderWidth?: number;
      fill?: boolean;
      yAxisID?: string;
    }[];
  };
  title: string;
  height?: number;
}

// Technical Chart Component with Multiple Indicators
const TechnicalChart: React.FC<TechnicalChartProps> = ({
  stockCode,
  candlestickData,
  volumeData,
  indicators,
  enabledIndicators,
  height = 600,
  onIndicatorToggle
}) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('1D');
  const [showVolume, setShowVolume] = useState(true);

  // Build chart datasets based on enabled indicators
  const chartData = useMemo(() => {
    const datasets: any[] = [];

    // Candlestick data
    datasets.push({
      label: `${stockCode} 价格`,
      type: 'candlestick',
      data: candlestickData,
      borderColor: '#1f77b4',
      backgroundColor: 'rgba(31, 119, 180, 0.1)',
      yAxisID: 'price',
    });

    // Volume data
    if (showVolume) {
      datasets.push({
        label: '成交量',
        type: 'bar',
        data: volumeData,
        backgroundColor: 'rgba(255, 99, 132, 0.3)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1,
        yAxisID: 'volume',
      });
    }

    // MACD Indicator
    if (indicators.macd && enabledIndicators.find(ind => ind.name === 'MACD')?.enabled) {
      datasets.push(
        {
          label: 'MACD',
          type: 'line',
          data: indicators.macd.macd,
          borderColor: '#ff7f0e',
          backgroundColor: 'transparent',
          borderWidth: 2,
          pointRadius: 0,
          yAxisID: 'macd',
        },
        {
          label: 'Signal',
          type: 'line',
          data: indicators.macd.signal,
          borderColor: '#2ca02c',
          backgroundColor: 'transparent',
          borderWidth: 2,
          pointRadius: 0,
          yAxisID: 'macd',
        },
        {
          label: 'Histogram',
          type: 'bar',
          data: indicators.macd.histogram,
          backgroundColor: 'rgba(214, 39, 40, 0.5)',
          borderColor: '#d62728',
          borderWidth: 1,
          yAxisID: 'macd',
        }
      );
    }

    // RSI Indicator
    if (indicators.rsi && enabledIndicators.find(ind => ind.name === 'RSI')?.enabled) {
      datasets.push({
        label: 'RSI',
        type: 'line',
        data: indicators.rsi,
        borderColor: '#9467bd',
        backgroundColor: 'transparent',
        borderWidth: 2,
        pointRadius: 0,
        yAxisID: 'rsi',
      });
    }

    // KDJ Indicator
    if (indicators.kdj && enabledIndicators.find(ind => ind.name === 'KDJ')?.enabled) {
      datasets.push(
        {
          label: 'K',
          type: 'line',
          data: indicators.kdj.k,
          borderColor: '#8c564b',
          backgroundColor: 'transparent',
          borderWidth: 2,
          pointRadius: 0,
          yAxisID: 'kdj',
        },
        {
          label: 'D',
          type: 'line',
          data: indicators.kdj.d,
          borderColor: '#e377c2',
          backgroundColor: 'transparent',
          borderWidth: 2,
          pointRadius: 0,
          yAxisID: 'kdj',
        },
        {
          label: 'J',
          type: 'line',
          data: indicators.kdj.j,
          borderColor: '#7f7f7f',
          backgroundColor: 'transparent',
          borderWidth: 2,
          pointRadius: 0,
          yAxisID: 'kdj',
        }
      );
    }

    // Bollinger Bands
    if (indicators.bollingerBands && enabledIndicators.find(ind => ind.name === 'Bollinger Bands')?.enabled) {
      datasets.push(
        {
          label: 'BB Upper',
          type: 'line',
          data: indicators.bollingerBands.upper,
          borderColor: '#bcbd22',
          backgroundColor: 'transparent',
          borderWidth: 1,
          pointRadius: 0,
          yAxisID: 'price',
        },
        {
          label: 'BB Middle',
          type: 'line',
          data: indicators.bollingerBands.middle,
          borderColor: '#17becf',
          backgroundColor: 'transparent',
          borderWidth: 1,
          pointRadius: 0,
          yAxisID: 'price',
        },
        {
          label: 'BB Lower',
          type: 'line',
          data: indicators.bollingerBands.lower,
          borderColor: '#bcbd22',
          backgroundColor: 'transparent',
          borderWidth: 1,
          pointRadius: 0,
          yAxisID: 'price',
        }
      );
    }

    return { datasets };
  }, [candlestickData, volumeData, indicators, enabledIndicators, showVolume, stockCode]);

  const chartOptions: ChartOptions<any> = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          filter: (legendItem) => {
            return legendItem.text !== undefined;
          },
        },
      },
      title: {
        display: true,
        text: `${stockCode} 技术分析图表`,
        font: {
          size: 18,
          weight: 'bold',
        },
      },
      tooltip: {
        callbacks: {
          title: (context) => {
            return context[0]?.label || '';
          },
          label: (context) => {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            return `${label}: ${typeof value === 'number' ? value.toFixed(2) : value}`;
          },
        },
      },
    },
    scales: {
      x: {
        type: 'time',
        time: {
          unit: 'day',
          displayFormats: {
            day: 'MM-dd',
          },
        },
        title: {
          display: true,
          text: '时间',
        },
      },
      price: {
        type: 'linear',
        display: true,
        position: 'left',
        title: {
          display: true,
          text: '价格 (元)',
        },
      },
      volume: {
        type: 'linear',
        display: showVolume,
        position: 'right',
        title: {
          display: true,
          text: '成交量',
        },
        grid: {
          drawOnChartArea: false,
        },
        max: Math.max(...volumeData.map(d => d.y)) * 4, // Scale volume to 1/4 of chart
      },
      macd: {
        type: 'linear',
        display: enabledIndicators.find(ind => ind.name === 'MACD')?.enabled || false,
        position: 'right',
        title: {
          display: true,
          text: 'MACD',
        },
        grid: {
          drawOnChartArea: false,
        },
      },
      rsi: {
        type: 'linear',
        display: enabledIndicators.find(ind => ind.name === 'RSI')?.enabled || false,
        position: 'right',
        title: {
          display: true,
          text: 'RSI',
        },
        min: 0,
        max: 100,
        grid: {
          drawOnChartArea: false,
        },
      },
      kdj: {
        type: 'linear',
        display: enabledIndicators.find(ind => ind.name === 'KDJ')?.enabled || false,
        position: 'right',
        title: {
          display: true,
          text: 'KDJ',
        },
        min: 0,
        max: 100,
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      {/* Chart Controls */}
      <div className="flex flex-wrap items-center justify-between mb-4 gap-4">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold text-gray-800">{stockCode} 技术分析</h3>
          
          {/* Timeframe Selector */}
          <div className="flex space-x-2">
            {['1D', '5D', '1M', '3M', '6M', '1Y'].map((timeframe) => (
              <button
                key={timeframe}
                onClick={() => setSelectedTimeframe(timeframe)}
                className={`px-3 py-1 text-sm rounded ${
                  selectedTimeframe === timeframe
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {timeframe}
              </button>
            ))}
          </div>
        </div>

        {/* Indicator Controls */}
        <div className="flex flex-wrap items-center space-x-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={showVolume}
              onChange={(e) => setShowVolume(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-gray-700">成交量</span>
          </label>
          
          {enabledIndicators.map((indicator) => (
            <label key={indicator.name} className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={indicator.enabled}
                onChange={(e) => onIndicatorToggle?.(indicator.name, e.target.checked)}
                className="rounded"
              />
              <span className="text-sm text-gray-700">{indicator.name}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Chart */}
      <div style={{ height: `${height}px` }}>
        <ChartComponent type="line" data={chartData} options={chartOptions} />
      </div>

      {/* Magic Nine Turns Signals */}
      {indicators.magicNine && enabledIndicators.find(ind => ind.name === 'Magic Nine')?.enabled && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-md font-semibold text-gray-800 mb-2">神奇九转信号</h4>
          <div className="flex flex-wrap gap-2">
            {indicators.magicNine.signals.map((signal, index) => (
              <div
                key={index}
                className={`px-3 py-1 rounded-full text-sm ${
                  signal.type === 'buy'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {signal.type === 'buy' ? '买入' : '卖出'} 信号 ({signal.count})
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// Simple Chart Component (for backward compatibility)
const Chart: React.FC<ChartProps> = ({ type, data, title, height = 400 }) => {
  const options: ChartOptions<'line' | 'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: title,
        font: {
          size: 16,
          weight: 'bold',
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: '日期',
        },
      },
      y: {
        type: 'linear' as const,
        display: true,
        position: 'left' as const,
        title: {
          display: true,
          text: '价格 (元)',
        },
      },
    },
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-md" style={{ height: `${height}px` }}>
      {type === 'line' ? (
        <Line data={data} options={options} />
      ) : (
        <Bar data={data} options={options} />
      )}
    </div>
  );
};

export default Chart;
export { TechnicalChart };