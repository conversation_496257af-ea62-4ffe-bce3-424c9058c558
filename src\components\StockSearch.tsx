import React, { useState, useRef, useEffect } from 'react';
import { Search, Star, TrendingUp, TrendingDown } from 'lucide-react';
import { useStockList } from '@/hooks/useApi';
import { useCurrentStock, useStockActions, usePreferences, usePreferenceActions } from '@/store/useAppStore';
import { LoadingSpinner } from './ui/LoadingSpinner';
import { cn } from '@/lib/utils';

interface StockSearchProps {
  onStockSelect?: (stockCode: string) => void;
  placeholder?: string;
  className?: string;
}

export const StockSearch: React.FC<StockSearchProps> = ({
  onStockSelect,
  placeholder = "输入股票代码或名称搜索",
  className,
}) => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const { code: currentStockCode } = useCurrentStock();
  const { setCurrentStock } = useStockActions();
  const { favoriteStocks } = usePreferences();
  const { addFavoriteStock, removeFavoriteStock } = usePreferenceActions();

  const { data: stockListData, loading } = useStockList('all', 50, {
    immediate: true,
  });

  const stocks = stockListData?.stocks || [];

  // Filter stocks based on query
  const filteredStocks = stocks.filter(stock =>
    stock.code.toLowerCase().includes(query.toLowerCase()) ||
    stock.name.toLowerCase().includes(query.toLowerCase())
  );

  // Show favorites first if no query
  const displayStocks = query.trim() === '' 
    ? [
        ...stocks.filter(stock => favoriteStocks.includes(stock.code)),
        ...stocks.filter(stock => !favoriteStocks.includes(stock.code))
      ].slice(0, 10)
    : filteredStocks.slice(0, 10);

  const handleStockSelect = (stockCode: string) => {
    setCurrentStock(stockCode);
    setQuery('');
    setIsOpen(false);
    setSelectedIndex(-1);
    onStockSelect?.(stockCode);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < displayStocks.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && displayStocks[selectedIndex]) {
          handleStockSelect(displayStocks[selectedIndex].code);
        } else if (query.trim()) {
          // Try to use query as stock code directly
          handleStockSelect(query.trim().toUpperCase());
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const toggleFavorite = (stockCode: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (favoriteStocks.includes(stockCode)) {
      removeFavoriteStock(stockCode);
    } else {
      addFavoriteStock(stockCode);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className={cn('relative', className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => {
            setQuery(e.target.value);
            setIsOpen(true);
            setSelectedIndex(-1);
          }}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto"
        >
          {loading && (
            <div className="p-4 text-center">
              <LoadingSpinner size="sm" text="搜索中..." />
            </div>
          )}

          {!loading && displayStocks.length === 0 && query.trim() && (
            <div className="p-4 text-center text-gray-500">
              <p>未找到匹配的股票</p>
              <button
                onClick={() => handleStockSelect(query.trim().toUpperCase())}
                className="mt-2 text-blue-600 hover:text-blue-700 text-sm"
              >
                直接搜索 "{query.trim().toUpperCase()}"
              </button>
            </div>
          )}

          {!loading && displayStocks.length === 0 && !query.trim() && (
            <div className="p-4 text-center text-gray-500">
              <p>开始输入以搜索股票</p>
            </div>
          )}

          {!loading && displayStocks.map((stock, index) => {
            const isFavorite = favoriteStocks.includes(stock.code);
            const isSelected = index === selectedIndex;
            const isCurrent = stock.code === currentStockCode;

            return (
              <div
                key={stock.code}
                onClick={() => handleStockSelect(stock.code)}
                className={cn(
                  'flex items-center justify-between p-3 cursor-pointer border-b border-gray-100 last:border-b-0',
                  isSelected && 'bg-blue-50',
                  isCurrent && 'bg-green-50',
                  'hover:bg-gray-50'
                )}
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">{stock.code}</span>
                    {isCurrent && (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">
                        当前
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 truncate">{stock.name}</p>
                  {stock.change_pct !== undefined && (
                    <div className="flex items-center space-x-1 mt-1">
                      {stock.change_pct > 0 ? (
                        <TrendingUp className="w-3 h-3 text-red-500" />
                      ) : stock.change_pct < 0 ? (
                        <TrendingDown className="w-3 h-3 text-green-500" />
                      ) : null}
                      <span
                        className={cn(
                          'text-xs',
                          stock.change_pct > 0 ? 'text-red-500' : 
                          stock.change_pct < 0 ? 'text-green-500' : 'text-gray-500'
                        )}
                      >
                        {stock.change_pct > 0 ? '+' : ''}{stock.change_pct?.toFixed(2)}%
                      </span>
                    </div>
                  )}
                </div>
                <button
                  onClick={(e) => toggleFavorite(stock.code, e)}
                  className={cn(
                    'p-1 rounded hover:bg-gray-200',
                    isFavorite ? 'text-yellow-500' : 'text-gray-400'
                  )}
                >
                  <Star className={cn('w-4 h-4', isFavorite && 'fill-current')} />
                </button>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
