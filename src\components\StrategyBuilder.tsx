import React, { useState, useEffect, useCallback } from 'react';
import { Plus, Trash2, Play, Save, Download, Upload, Settings, TrendingUp, TrendingDown, BarChart3, Activity, Volume2, Target, AlertTriangle, CheckCircle } from 'lucide-react';

// Simple toast implementation
const toast = {
  success: (message: string) => console.log('✅', message),
  error: (message: string) => console.error('❌', message)
};

// Types for strategy building
interface Rule {
  id: string;
  type: 'entry' | 'exit';
  indicator: string;
  condition: string;
  value: number | number[];
  weight?: number;
  required_trend?: string;
  required_volatility?: string;
  required_volume?: string;
}

interface Strategy {
  id?: string;
  name: string;
  description: string;
  entry_rules: Rule[];
  exit_rules: Rule[];
  position_sizing: {
    method: string;
    value: number;
  };
  risk_management: {
    stop_loss?: number;
    take_profit?: number;
    max_position_size?: number;
  };
  timeframe: string;
}

interface BacktestResult {
  total_return: number;
  sharpe_ratio: number;
  max_drawdown: number;
  win_rate: number;
  total_trades: number;
  profit_factor: number;
  equity_curve: Array<{ date: string; value: number }>;
  trades: Array<any>;
  performance_metrics: any;
}

// Available indicators with their configurations
const INDICATORS = {
  'Technical': [
    { id: 'rsi', name: 'RSI', description: 'Relative Strength Index', icon: Activity },
    { id: 'macd', name: 'MACD', description: 'Moving Average Convergence Divergence', icon: TrendingUp },
    { id: 'kdj_k', name: 'KDJ-K', description: 'KDJ K Line', icon: BarChart3 },
    { id: 'kdj_d', name: 'KDJ-D', description: 'KDJ D Line', icon: BarChart3 },
    { id: 'kdj_j', name: 'KDJ-J', description: 'KDJ J Line', icon: BarChart3 },
    { id: 'sma_5', name: 'SMA(5)', description: '5-day Simple Moving Average', icon: TrendingUp },
    { id: 'sma_20', name: 'SMA(20)', description: '20-day Simple Moving Average', icon: TrendingUp },
    { id: 'ema_12', name: 'EMA(12)', description: '12-day Exponential Moving Average', icon: TrendingUp },
    { id: 'bb_upper', name: 'BB Upper', description: 'Bollinger Band Upper', icon: BarChart3 },
    { id: 'bb_lower', name: 'BB Lower', description: 'Bollinger Band Lower', icon: BarChart3 },
    { id: 'atr', name: 'ATR', description: 'Average True Range', icon: Activity },
    { id: 'adx', name: 'ADX', description: 'Average Directional Index', icon: TrendingUp }
  ],
  'Volume': [
    { id: 'volume', name: 'Volume', description: 'Trading Volume', icon: Volume2 },
    { id: 'volume_sma', name: 'Volume SMA', description: 'Volume Moving Average', icon: Volume2 }
  ],
  'Price': [
    { id: 'price', name: 'Price', description: 'Current Price', icon: Target },
    { id: 'high', name: 'High', description: 'Daily High', icon: TrendingUp },
    { id: 'low', name: 'Low', description: 'Daily Low', icon: TrendingDown }
  ]
};

const CONDITIONS = [
  { id: 'greater_than', name: '>', description: 'Greater than' },
  { id: 'less_than', name: '<', description: 'Less than' },
  { id: 'equals', name: '=', description: 'Equals' },
  { id: 'crosses_above', name: '↗', description: 'Crosses above' },
  { id: 'crosses_below', name: '↘', description: 'Crosses below' },
  { id: 'between', name: '⟷', description: 'Between' }
];

const POSITION_SIZING_METHODS = [
  { id: 'fixed', name: 'Fixed Amount', description: 'Fixed dollar amount' },
  { id: 'percent', name: 'Percentage', description: 'Percentage of portfolio' },
  { id: 'volatility', name: 'Volatility-based', description: 'Based on ATR volatility' },
  { id: 'kelly', name: 'Kelly Criterion', description: 'Optimal position sizing' }
];

const STRATEGY_TEMPLATES = [
  {
    id: 'rsi_oversold',
    name: 'RSI Oversold',
    description: 'Buy when RSI < 30, sell when RSI > 70',
    entry_rules: [{ id: '1', type: 'entry', indicator: 'rsi', condition: 'less_than', value: 30, weight: 1.0 }],
    exit_rules: [{ id: '2', type: 'exit', indicator: 'rsi', condition: 'greater_than', value: 70, weight: 1.0 }]
  },
  {
    id: 'macd_crossover',
    name: 'MACD Crossover',
    description: 'Buy on MACD bullish crossover, sell on bearish crossover',
    entry_rules: [{ id: '1', type: 'entry', indicator: 'macd', condition: 'crosses_above', value: 0, weight: 1.0 }],
    exit_rules: [{ id: '2', type: 'exit', indicator: 'macd', condition: 'crosses_below', value: 0, weight: 1.0 }]
  },
  {
    id: 'kdj_strategy',
    name: 'KDJ Strategy',
    description: 'Chinese market KDJ indicator strategy',
    entry_rules: [
      { id: '1', type: 'entry', indicator: 'kdj_k', condition: 'less_than', value: 20, weight: 0.6 },
      { id: '2', type: 'entry', indicator: 'kdj_d', condition: 'less_than', value: 20, weight: 0.4 }
    ],
    exit_rules: [
      { id: '3', type: 'exit', indicator: 'kdj_k', condition: 'greater_than', value: 80, weight: 0.6 },
      { id: '4', type: 'exit', indicator: 'kdj_d', condition: 'greater_than', value: 80, weight: 0.4 }
    ]
  }
];

export default function StrategyBuilder() {
  const [strategy, setStrategy] = useState<Strategy>({
    name: '',
    description: '',
    entry_rules: [],
    exit_rules: [],
    position_sizing: { method: 'percent', value: 10 },
    risk_management: { stop_loss: 5, take_profit: 15 },
    timeframe: '1d'
  });

  const [backtestResult, setBacktestResult] = useState<BacktestResult | null>(null);
  const [isBacktesting, setIsBacktesting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [showTemplates, setShowTemplates] = useState(false);
  const [activeTab, setActiveTab] = useState<'builder' | 'backtest' | 'settings'>('builder');
  const [draggedItem, setDraggedItem] = useState<any>(null);

  // Load strategy templates
  const loadTemplate = useCallback((templateId: string) => {
    const template = STRATEGY_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      setStrategy(prev => ({
        ...prev,
        name: template.name,
        description: template.description,
        entry_rules: template.entry_rules.map(rule => ({ ...rule, id: Math.random().toString(36).substr(2, 9) })),
        exit_rules: template.exit_rules.map(rule => ({ ...rule, id: Math.random().toString(36).substr(2, 9) }))
      }));
      setSelectedTemplate(templateId);
      setShowTemplates(false);
      toast.success(`Loaded template: ${template.name}`);
    }
  }, []);

  // Add new rule
  const addRule = useCallback((type: 'entry' | 'exit') => {
    const newRule: Rule = {
      id: Math.random().toString(36).substr(2, 9),
      type,
      indicator: 'rsi',
      condition: 'greater_than',
      value: 50,
      weight: 1.0
    };

    setStrategy(prev => ({
      ...prev,
      [type === 'entry' ? 'entry_rules' : 'exit_rules']: [
        ...prev[type === 'entry' ? 'entry_rules' : 'exit_rules'],
        newRule
      ]
    }));
  }, []);

  // Update rule
  const updateRule = useCallback((ruleId: string, updates: Partial<Rule>) => {
    setStrategy(prev => ({
      ...prev,
      entry_rules: prev.entry_rules.map(rule => 
        rule.id === ruleId ? { ...rule, ...updates } : rule
      ),
      exit_rules: prev.exit_rules.map(rule => 
        rule.id === ruleId ? { ...rule, ...updates } : rule
      )
    }));
  }, []);

  // Delete rule
  const deleteRule = useCallback((ruleId: string) => {
    setStrategy(prev => ({
      ...prev,
      entry_rules: prev.entry_rules.filter(rule => rule.id !== ruleId),
      exit_rules: prev.exit_rules.filter(rule => rule.id !== ruleId)
    }));
  }, []);

  // Move rule up or down
  const moveRule = useCallback((ruleId: string, direction: 'up' | 'down') => {
    setStrategy(prev => {
      const moveInArray = (rules: Rule[]) => {
        const index = rules.findIndex(rule => rule.id === ruleId);
        if (index === -1) return rules;
        
        const newIndex = direction === 'up' ? index - 1 : index + 1;
        if (newIndex < 0 || newIndex >= rules.length) return rules;
        
        const newRules = [...rules];
        [newRules[index], newRules[newIndex]] = [newRules[newIndex], newRules[index]];
        return newRules;
      };
      
      return {
        ...prev,
        entry_rules: moveInArray(prev.entry_rules),
        exit_rules: moveInArray(prev.exit_rules)
      };
    });
  }, []);

  // Run backtest
  const runBacktest = useCallback(async () => {
    if (strategy.entry_rules.length === 0) {
      toast.error('Please add at least one entry rule');
      return;
    }

    setIsBacktesting(true);
    try {
      const response = await fetch('/api/strategy/backtest', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          strategy,
          symbol: '000001.SZ', // Default to a Chinese stock
          start_date: '2023-01-01',
          end_date: '2024-01-01'
        })
      });

      if (!response.ok) {
        throw new Error('Backtest failed');
      }

      const result = await response.json();
      setBacktestResult(result);
      setActiveTab('backtest');
      toast.success('Backtest completed successfully!');
    } catch (error) {
      console.error('Backtest error:', error);
      toast.error('Backtest failed. Please check your strategy configuration.');
    } finally {
      setIsBacktesting(false);
    }
  }, [strategy]);

  // Save strategy
  const saveStrategy = useCallback(async () => {
    if (!strategy.name.trim()) {
      toast.error('Please enter a strategy name');
      return;
    }

    setIsSaving(true);
    try {
      const response = await fetch('/api/strategy', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(strategy)
      });

      if (!response.ok) {
        throw new Error('Failed to save strategy');
      }

      const savedStrategy = await response.json();
      setStrategy(savedStrategy);
      toast.success('Strategy saved successfully!');
    } catch (error) {
      console.error('Save error:', error);
      toast.error('Failed to save strategy');
    } finally {
      setIsSaving(false);
    }
  }, [strategy]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Strategy Builder</h1>
              <p className="text-gray-600">Create and test your trading strategies with advanced technical indicators</p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowTemplates(true)}
                className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors flex items-center space-x-2"
              >
                <Download className="w-4 h-4" />
                <span>Templates</span>
              </button>
              <button
                onClick={saveStrategy}
                disabled={isSaving}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
              >
                <Save className="w-4 h-4" />
                <span>{isSaving ? 'Saving...' : 'Save'}</span>
              </button>
              <button
                onClick={runBacktest}
                disabled={isBacktesting}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2 disabled:opacity-50"
              >
                <Play className="w-4 h-4" />
                <span>{isBacktesting ? 'Testing...' : 'Backtest'}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-xl shadow-lg mb-6">
          <div className="flex border-b border-gray-200">
            {[
              { id: 'builder', name: 'Strategy Builder', icon: Settings },
              { id: 'backtest', name: 'Backtest Results', icon: BarChart3 },
              { id: 'settings', name: 'Settings', icon: Target }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 px-6 py-4 font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Strategy Builder Tab */}
        {activeTab === 'builder' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Strategy Info */}
            <div className="lg:col-span-3 bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Strategy Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Strategy Name</label>
                  <input
                    type="text"
                    value={strategy.name}
                    onChange={(e) => setStrategy(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter strategy name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Timeframe</label>
                  <select
                    value={strategy.timeframe}
                    onChange={(e) => setStrategy(prev => ({ ...prev, timeframe: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="1d">Daily</option>
                    <option value="1h">Hourly</option>
                    <option value="15m">15 Minutes</option>
                    <option value="5m">5 Minutes</option>
                  </select>
                </div>
              </div>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea
                  value={strategy.description}
                  onChange={(e) => setStrategy(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                  placeholder="Describe your strategy..."
                />
              </div>
            </div>

            {/* Entry Rules */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                  <span>Entry Rules</span>
                </h2>
                <button
                  onClick={() => addRule('entry')}
                  className="p-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
              
              <div className="space-y-3">
                {strategy.entry_rules.map((rule, index) => (
                  <RuleCard
                    key={rule.id}
                    rule={rule}
                    index={index}
                    onUpdate={updateRule}
                    onDelete={deleteRule}
                    onMoveUp={() => moveRule(rule.id, 'up')}
                    onMoveDown={() => moveRule(rule.id, 'down')}
                    canMoveUp={index > 0}
                    canMoveDown={index < strategy.entry_rules.length - 1}
                  />
                ))}
                {strategy.entry_rules.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <TrendingUp className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p>No entry rules yet</p>
                    <p className="text-sm">Click + to add your first rule</p>
                  </div>
                )}
              </div>
            </div>

            {/* Exit Rules */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
                  <TrendingDown className="w-5 h-5 text-red-600" />
                  <span>Exit Rules</span>
                </h2>
                <button
                  onClick={() => addRule('exit')}
                  className="p-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
              
              <div className="space-y-3">
                {strategy.exit_rules.map((rule, index) => (
                  <RuleCard
                    key={rule.id}
                    rule={rule}
                    index={index}
                    onUpdate={updateRule}
                    onDelete={deleteRule}
                    onMoveUp={() => moveRule(rule.id, 'up')}
                    onMoveDown={() => moveRule(rule.id, 'down')}
                    canMoveUp={index > 0}
                    canMoveDown={index < strategy.exit_rules.length - 1}
                  />
                ))}
                {strategy.exit_rules.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <TrendingDown className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p>No exit rules yet</p>
                    <p className="text-sm">Click + to add your first rule</p>
                  </div>
                )}
              </div>
            </div>

            {/* Indicator Palette */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <BarChart3 className="w-5 h-5 text-blue-600" />
                <span>Indicators</span>
              </h2>
              
              {Object.entries(INDICATORS).map(([category, indicators]) => (
                <div key={category} className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">{category}</h3>
                  <div className="grid grid-cols-1 gap-2">
                    {indicators.map((indicator) => {
                      const Icon = indicator.icon;
                      return (
                        <div
                          key={indicator.id}
                          className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                          title={indicator.description}
                        >
                          <div className="flex items-center space-x-2">
                            <Icon className="w-4 h-4 text-gray-600" />
                            <span className="text-sm font-medium">{indicator.name}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">{indicator.description}</p>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Backtest Results Tab */}
        {activeTab === 'backtest' && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            {backtestResult ? (
              <BacktestResults result={backtestResult} />
            ) : (
              <div className="text-center py-12">
                <BarChart3 className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Backtest Results</h3>
                <p className="text-gray-600 mb-4">Run a backtest to see performance metrics and analysis</p>
                <button
                  onClick={runBacktest}
                  disabled={isBacktesting}
                  className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2 mx-auto disabled:opacity-50"
                >
                  <Play className="w-5 h-5" />
                  <span>{isBacktesting ? 'Running Backtest...' : 'Run Backtest'}</span>
                </button>
              </div>
            )}
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <StrategySettings strategy={strategy} onUpdate={setStrategy} />
          </div>
        )}

        {/* Template Modal */}
        {showTemplates && (
          <TemplateModal
            templates={STRATEGY_TEMPLATES}
            onSelect={loadTemplate}
            onClose={() => setShowTemplates(false)}
          />
        )}
      </div>
    </div>
  );
}

// Rule Card Component
interface RuleCardProps {
  rule: Rule;
  index: number;
  onUpdate: (ruleId: string, updates: Partial<Rule>) => void;
  onDelete: (ruleId: string) => void;
  onMoveUp?: () => void;
  onMoveDown?: () => void;
  canMoveUp?: boolean;
  canMoveDown?: boolean;
}

function RuleCard({ rule, index, onUpdate, onDelete, onMoveUp, onMoveDown, canMoveUp, canMoveDown }: RuleCardProps) {
  const getIndicatorOptions = () => {
    return Object.values(INDICATORS).flat();
  };

  return (
    <div className="p-4 border border-gray-200 rounded-lg bg-white transition-shadow hover:shadow-md">
      <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${
                rule.type === 'entry' ? 'bg-green-500' : 'bg-red-500'
              }`} />
              <span className="text-sm font-medium text-gray-700">
                {rule.type === 'entry' ? 'Entry' : 'Exit'} Rule
              </span>
            </div>
            <div className="flex items-center gap-1">
              {onMoveUp && (
                <button
                  onClick={onMoveUp}
                  disabled={!canMoveUp}
                  className="text-gray-500 hover:text-gray-700 p-1 hover:bg-gray-50 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Move up"
                >
                  <TrendingUp className="w-4 h-4" />
                </button>
              )}
              {onMoveDown && (
                <button
                  onClick={onMoveDown}
                  disabled={!canMoveDown}
                  className="text-gray-500 hover:text-gray-700 p-1 hover:bg-gray-50 rounded disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Move down"
                >
                  <TrendingDown className="w-4 h-4" />
                </button>
              )}
              <button
                onClick={() => onDelete(rule.id)}
                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>

          <div className="space-y-3">
            {/* Indicator Selection */}
            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">Indicator</label>
              <select
                value={rule.indicator}
                onChange={(e) => onUpdate(rule.id, { indicator: e.target.value })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
              >
                {getIndicatorOptions().map((indicator) => (
                  <option key={indicator.id} value={indicator.id}>
                    {indicator.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Condition Selection */}
            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">Condition</label>
              <select
                value={rule.condition}
                onChange={(e) => onUpdate(rule.id, { condition: e.target.value })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
              >
                {CONDITIONS.map((condition) => (
                  <option key={condition.id} value={condition.id}>
                    {condition.name} {condition.description}
                  </option>
                ))}
              </select>
            </div>

            {/* Value Input */}
            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">Value</label>
              <input
                type="number"
                value={Array.isArray(rule.value) ? rule.value[0] : rule.value}
                onChange={(e) => onUpdate(rule.id, { value: parseFloat(e.target.value) || 0 })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                step="0.01"
              />
            </div>

            {/* Weight Input */}
            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">Weight</label>
              <input
                type="number"
                value={rule.weight || 1.0}
                onChange={(e) => onUpdate(rule.id, { weight: parseFloat(e.target.value) || 1.0 })}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent"
                min="0"
                max="2"
                step="0.1"
              />
            </div>
          </div>
        </div>
    </div>
  );
}

// Backtest Results Component
interface BacktestResultsProps {
  result: BacktestResult;
}

function BacktestResults({ result }: BacktestResultsProps) {
  const formatPercentage = (value: number) => `${(value * 100).toFixed(2)}%`;
  const formatCurrency = (value: number) => `¥${value.toLocaleString()}`;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Backtest Results</h2>
        <div className="flex items-center space-x-2">
          <CheckCircle className="w-5 h-5 text-green-600" />
          <span className="text-sm text-green-600 font-medium">Completed</span>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
          <div className="text-sm text-green-700 font-medium">Total Return</div>
          <div className="text-2xl font-bold text-green-800">
            {formatPercentage(result.total_return)}
          </div>
        </div>
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
          <div className="text-sm text-blue-700 font-medium">Sharpe Ratio</div>
          <div className="text-2xl font-bold text-blue-800">
            {result.sharpe_ratio.toFixed(2)}
          </div>
        </div>
        <div className="bg-gradient-to-r from-red-50 to-red-100 p-4 rounded-lg">
          <div className="text-sm text-red-700 font-medium">Max Drawdown</div>
          <div className="text-2xl font-bold text-red-800">
            {formatPercentage(result.max_drawdown)}
          </div>
        </div>
        <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg">
          <div className="text-sm text-purple-700 font-medium">Win Rate</div>
          <div className="text-2xl font-bold text-purple-800">
            {formatPercentage(result.win_rate)}
          </div>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="text-sm text-gray-600 font-medium">Total Trades</div>
          <div className="text-xl font-bold text-gray-800">{result.total_trades}</div>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="text-sm text-gray-600 font-medium">Profit Factor</div>
          <div className="text-xl font-bold text-gray-800">{result.profit_factor.toFixed(2)}</div>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="text-sm text-gray-600 font-medium">Avg Trade</div>
          <div className="text-xl font-bold text-gray-800">
            {result.performance_metrics?.avg_pnl_per_trade ? 
              formatCurrency(result.performance_metrics.avg_pnl_per_trade) : 'N/A'}
          </div>
        </div>
      </div>

      {/* Performance Chart Placeholder */}
      <div className="bg-gray-50 p-8 rounded-lg text-center">
        <BarChart3 className="w-12 h-12 mx-auto mb-3 text-gray-400" />
        <h3 className="text-lg font-medium text-gray-700 mb-2">Equity Curve</h3>
        <p className="text-gray-600">Chart visualization would be implemented here</p>
        <p className="text-sm text-gray-500 mt-2">
          {result.equity_curve?.length || 0} data points available
        </p>
      </div>
    </div>
  );
}

// Strategy Settings Component
interface StrategySettingsProps {
  strategy: Strategy;
  onUpdate: (strategy: Strategy) => void;
}

function StrategySettings({ strategy, onUpdate }: StrategySettingsProps) {
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900">Strategy Settings</h2>

      {/* Position Sizing */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Position Sizing</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Method</label>
            <select
              value={strategy.position_sizing.method}
              onChange={(e) => onUpdate({
                ...strategy,
                position_sizing: { ...strategy.position_sizing, method: e.target.value }
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {POSITION_SIZING_METHODS.map((method) => (
                <option key={method.id} value={method.id}>
                  {method.name} - {method.description}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Value</label>
            <input
              type="number"
              value={strategy.position_sizing.value}
              onChange={(e) => onUpdate({
                ...strategy,
                position_sizing: { ...strategy.position_sizing, value: parseFloat(e.target.value) || 0 }
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0"
              step="0.1"
            />
          </div>
        </div>
      </div>

      {/* Risk Management */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Management</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Stop Loss (%)</label>
            <input
              type="number"
              value={strategy.risk_management.stop_loss || ''}
              onChange={(e) => onUpdate({
                ...strategy,
                risk_management: { ...strategy.risk_management, stop_loss: parseFloat(e.target.value) || undefined }
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0"
              max="50"
              step="0.5"
              placeholder="5"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Take Profit (%)</label>
            <input
              type="number"
              value={strategy.risk_management.take_profit || ''}
              onChange={(e) => onUpdate({
                ...strategy,
                risk_management: { ...strategy.risk_management, take_profit: parseFloat(e.target.value) || undefined }
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0"
              max="100"
              step="0.5"
              placeholder="15"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Max Position Size (%)</label>
            <input
              type="number"
              value={strategy.risk_management.max_position_size || ''}
              onChange={(e) => onUpdate({
                ...strategy,
                risk_management: { ...strategy.risk_management, max_position_size: parseFloat(e.target.value) || undefined }
              })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              min="0"
              max="100"
              step="1"
              placeholder="20"
            />
          </div>
        </div>
      </div>
    </div>
  );
}

// Template Modal Component
interface TemplateModalProps {
  templates: typeof STRATEGY_TEMPLATES;
  onSelect: (templateId: string) => void;
  onClose: () => void;
}

function TemplateModal({ templates, onSelect, onClose }: TemplateModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">Strategy Templates</h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Trash2 className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        <div className="p-6 overflow-y-auto">
          <div className="grid grid-cols-1 gap-4">
            {templates.map((template) => (
              <div
                key={template.id}
                onClick={() => onSelect(template.id)}
                className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <h3 className="font-semibold text-gray-900 mb-2">{template.name}</h3>
                <p className="text-gray-600 text-sm mb-3">{template.description}</p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{template.entry_rules.length} entry rules</span>
                  <span>{template.exit_rules.length} exit rules</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}