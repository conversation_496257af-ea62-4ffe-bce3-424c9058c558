import React from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ErrorMessageProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  className?: string;
  variant?: 'default' | 'compact';
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  title = '出现错误',
  message,
  onRetry,
  className,
  variant = 'default',
}) => {
  if (variant === 'compact') {
    return (
      <div className={cn('flex items-center space-x-2 text-red-600', className)}>
        <AlertCircle className="h-4 w-4 flex-shrink-0" />
        <span className="text-sm">{message}</span>
        {onRetry && (
          <button
            onClick={onRetry}
            className="text-blue-600 hover:text-blue-700 text-sm underline"
          >
            重试
          </button>
        )}
      </div>
    );
  }

  return (
    <div className={cn('rounded-lg border border-red-200 bg-red-50 p-4', className)}>
      <div className="flex items-start space-x-3">
        <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-red-800">{title}</h3>
          <p className="mt-1 text-sm text-red-700">{message}</p>
          {onRetry && (
            <button
              onClick={onRetry}
              className="mt-3 inline-flex items-center space-x-2 text-sm text-red-800 hover:text-red-900 font-medium"
            >
              <RefreshCw className="h-4 w-4" />
              <span>重试</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
