import React, { useEffect } from 'react';
import { X, Check<PERSON><PERSON>cle, AlertCircle, AlertTriangle, Info } from 'lucide-react';
import { useUI, useUIActions } from '@/store/useAppStore';
import { cn } from '@/lib/utils';

const iconMap = {
  success: CheckCircle,
  error: AlertCircle,
  warning: AlertTriangle,
  info: Info,
};

const colorMap = {
  success: 'bg-green-50 border-green-200 text-green-800',
  error: 'bg-red-50 border-red-200 text-red-800',
  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
  info: 'bg-blue-50 border-blue-200 text-blue-800',
};

const iconColorMap = {
  success: 'text-green-600',
  error: 'text-red-600',
  warning: 'text-yellow-600',
  info: 'text-blue-600',
};

export const NotificationSystem: React.FC = () => {
  const { notifications } = useUI();
  const { removeNotification } = useUIActions();

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {notifications.map((notification) => {
        const Icon = iconMap[notification.type];
        
        return (
          <div
            key={notification.id}
            className={cn(
              'rounded-lg border p-4 shadow-lg transition-all duration-300 ease-in-out',
              colorMap[notification.type]
            )}
          >
            <div className="flex items-start space-x-3">
              <Icon className={cn('h-5 w-5 flex-shrink-0 mt-0.5', iconColorMap[notification.type])} />
              <div className="flex-1">
                <p className="text-sm font-medium">{notification.message}</p>
              </div>
              <button
                onClick={() => removeNotification(notification.id)}
                className="flex-shrink-0 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        );
      })}
    </div>
  );
};

// Hook for easy notification usage
export const useNotifications = () => {
  const { addNotification } = useUIActions();

  return {
    success: (message: string) => addNotification({ type: 'success', message }),
    error: (message: string) => addNotification({ type: 'error', message }),
    warning: (message: string) => addNotification({ type: 'warning', message }),
    info: (message: string) => addNotification({ type: 'info', message }),
  };
};
