/**
 * Custom hooks for API calls
 * Provides consistent loading states, error handling, and caching
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { apiClient, ApiError, ApiResponse, handleApiError } from '@/lib/api';

export interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export interface UseApiOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}

/**
 * Generic hook for API calls with loading and error states
 */
export function useApi<T>(
  apiCall: () => Promise<ApiResponse<T>>,
  options: UseApiOptions = {}
): UseApiState<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const mountedRef = useRef(true);

  const { immediate = true, onSuccess, onError } = options;

  const fetchData = useCallback(async () => {
    if (!mountedRef.current) return;
    
    setLoading(true);
    setError(null);

    try {
      const response = await apiCall();
      
      if (!mountedRef.current) return;
      
      if (response.success && response.data) {
        setData(response.data);
        onSuccess?.(response.data);
      } else {
        const errorMessage = response.error || response.message || 'Request failed';
        setError(errorMessage);
        onError?.(errorMessage);
      }
    } catch (err) {
      if (!mountedRef.current) return;
      
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, [apiCall, onSuccess, onError]);

  useEffect(() => {
    if (immediate) {
      fetchData();
    }
  }, [fetchData, immediate]);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
}

/**
 * Hook for stock information
 */
export function useStockInfo(
  stockCode: string,
  period: string = 'daily',
  days: number = 100,
  options: UseApiOptions = {}
) {
  return useApi(
    () => apiClient.getStockInfo(stockCode, period, days),
    { immediate: !!stockCode, ...options }
  );
}

/**
 * Hook for technical indicators
 */
export function useTechnicalIndicators(
  stockCode: string,
  indicators: string[] = [],
  timeframe: string = '1d',
  options: UseApiOptions = {}
) {
  return useApi(
    () => apiClient.getTechnicalIndicators(stockCode, indicators, timeframe),
    { immediate: !!stockCode, ...options }
  );
}

/**
 * Hook for trading signals
 */
export function useTradingSignals(
  stockCode: string,
  timeframe: string = '1d',
  options: UseApiOptions = {}
) {
  return useApi(
    () => apiClient.getTradingSignals(stockCode, timeframe),
    { immediate: !!stockCode, ...options }
  );
}

/**
 * Hook for stock list
 */
export function useStockList(
  market: string = 'all',
  limit: number = 100,
  options: UseApiOptions = {}
) {
  return useApi(
    () => apiClient.getStockList(market, limit),
    options
  );
}

/**
 * Hook for fundamental analysis
 */
export function useFundamentalAnalysis(
  stockCode: string,
  options: UseApiOptions = {}
) {
  return useApi(
    () => apiClient.getFundamentalAnalysis(stockCode),
    { immediate: !!stockCode, ...options }
  );
}

/**
 * Hook for market sentiment
 */
export function useMarketSentiment(options: UseApiOptions = {}) {
  const sentimentState = useApi(
    () => apiClient.getOverallSentiment(),
    options
  );
  
  const capitalFlowState = useApi(
    () => apiClient.getCapitalFlow(),
    options
  );

  return {
    sentiment: sentimentState,
    capitalFlow: capitalFlowState,
    loading: sentimentState.loading || capitalFlowState.loading,
    error: sentimentState.error || capitalFlowState.error,
    refetch: async () => {
      await Promise.all([
        sentimentState.refetch(),
        capitalFlowState.refetch(),
      ]);
    },
  };
}

/**
 * Hook for market overview
 */
export function useMarketOverview(options: UseApiOptions = {}) {
  return useApi(
    () => apiClient.getMarketOverview(),
    options
  );
}

/**
 * Hook for async operations (mutations)
 */
export function useAsyncOperation<T, P = any>() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<T | null>(null);

  const execute = useCallback(async (
    operation: (params: P) => Promise<ApiResponse<T>>,
    params: P,
    options: {
      onSuccess?: (data: T) => void;
      onError?: (error: string) => void;
    } = {}
  ) => {
    setLoading(true);
    setError(null);

    try {
      const response = await operation(params);
      
      if (response.success && response.data) {
        setData(response.data);
        options.onSuccess?.(response.data);
        return response.data;
      } else {
        const errorMessage = response.error || response.message || 'Operation failed';
        setError(errorMessage);
        options.onError?.(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (err) {
      const errorMessage = handleApiError(err);
      setError(errorMessage);
      options.onError?.(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  return {
    execute,
    loading,
    error,
    data,
    reset,
  };
}

/**
 * Hook for stock screening
 */
export function useStockScreening() {
  return useAsyncOperation<{
    total_count: number;
    stocks: any[];
    execution_time: number;
  }, any>();
}

/**
 * Hook for strategy operations
 */
export function useStrategyOperations() {
  const createStrategy = useAsyncOperation<{ strategy_id: string; message: string }, any>();
  const backtestStrategy = useAsyncOperation<{ results: any }, any>();

  return {
    createStrategy: {
      ...createStrategy,
      execute: (strategyData: any, options?: any) =>
        createStrategy.execute(
          (data) => apiClient.createStrategy(data),
          strategyData,
          options
        ),
    },
    backtestStrategy: {
      ...backtestStrategy,
      execute: (backtestData: any, options?: any) =>
        backtestStrategy.execute(
          (data) => apiClient.backtestStrategy(data),
          backtestData,
          options
        ),
    },
  };
}

/**
 * Hook for risk management operations
 */
export function useRiskManagement() {
  const portfolioAnalysis = useAsyncOperation<{ risk_metrics: any }, any>();
  const varCalculation = useAsyncOperation<{ var_result: any }, any>();

  return {
    portfolioAnalysis: {
      ...portfolioAnalysis,
      execute: (portfolio: any[], options?: any) =>
        portfolioAnalysis.execute(
          (data) => apiClient.analyzePortfolioRisk(data),
          portfolio,
          options
        ),
    },
    varCalculation: {
      ...varCalculation,
      execute: (varData: any, options?: any) =>
        varCalculation.execute(
          (data) => apiClient.calculateVaR(data),
          varData,
          options
        ),
    },
  };
}
