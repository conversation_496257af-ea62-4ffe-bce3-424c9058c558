/**
 * Centralized API client for Trading Agent
 * Eliminates code duplication and provides consistent error handling
 */

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface StockInfo {
  code: string;
  name: string;
  price?: number;
  change_pct?: number;
  market?: string;
  industry?: string;
}

export interface TechnicalIndicators {
  rsi?: number;
  macd?: {
    macd: number;
    signal: number;
    histogram: number;
  };
  kdj?: {
    k: number;
    d: number;
    j: number;
  };
  bollinger?: {
    upper: number;
    middle: number;
    lower: number;
  };
  magic_nine?: {
    signals: Array<{
      type: 'buy' | 'sell';
      count: number;
      date: string;
    }>;
  };
}

export interface CandlestickData {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

class ApiClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseUrl: string = '/api') {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.defaultHeaders,
          ...options.headers,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new ApiError(
          data.message || data.error || 'Request failed',
          response.status,
          data
        );
      }

      return data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      
      // Network or parsing error
      throw new ApiError(
        error instanceof Error ? error.message : 'Network error occurred'
      );
    }
  }

  // Health check
  async healthCheck(): Promise<ApiResponse> {
    return this.request('/health');
  }

  // Stock information
  async getStockInfo(stockCode: string, period: string = 'daily', days: number = 100): Promise<ApiResponse<{
    code: string;
    name: string;
    price_data: CandlestickData[];
    magic_nine: any;
    macd_data: any;
    signals: any[];
    last_updated: string;
  }>> {
    const params = new URLSearchParams({
      period,
      days: days.toString(),
    });
    
    return this.request(`/stock/info/${stockCode}?${params}`);
  }

  // Technical indicators
  async getTechnicalIndicators(
    stockCode: string,
    indicators: string[] = [],
    timeframe: string = '1d'
  ): Promise<ApiResponse<{
    stock_code: string;
    indicators: TechnicalIndicators;
    candlestickData?: CandlestickData[];
    volumeData?: Array<{ date: string; volume: number }>;
  }>> {
    const params = new URLSearchParams({
      timeframe,
      ...(indicators.length > 0 && { indicators: indicators.join(',') }),
    });
    
    return this.request(`/technical/indicators/${stockCode}?${params}`);
  }

  // Trading signals
  async getTradingSignals(
    stockCode: string,
    timeframe: string = '1d'
  ): Promise<ApiResponse<{
    stock_code: string;
    signals: any[];
    timestamp: string;
  }>> {
    const params = new URLSearchParams({ timeframe });
    return this.request(`/technical/signals/${stockCode}?${params}`);
  }

  // Stock list
  async getStockList(
    market: string = 'all',
    limit: number = 100
  ): Promise<ApiResponse<{
    stocks: StockInfo[];
    count: number;
  }>> {
    const params = new URLSearchParams({
      market,
      limit: limit.toString(),
    });
    
    return this.request(`/stocks/list?${params}`);
  }

  // Analysis calculation
  async calculateAnalysis(data: {
    stock_code: string;
    indicators?: string[];
    start_date?: string;
    end_date?: string;
  }): Promise<ApiResponse<{
    data: Record<string, any>;
    signals: any[];
  }>> {
    return this.request('/analysis/calculate', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Stock screening
  async filterStocks(criteria: {
    magic_nine_condition?: Record<string, any>;
    macd_condition?: Record<string, any>;
    market_cap_range?: Record<string, any>;
    industry_filter?: string[];
  }): Promise<ApiResponse<{
    total_count: number;
    stocks: Array<StockInfo & { signals: any[]; signal_strength: number }>;
    execution_time: number;
  }>> {
    return this.request('/screening/filter', {
      method: 'POST',
      body: JSON.stringify(criteria),
    });
  }

  // Fundamental analysis
  async getFundamentalAnalysis(stockCode: string): Promise<ApiResponse<{
    stock_code: string;
    financial_health: any;
    valuation: any;
    industry_analysis: any;
    timestamp: string;
  }>> {
    return this.request(`/fundamental/analysis/${stockCode}`);
  }

  // Financial ratios
  async getFinancialRatios(stockCode: string): Promise<ApiResponse<{
    stock_code: string;
    ratios: Record<string, number>;
    timestamp: string;
  }>> {
    return this.request(`/fundamental/ratios/${stockCode}`);
  }

  // Market sentiment
  async getOverallSentiment(): Promise<ApiResponse<any>> {
    return this.request('/sentiment/overall');
  }

  async getCapitalFlow(): Promise<ApiResponse<any>> {
    return this.request('/sentiment/capital-flow');
  }

  // Real-time data
  async getRealtimeStocks(stockCodes: string[]): Promise<ApiResponse<any>> {
    return this.request('/realtime/stocks', {
      method: 'POST',
      body: JSON.stringify({ codes: stockCodes }),
    });
  }

  // Market overview
  async getMarketOverview(): Promise<ApiResponse<any>> {
    return this.request('/market/overview');
  }

  // Strategy management
  async createStrategy(strategy: {
    name: string;
    config: Record<string, any>;
    user_id?: string;
  }): Promise<ApiResponse<{
    strategy_id: string;
    message: string;
  }>> {
    return this.request('/strategy/create', {
      method: 'POST',
      body: JSON.stringify(strategy),
    });
  }

  async backtestStrategy(data: {
    strategy_id: string;
    start_date: string;
    end_date: string;
    initial_capital?: number;
  }): Promise<ApiResponse<{
    results: any;
  }>> {
    return this.request('/strategy/backtest', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Risk management
  async analyzePortfolioRisk(portfolio: any[]): Promise<ApiResponse<{
    risk_metrics: any;
  }>> {
    return this.request('/risk/portfolio-analysis', {
      method: 'POST',
      body: JSON.stringify({ portfolio }),
    });
  }

  async calculateVaR(data: {
    portfolio: any[];
    confidence_level?: number;
    time_horizon?: number;
  }): Promise<ApiResponse<{
    var_result: any;
  }>> {
    return this.request('/risk/var-calculation', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}

// Create and export singleton instance
export const apiClient = new ApiClient();

// Export error class for error handling
export { ApiError };

// Utility function for handling API errors in components
export const handleApiError = (error: unknown): string => {
  if (error instanceof ApiError) {
    return error.message;
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};
