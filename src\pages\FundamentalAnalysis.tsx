import React, { useState, useEffect } from 'react';
import { Search, DollarSign, TrendingUp, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AlertTriangle } from 'lucide-react';

interface StockInfo {
  code: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
  industry: string;
  sector: string;
}

interface FinancialMetrics {
  profitability: {
    roe: number;
    roa: number;
    grossMargin: number;
    netMargin: number;
    operatingMargin: number;
    description: string;
    rating: string;
  };
  valuation: {
    pe: number;
    pb: number;
    ps: number;
    peg: number;
    evEbitda: number;
    description: string;
    rating: string;
  };
  growth: {
    revenueGrowth: number;
    netIncomeGrowth: number;
    epsGrowth: number;
    bookValueGrowth: number;
    description: string;
    rating: string;
  };
  financial_health: {
    currentRatio: number;
    quickRatio: number;
    debtToEquity: number;
    interestCoverage: number;
    cashRatio: number;
    description: string;
    rating: string;
  };
  efficiency: {
    assetTurnover: number;
    inventoryTurnover: number;
    receivablesTurnover: number;
    workingCapitalTurnover: number;
    description: string;
    rating: string;
  };
}

interface IndustryComparison {
  industry: string;
  companyMetric: number;
  industryAverage: number;
  industryMedian: number;
  percentile: number;
  ranking: string;
}

interface FinancialStatement {
  year: string;
  revenue: number;
  netIncome: number;
  totalAssets: number;
  totalEquity: number;
  operatingCashFlow: number;
  freeCashFlow: number;
}

export default function FundamentalAnalysis() {
  const [stockCode, setStockCode] = useState('');
  const [stockInfo, setStockInfo] = useState<StockInfo | null>(null);
  const [metrics, setMetrics] = useState<FinancialMetrics | null>(null);
  const [industryComparison, setIndustryComparison] = useState<IndustryComparison[]>([]);
  const [financialStatements, setFinancialStatements] = useState<FinancialStatement[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const stockParam = urlParams.get('stock');
    if (stockParam) {
      setStockCode(stockParam);
      handleAnalysis(stockParam);
    }
  }, []);

  const handleAnalysis = async (code: string) => {
    if (!code.trim()) return;
    
    setLoading(true);
    setError('');
    
    try {
      // Mock data for development - replace with actual API calls
      const mockStockInfo: StockInfo = {
        code: code.toUpperCase(),
        name: '平安银行',
        price: 12.45,
        change: 0.23,
        changePercent: 1.88,
        volume: 45678900,
        marketCap: 241500000000,
        industry: '银行',
        sector: '金融'
      };
      
      const mockMetrics: FinancialMetrics = {
        profitability: {
          roe: 11.2,
          roa: 0.98,
          grossMargin: 68.5,
          netMargin: 28.3,
          operatingMargin: 35.7,
          description: '盈利能力表现良好，ROE和净利润率均高于行业平均水平',
          rating: '良好'
        },
        valuation: {
          pe: 5.8,
          pb: 0.65,
          ps: 1.64,
          peg: 0.52,
          evEbitda: 4.2,
          description: '估值水平偏低，具有一定的投资价值',
          rating: '低估'
        },
        growth: {
          revenueGrowth: 8.5,
          netIncomeGrowth: 12.3,
          epsGrowth: 11.8,
          bookValueGrowth: 9.2,
          description: '增长稳健，净利润增长率表现突出',
          rating: '稳健'
        },
        financial_health: {
          currentRatio: 1.45,
          quickRatio: 1.32,
          debtToEquity: 0.68,
          interestCoverage: 8.5,
          cashRatio: 0.25,
          description: '财务状况健康，流动性充足，负债水平合理',
          rating: '健康'
        },
        efficiency: {
          assetTurnover: 0.035,
          inventoryTurnover: 0,
          receivablesTurnover: 0,
          workingCapitalTurnover: 2.1,
          description: '资产运营效率符合银行业特点',
          rating: '正常'
        }
      };
      
      const mockIndustryComparison: IndustryComparison[] = [
        { industry: 'ROE', companyMetric: 11.2, industryAverage: 9.8, industryMedian: 9.5, percentile: 75, ranking: '优秀' },
        { industry: 'PE', companyMetric: 5.8, industryAverage: 6.5, industryMedian: 6.2, percentile: 25, ranking: '低估' },
        { industry: '净利润率', companyMetric: 28.3, industryAverage: 25.1, industryMedian: 24.8, percentile: 80, ranking: '优秀' }
      ];
      
      const mockFinancialStatements: FinancialStatement[] = [
        { year: '2023', revenue: 168500000000, netIncome: 47600000000, totalAssets: 4850000000000, totalEquity: 371000000000, operatingCashFlow: 52000000000, freeCashFlow: 48000000000 },
        { year: '2022', revenue: 155200000000, netIncome: 42300000000, totalAssets: 4620000000000, totalEquity: 339000000000, operatingCashFlow: 48500000000, freeCashFlow: 44200000000 },
        { year: '2021', revenue: 143800000000, netIncome: 37800000000, totalAssets: 4380000000000, totalEquity: 312000000000, operatingCashFlow: 45200000000, freeCashFlow: 41500000000 }
      ];
      
      setStockInfo(mockStockInfo);
      setMetrics(mockMetrics);
      setIndustryComparison(mockIndustryComparison);
      setFinancialStatements(mockFinancialStatements);
    } catch (err) {
      setError('获取基本面数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    handleAnalysis(stockCode);
  };

  const getRatingColor = (rating: string) => {
    switch (rating) {
      case '优秀': case '健康': case '良好': return 'text-green-600 bg-green-100';
      case '低估': return 'text-blue-600 bg-blue-100';
      case '稳健': case '正常': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatNumber = (num: number, decimals: number = 2) => {
    if (num >= 1e12) return (num / 1e12).toFixed(decimals) + '万亿';
    if (num >= 1e8) return (num / 1e8).toFixed(decimals) + '亿';
    if (num >= 1e4) return (num / 1e4).toFixed(decimals) + '万';
    return num.toFixed(decimals);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-8">
        <div className="container mx-auto px-4">
          <h1 className="text-3xl font-bold mb-4">基本面分析</h1>
          <p className="text-blue-100">深度分析公司财务状况、盈利能力和投资价值</p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Search Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="flex gap-4">
            <div className="flex-1">
              <input
                type="text"
                value={stockCode}
                onChange={(e) => setStockCode(e.target.value)}
                placeholder="请输入股票代码（如：000001）"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <button
              onClick={handleSearch}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
            >
              <Search size={20} />
              {loading ? '分析中...' : '开始分析'}
            </button>
          </div>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <div className="flex items-center gap-2">
              <AlertTriangle size={20} />
              {error}
            </div>
          </div>
        )}

        {stockInfo && (
          <>
            {/* Stock Info */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-800">{stockInfo.name}</h2>
                  <p className="text-gray-600">{stockInfo.code}</p>
                  <p className="text-sm text-gray-500">{stockInfo.industry} · {stockInfo.sector}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">当前价格</p>
                  <p className="text-2xl font-bold text-gray-800">¥{stockInfo.price.toFixed(2)}</p>
                  <p className={`text-sm ${stockInfo.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {stockInfo.change >= 0 ? '+' : ''}{stockInfo.change.toFixed(2)} ({stockInfo.changePercent.toFixed(2)}%)
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">成交量</p>
                  <p className="text-lg font-semibold text-gray-800">{formatNumber(stockInfo.volume)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">市值</p>
                  <p className="text-lg font-semibold text-gray-800">{formatNumber(stockInfo.marketCap)}</p>
                </div>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="bg-white rounded-lg shadow-md mb-8">
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  {[
                    { id: 'overview', name: '财务概览', icon: BarChart3 },
                    { id: 'comparison', name: '行业对比', icon: PieChart },
                    { id: 'statements', name: '财务报表', icon: Calculator }
                  ].map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`py-4 px-2 border-b-2 font-medium text-sm flex items-center gap-2 ${
                          activeTab === tab.id
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        <Icon size={16} />
                        {tab.name}
                      </button>
                    );
                  })}
                </nav>
              </div>

              <div className="p-6">
                {activeTab === 'overview' && metrics && (
                  <div className="space-y-8">
                    {/* Profitability */}
                    <div>
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <DollarSign className="text-green-600" size={20} />
                        盈利能力
                      </h3>
                      <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">ROE</p>
                          <p className="text-xl font-bold">{metrics.profitability.roe}%</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">ROA</p>
                          <p className="text-xl font-bold">{metrics.profitability.roa}%</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">毛利率</p>
                          <p className="text-xl font-bold">{metrics.profitability.grossMargin}%</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">净利率</p>
                          <p className="text-xl font-bold">{metrics.profitability.netMargin}%</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">营业利润率</p>
                          <p className="text-xl font-bold">{metrics.profitability.operatingMargin}%</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRatingColor(metrics.profitability.rating)}`}>
                          {metrics.profitability.rating}
                        </span>
                        <p className="text-gray-600">{metrics.profitability.description}</p>
                      </div>
                    </div>

                    {/* Valuation */}
                    <div>
                      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                        <TrendingUp className="text-blue-600" size={20} />
                        估值水平
                      </h3>
                      <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">PE</p>
                          <p className="text-xl font-bold">{metrics.valuation.pe}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">PB</p>
                          <p className="text-xl font-bold">{metrics.valuation.pb}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">PS</p>
                          <p className="text-xl font-bold">{metrics.valuation.ps}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">PEG</p>
                          <p className="text-xl font-bold">{metrics.valuation.peg}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">EV/EBITDA</p>
                          <p className="text-xl font-bold">{metrics.valuation.evEbitda}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRatingColor(metrics.valuation.rating)}`}>
                          {metrics.valuation.rating}
                        </span>
                        <p className="text-gray-600">{metrics.valuation.description}</p>
                      </div>
                    </div>

                    {/* Growth */}
                    <div>
                      <h3 className="text-lg font-semibold mb-4">成长性</h3>
                      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">营收增长率</p>
                          <p className="text-xl font-bold">{metrics.growth.revenueGrowth}%</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">净利润增长率</p>
                          <p className="text-xl font-bold">{metrics.growth.netIncomeGrowth}%</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">EPS增长率</p>
                          <p className="text-xl font-bold">{metrics.growth.epsGrowth}%</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">净资产增长率</p>
                          <p className="text-xl font-bold">{metrics.growth.bookValueGrowth}%</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRatingColor(metrics.growth.rating)}`}>
                          {metrics.growth.rating}
                        </span>
                        <p className="text-gray-600">{metrics.growth.description}</p>
                      </div>
                    </div>

                    {/* Financial Health */}
                    <div>
                      <h3 className="text-lg font-semibold mb-4">财务健康</h3>
                      <div className="grid md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">流动比率</p>
                          <p className="text-xl font-bold">{metrics.financial_health.currentRatio}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">速动比率</p>
                          <p className="text-xl font-bold">{metrics.financial_health.quickRatio}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">资产负债率</p>
                          <p className="text-xl font-bold">{metrics.financial_health.debtToEquity}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">利息保障倍数</p>
                          <p className="text-xl font-bold">{metrics.financial_health.interestCoverage}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <p className="text-sm text-gray-600">现金比率</p>
                          <p className="text-xl font-bold">{metrics.financial_health.cashRatio}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRatingColor(metrics.financial_health.rating)}`}>
                          {metrics.financial_health.rating}
                        </span>
                        <p className="text-gray-600">{metrics.financial_health.description}</p>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'comparison' && (
                  <div>
                    <h3 className="text-lg font-semibold mb-6">行业对比分析</h3>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="border border-gray-200 px-4 py-3 text-left">指标</th>
                            <th className="border border-gray-200 px-4 py-3 text-center">公司数值</th>
                            <th className="border border-gray-200 px-4 py-3 text-center">行业平均</th>
                            <th className="border border-gray-200 px-4 py-3 text-center">行业中位数</th>
                            <th className="border border-gray-200 px-4 py-3 text-center">百分位</th>
                            <th className="border border-gray-200 px-4 py-3 text-center">评级</th>
                          </tr>
                        </thead>
                        <tbody>
                          {industryComparison.map((item, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="border border-gray-200 px-4 py-3 font-medium">{item.industry}</td>
                              <td className="border border-gray-200 px-4 py-3 text-center font-semibold">
                                {item.companyMetric}{item.industry.includes('率') ? '%' : ''}
                              </td>
                              <td className="border border-gray-200 px-4 py-3 text-center">
                                {item.industryAverage}{item.industry.includes('率') ? '%' : ''}
                              </td>
                              <td className="border border-gray-200 px-4 py-3 text-center">
                                {item.industryMedian}{item.industry.includes('率') ? '%' : ''}
                              </td>
                              <td className="border border-gray-200 px-4 py-3 text-center">{item.percentile}%</td>
                              <td className="border border-gray-200 px-4 py-3 text-center">
                                <span className={`px-2 py-1 rounded text-sm font-medium ${getRatingColor(item.ranking)}`}>
                                  {item.ranking}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {activeTab === 'statements' && (
                  <div>
                    <h3 className="text-lg font-semibold mb-6">历史财务数据</h3>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="border border-gray-200 px-4 py-3 text-left">年份</th>
                            <th className="border border-gray-200 px-4 py-3 text-center">营业收入</th>
                            <th className="border border-gray-200 px-4 py-3 text-center">净利润</th>
                            <th className="border border-gray-200 px-4 py-3 text-center">总资产</th>
                            <th className="border border-gray-200 px-4 py-3 text-center">股东权益</th>
                            <th className="border border-gray-200 px-4 py-3 text-center">经营现金流</th>
                            <th className="border border-gray-200 px-4 py-3 text-center">自由现金流</th>
                          </tr>
                        </thead>
                        <tbody>
                          {financialStatements.map((statement, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="border border-gray-200 px-4 py-3 font-medium">{statement.year}</td>
                              <td className="border border-gray-200 px-4 py-3 text-center">{formatNumber(statement.revenue)}</td>
                              <td className="border border-gray-200 px-4 py-3 text-center">{formatNumber(statement.netIncome)}</td>
                              <td className="border border-gray-200 px-4 py-3 text-center">{formatNumber(statement.totalAssets)}</td>
                              <td className="border border-gray-200 px-4 py-3 text-center">{formatNumber(statement.totalEquity)}</td>
                              <td className="border border-gray-200 px-4 py-3 text-center">{formatNumber(statement.operatingCashFlow)}</td>
                              <td className="border border-gray-200 px-4 py-3 text-center">{formatNumber(statement.freeCashFlow)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}