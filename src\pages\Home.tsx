import React, { useState, useEffect } from 'react';
import { TrendingUp, Bar<PERSON>hart3, Search, Star } from 'lucide-react';

interface PopularStock {
  code: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
}

export default function Home() {
  const [popularStocks, setPopularStocks] = useState<PopularStock[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPopularStocks();
  }, []);

  const fetchPopularStocks = async () => {
    try {
      const response = await fetch('/api/popular-stocks');
      if (response.ok) {
        const data = await response.json();
        setPopularStocks(data.stocks || []);
      }
    } catch (error) {
      console.error('Failed to fetch popular stocks:', error);
    } finally {
      setLoading(false);
    }
  };

  const navigateToAnalysis = (stockCode: string) => {
    window.location.href = `/analysis?stock=${stockCode}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-900 to-blue-800 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              智能股票分析平台
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              结合神奇九转与MACD顶背离，精准捕捉交易信号
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                onClick={() => window.location.href = '/analysis'}
                className="bg-yellow-500 hover:bg-yellow-600 text-blue-900 px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                开始分析
              </button>
              <button 
                onClick={() => window.location.href = '/screening'}
                className="border-2 border-white hover:bg-white hover:text-blue-900 px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                股票筛选
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
          核心功能
        </h2>
        <div className="grid md:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <TrendingUp className="text-blue-600" size={24} />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800">神奇九转指标</h3>
            <p className="text-gray-600">
              基于TD Sequential理论，识别市场转折点，提供精准的买卖时机
            </p>
          </div>
          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="bg-yellow-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <BarChart3 className="text-yellow-600" size={24} />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800">MACD顶背离</h3>
            <p className="text-gray-600">
              检测价格与MACD指标的背离现象，预警趋势反转信号
            </p>
          </div>
          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="bg-green-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Search className="text-green-600" size={24} />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800">智能筛选</h3>
            <p className="text-gray-600">
              批量筛选符合条件的股票，快速发现投资机会
            </p>
          </div>
        </div>
      </div>

      {/* Popular Stocks Section */}
      <div className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center mb-12">
            <Star className="text-yellow-500 mr-2" size={24} />
            <h2 className="text-3xl font-bold text-gray-800">热门股票</h2>
          </div>
          
          {loading ? (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <p className="mt-2 text-gray-600">加载中...</p>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {popularStocks.map((stock) => (
                <div 
                  key={stock.code}
                  onClick={() => navigateToAnalysis(stock.code)}
                  className="bg-gray-50 p-6 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors border hover:border-blue-300"
                >
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="font-semibold text-gray-800">{stock.name}</h3>
                      <p className="text-sm text-gray-500">{stock.code}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-lg text-gray-800">¥{stock.price.toFixed(2)}</p>
                      <p className={`text-sm ${
                        stock.change >= 0 ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {stock.change >= 0 ? '+' : ''}{stock.change.toFixed(2)} ({stock.changePercent.toFixed(2)}%)
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {!loading && popularStocks.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-600">暂无热门股票数据</p>
            </div>
          )}
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-blue-900 to-blue-800 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">开始您的投资分析之旅</h2>
          <p className="text-xl mb-8 text-blue-100">
            利用专业的技术指标，做出更明智的投资决策
          </p>
          <button 
            onClick={() => window.location.href = '/analysis'}
            className="bg-yellow-500 hover:bg-yellow-600 text-blue-900 px-8 py-3 rounded-lg font-semibold transition-colors"
          >
            立即开始分析
          </button>
        </div>
      </div>
    </div>
  );
}