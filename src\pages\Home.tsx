import React, { useState, useEffect } from 'react';
import { TrendingUp, Bar<PERSON>hart3, Search, Star, PieChart, Target, Shield, Activity, Brain, Zap } from 'lucide-react';

interface PopularStock {
  code: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
}

export default function Home() {
  const [popularStocks, setPopularStocks] = useState<PopularStock[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPopularStocks();
  }, []);

  const fetchPopularStocks = async () => {
    try {
      const response = await fetch('/api/popular-stocks');
      if (response.ok) {
        const data = await response.json();
        setPopularStocks(data.stocks || []);
      }
    } catch (error) {
      console.error('Failed to fetch popular stocks:', error);
    } finally {
      setLoading(false);
    }
  };

  const navigateToAnalysis = (stockCode: string) => {
    window.location.href = `/analysis?stock=${stockCode}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-900 to-blue-800 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              智能股票分析平台
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              结合神奇九转与MACD顶背离，精准捕捉交易信号
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                onClick={() => window.location.href = '/technical'}
                className="bg-yellow-500 hover:bg-yellow-600 text-blue-900 px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                技术分析
              </button>
              <button 
                onClick={() => window.location.href = '/fundamental'}
                className="border-2 border-white hover:bg-white hover:text-blue-900 px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                基本面分析
              </button>
              <button 
                onClick={() => window.location.href = '/screening'}
                className="border-2 border-white hover:bg-white hover:text-blue-900 px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                股票筛选
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Analysis Modules Section */}
      <div className="container mx-auto px-4 py-16">
        <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
          多维度分析体系
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div 
            onClick={() => window.location.href = '/technical'}
            className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all cursor-pointer hover:scale-105"
          >
            <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <TrendingUp className="text-blue-600" size={24} />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800">技术分析</h3>
            <p className="text-gray-600 mb-3">
              神奇九转、MACD、KDJ、布林带等多种技术指标综合分析
            </p>
            <div className="text-blue-600 font-medium">立即分析 →</div>
          </div>
          
          <div 
            onClick={() => window.location.href = '/fundamental'}
            className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all cursor-pointer hover:scale-105"
          >
            <div className="bg-green-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <BarChart3 className="text-green-600" size={24} />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800">基本面分析</h3>
            <p className="text-gray-600 mb-3">
              财务健康度、盈利能力、估值水平、行业对比分析
            </p>
            <div className="text-green-600 font-medium">查看详情 →</div>
          </div>
          
          <div 
            onClick={() => window.location.href = '/sentiment'}
            className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all cursor-pointer hover:scale-105"
          >
            <div className="bg-purple-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Brain className="text-purple-600" size={24} />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800">市场情绪</h3>
            <p className="text-gray-600 mb-3">
              资金流向、板块轮动、恐慌贪婪指数等情绪指标
            </p>
            <div className="text-purple-600 font-medium">了解更多 →</div>
          </div>
          
          <div 
            onClick={() => window.location.href = '/strategy'}
            className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all cursor-pointer hover:scale-105"
          >
            <div className="bg-yellow-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Target className="text-yellow-600" size={24} />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800">策略构建</h3>
            <p className="text-gray-600 mb-3">
              可视化策略构建器，支持回测验证和性能评估
            </p>
            <div className="text-yellow-600 font-medium">开始构建 →</div>
          </div>
          
          <div 
            onClick={() => window.location.href = '/risk'}
            className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all cursor-pointer hover:scale-105"
          >
            <div className="bg-red-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Shield className="text-red-600" size={24} />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800">风险管理</h3>
            <p className="text-gray-600 mb-3">
              投资组合风险监控、VaR计算、止损策略管理
            </p>
            <div className="text-red-600 font-medium">风险评估 →</div>
          </div>
          
          <div 
            onClick={() => window.location.href = '/screening'}
            className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all cursor-pointer hover:scale-105"
          >
            <div className="bg-indigo-100 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
              <Search className="text-indigo-600" size={24} />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-gray-800">股票筛选</h3>
            <p className="text-gray-600 mb-3">
              多因子筛选系统，快速发现符合条件的投资标的
            </p>
            <div className="text-indigo-600 font-medium">开始筛选 →</div>
          </div>
        </div>
      </div>
      
      {/* Key Features Section */}
      <div className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
            核心特色
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="text-blue-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">实时数据</h3>
              <p className="text-gray-600">
                接入多个数据源，提供实时股价、财务数据和市场情绪指标
              </p>
            </div>
            <div className="text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Activity className="text-green-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">智能算法</h3>
              <p className="text-gray-600">
                基于机器学习的信号识别，提高分析准确性和预测能力
              </p>
            </div>
            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <PieChart className="text-purple-600" size={32} />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">可视化界面</h3>
              <p className="text-gray-600">
                直观的图表展示和交互式分析界面，让复杂数据一目了然
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Popular Stocks Section */}
      <div className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center mb-12">
            <Star className="text-yellow-500 mr-2" size={24} />
            <h2 className="text-3xl font-bold text-gray-800">热门股票</h2>
          </div>
          
          {loading ? (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <p className="mt-2 text-gray-600">加载中...</p>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {popularStocks.map((stock) => (
                <div 
                  key={stock.code}
                  onClick={() => navigateToAnalysis(stock.code)}
                  className="bg-gray-50 p-6 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors border hover:border-blue-300"
                >
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="font-semibold text-gray-800">{stock.name}</h3>
                      <p className="text-sm text-gray-500">{stock.code}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-lg text-gray-800">¥{stock.price.toFixed(2)}</p>
                      <p className={`text-sm ${
                        stock.change >= 0 ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {stock.change >= 0 ? '+' : ''}{stock.change.toFixed(2)} ({stock.changePercent.toFixed(2)}%)
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {!loading && popularStocks.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-600">暂无热门股票数据</p>
            </div>
          )}
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-blue-900 to-blue-800 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">开始您的投资分析之旅</h2>
          <p className="text-xl mb-8 text-blue-100">
            利用专业的技术指标，做出更明智的投资决策
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button 
              onClick={() => window.location.href = '/technical'}
              className="bg-yellow-500 hover:bg-yellow-600 text-blue-900 px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              技术分析
            </button>
            <button 
              onClick={() => window.location.href = '/fundamental'}
              className="bg-green-500 hover:bg-green-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              基本面分析
            </button>
            <button 
              onClick={() => window.location.href = '/strategy'}
              className="border-2 border-white hover:bg-white hover:text-blue-900 px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              策略构建
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}