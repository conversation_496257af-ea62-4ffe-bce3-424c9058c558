import React, { useState, useEffect } from 'react';
import { Search, AlertTriangle, TrendingUp, TrendingDown, Activity, DollarSign, Users, BarChart3, <PERSON><PERSON>hart, AlertCircle, Target } from 'lucide-react';
import Chart from '../components/Chart';

interface CapitalFlow {
  date: string;
  northbound: number;
  mainForce: number;
  retail: number;
  institutional: number;
  total: number;
}

interface SectorData {
  sector: string;
  change: number;
  volume: number;
  capitalFlow: number;
  strength: number;
  trend: 'up' | 'down' | 'neutral';
}

interface SentimentIndicator {
  name: string;
  value: number;
  change: number;
  level: 'extreme_fear' | 'fear' | 'neutral' | 'greed' | 'extreme_greed';
  description: string;
}

interface MarketOverview {
  fearGreedIndex: number;
  marginBalance: number;
  marginChange: number;
  turnoverRate: number;
  volatilityIndex: number;
  marketMood: string;
}

export default function MarketSentiment() {
  const [capitalFlowData, setCapitalFlowData] = useState<CapitalFlow[]>([]);
  const [sectorData, setSectorData] = useState<SectorData[]>([]);
  const [sentimentIndicators, setSentimentIndicators] = useState<SentimentIndicator[]>([]);
  const [marketOverview, setMarketOverview] = useState<MarketOverview | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [timeframe, setTimeframe] = useState('1d');
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchMarketSentimentData();
  }, [timeframe]);

  const fetchMarketSentimentData = async () => {
    setLoading(true);
    setError('');
    
    try {
      // Fetch market sentiment data from backend
      const response = await fetch(`/api/sentiment/market?timeframe=${timeframe}`);
      if (!response.ok) {
        throw new Error('Failed to fetch market sentiment data');
      }
      
      const data = await response.json();
      setCapitalFlowData(data.capital_flow || []);
      setSectorData(data.sector_rotation || []);
      setSentimentIndicators(data.sentiment_indicators || []);
      setMarketOverview(data.market_overview || null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      // Mock data for development
      setMockData();
    } finally {
      setLoading(false);
    }
  };

  const setMockData = () => {
    // Mock capital flow data
    const mockCapitalFlow: CapitalFlow[] = [
      { date: '2024-01-15', northbound: 2500000000, mainForce: -1200000000, retail: 800000000, institutional: 1500000000, total: 3600000000 },
      { date: '2024-01-16', northbound: 1800000000, mainForce: -800000000, retail: 600000000, institutional: 1200000000, total: 2800000000 },
      { date: '2024-01-17', northbound: 3200000000, mainForce: -1500000000, retail: 1000000000, institutional: 1800000000, total: 4500000000 }
    ];
    
    // Mock sector data
    const mockSectorData: SectorData[] = [
      { sector: '科技', change: 2.5, volume: 45000000000, capitalFlow: 1200000000, strength: 85, trend: 'up' },
      { sector: '金融', change: -1.2, volume: 38000000000, capitalFlow: -800000000, strength: 35, trend: 'down' },
      { sector: '医药', change: 1.8, volume: 28000000000, capitalFlow: 600000000, strength: 72, trend: 'up' },
      { sector: '消费', change: 0.3, volume: 32000000000, capitalFlow: 200000000, strength: 55, trend: 'neutral' },
      { sector: '新能源', change: 3.2, volume: 42000000000, capitalFlow: 1500000000, strength: 92, trend: 'up' }
    ];
    
    // Mock sentiment indicators
    const mockSentimentIndicators: SentimentIndicator[] = [
      { name: '恐慌贪婪指数', value: 72, change: 5, level: 'greed', description: '市场情绪偏向贪婪，投资者信心较强' },
      { name: '融资融券余额', value: 1.65, change: 0.08, level: 'neutral', description: '融资融券余额适中，杠杆水平正常' },
      { name: '换手率', value: 3.2, change: 0.3, level: 'neutral', description: '市场活跃度正常，交易情绪稳定' },
      { name: '波动率指数', value: 18.5, change: -2.1, level: 'fear', description: '市场波动率下降，恐慌情绪缓解' }
    ];
    
    // Mock market overview
    const mockMarketOverview: MarketOverview = {
      fearGreedIndex: 72,
      marginBalance: 1.65,
      marginChange: 0.08,
      turnoverRate: 3.2,
      volatilityIndex: 18.5,
      marketMood: '乐观'
    };
    
    setCapitalFlowData(mockCapitalFlow);
    setSectorData(mockSectorData);
    setSentimentIndicators(mockSentimentIndicators);
    setMarketOverview(mockMarketOverview);
  };

  const getSentimentColor = (level: string) => {
    switch (level) {
      case 'extreme_fear': return 'text-red-600 bg-red-50';
      case 'fear': return 'text-orange-600 bg-orange-50';
      case 'neutral': return 'text-gray-600 bg-gray-50';
      case 'greed': return 'text-green-600 bg-green-50';
      case 'extreme_greed': return 'text-emerald-600 bg-emerald-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down': return <TrendingDown className="w-4 h-4 text-red-500" />;
      default: return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const formatCurrency = (value: number) => {
    if (Math.abs(value) >= 1e9) {
      return `${(value / 1e9).toFixed(1)}B`;
    } else if (Math.abs(value) >= 1e6) {
      return `${(value / 1e6).toFixed(1)}M`;
    }
    return value.toLocaleString();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-lg text-gray-600">加载市场情绪数据...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">市场情绪分析</h1>
          <p className="text-gray-600">实时追踪资金流向、板块轮动和市场情绪指标</p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        )}

        {/* Timeframe Selector */}
        <div className="mb-6">
          <div className="flex space-x-2">
            {['1d', '5d', '1m', '3m'].map((tf) => (
              <button
                key={tf}
                onClick={() => setTimeframe(tf)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  timeframe === tf
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                {tf === '1d' ? '今日' : tf === '5d' ? '5日' : tf === '1m' ? '1月' : '3月'}
              </button>
            ))}
          </div>
        </div>

        {/* Market Overview */}
        {marketOverview && (
          <div className="mb-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-500">恐慌贪婪指数</h3>
                <Target className="w-5 h-5 text-blue-500" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-2">{marketOverview.fearGreedIndex}</div>
              <div className="text-sm text-green-600">市场情绪: {marketOverview.marketMood}</div>
            </div>
            
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-500">融资融券余额</h3>
                <DollarSign className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-2">{marketOverview.marginBalance}万亿</div>
              <div className={`text-sm ${marketOverview.marginChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {marketOverview.marginChange >= 0 ? '+' : ''}{marketOverview.marginChange}%
              </div>
            </div>
            
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-500">换手率</h3>
                <BarChart3 className="w-5 h-5 text-purple-500" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-2">{marketOverview.turnoverRate}%</div>
              <div className="text-sm text-gray-600">市场活跃度正常</div>
            </div>
            
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-500">波动率指数</h3>
                <Activity className="w-5 h-5 text-orange-500" />
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-2">{marketOverview.volatilityIndex}</div>
              <div className="text-sm text-gray-600">波动率适中</div>
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', name: '概览', icon: BarChart3 },
                { id: 'capital', name: '资金流向', icon: DollarSign },
                { id: 'sectors', name: '板块轮动', icon: PieChart },
                { id: 'sentiment', name: '情绪指标', icon: Activity }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="w-4 h-4 mr-2" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Capital Flow Chart */}
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">资金流向趋势</h3>
              {capitalFlowData.length > 0 && (
                <Chart
                  type="line"
                  title="资金流向趋势"
                  data={{
                    labels: capitalFlowData.map(d => d.date),
                    datasets: [
                      {
                        label: '北向资金',
                        data: capitalFlowData.map(d => d.northbound / 1e8),
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true
                      },
                      {
                        label: '主力资金',
                        data: capitalFlowData.map(d => d.mainForce / 1e8),
                        borderColor: 'rgb(239, 68, 68)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        fill: true
                      }
                    ]
                  }}
                />
              )}
            </div>

            {/* Sector Performance */}
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">板块表现</h3>
              <div className="space-y-4">
                {sectorData.slice(0, 5).map((sector, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      {getTrendIcon(sector.trend)}
                      <span className="ml-3 font-medium text-gray-900">{sector.sector}</span>
                    </div>
                    <div className="text-right">
                      <div className={`font-semibold ${
                        sector.change >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {sector.change >= 0 ? '+' : ''}{sector.change.toFixed(2)}%
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatCurrency(sector.capitalFlow)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'capital' && (
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">资金流向详情</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">北向资金</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主力资金</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">散户资金</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">机构资金</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总计</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {capitalFlowData.map((flow, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{flow.date}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                        {formatCurrency(flow.northbound)}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                        flow.mainForce >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {formatCurrency(flow.mainForce)}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                        flow.retail >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {formatCurrency(flow.retail)}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                        flow.institutional >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {formatCurrency(flow.institutional)}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                        flow.total >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {formatCurrency(flow.total)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'sectors' && (
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">板块轮动分析</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {sectorData.map((sector, index) => (
                <div key={index} className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-gray-900">{sector.sector}</h4>
                    {getTrendIcon(sector.trend)}
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">涨跌幅:</span>
                      <span className={`text-sm font-medium ${
                        sector.change >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {sector.change >= 0 ? '+' : ''}{sector.change.toFixed(2)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">成交量:</span>
                      <span className="text-sm text-gray-900">{formatCurrency(sector.volume)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">资金流向:</span>
                      <span className={`text-sm font-medium ${
                        sector.capitalFlow >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {formatCurrency(sector.capitalFlow)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">强度:</span>
                      <span className="text-sm text-gray-900">{sector.strength}/100</span>
                    </div>
                  </div>
                  <div className="mt-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          sector.strength >= 80 ? 'bg-green-500' :
                          sector.strength >= 60 ? 'bg-yellow-500' :
                          sector.strength >= 40 ? 'bg-orange-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${sector.strength}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'sentiment' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {sentimentIndicators.map((indicator, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">{indicator.name}</h3>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getSentimentColor(indicator.level)}`}>
                    {indicator.level.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
                <div className="mb-4">
                  <div className="text-3xl font-bold text-gray-900 mb-2">{indicator.value}</div>
                  <div className={`text-sm font-medium ${
                    indicator.change >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {indicator.change >= 0 ? '+' : ''}{indicator.change} 较昨日
                  </div>
                </div>
                <p className="text-sm text-gray-600">{indicator.description}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}