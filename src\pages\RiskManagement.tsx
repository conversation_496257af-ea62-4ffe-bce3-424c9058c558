import React, { useState, useEffect } from 'react';
import { Shield, AlertTriangle, TrendingDown, TrendingUp, Target, BarChart3, Pie<PERSON><PERSON>, Settings, RefreshCw } from 'lucide-react';
import Chart from '../components/Chart';

interface PortfolioPosition {
  symbol: string;
  name: string;
  quantity: number;
  avgPrice: number;
  currentPrice: number;
  marketValue: number;
  unrealizedPnL: number;
  unrealizedPnLPercent: number;
  weight: number;
  sector: string;
  riskLevel: 'low' | 'medium' | 'high';
}

interface RiskMetrics {
  portfolioValue: number;
  totalPnL: number;
  totalPnLPercent: number;
  beta: number;
  sharpeRatio: number;
  maxDrawdown: number;
  volatility: number;
  var95: number; // Value at Risk 95%
  var99: number; // Value at Risk 99%
  expectedShortfall: number;
  concentrationRisk: number;
  sectorExposure: { [sector: string]: number };
}

interface RiskAlert {
  id: string;
  type: 'position' | 'portfolio' | 'market' | 'concentration';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  recommendation: string;
  timestamp: string;
}

interface RiskLimit {
  id: string;
  name: string;
  type: 'position_size' | 'sector_concentration' | 'single_stock' | 'drawdown' | 'var';
  limit: number;
  current: number;
  unit: '%' | 'amount';
  status: 'safe' | 'warning' | 'breach';
}

export default function RiskManagement() {
  const [positions, setPositions] = useState<PortfolioPosition[]>([]);
  const [riskMetrics, setRiskMetrics] = useState<RiskMetrics | null>(null);
  const [riskAlerts, setRiskAlerts] = useState<RiskAlert[]>([]);
  const [riskLimits, setRiskLimits] = useState<RiskLimit[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadRiskData();
  }, []);

  const loadRiskData = async () => {
    setLoading(true);
    setError('');
    
    try {
      const [positionsRes, metricsRes, alertsRes, limitsRes] = await Promise.all([
        fetch('/api/portfolio/positions'),
        fetch('/api/risk/metrics'),
        fetch('/api/risk/alerts'),
        fetch('/api/risk/limits')
      ]);
      
      if (positionsRes.ok && metricsRes.ok && alertsRes.ok && limitsRes.ok) {
        const [positionsData, metricsData, alertsData, limitsData] = await Promise.all([
          positionsRes.json(),
          metricsRes.json(),
          alertsRes.json(),
          limitsRes.json()
        ]);
        
        setPositions(positionsData.positions || []);
        setRiskMetrics(metricsData.metrics);
        setRiskAlerts(alertsData.alerts || []);
        setRiskLimits(limitsData.limits || []);
      } else {
        // Mock data for development
        setMockData();
      }
    } catch (err) {
      setMockData();
    } finally {
      setLoading(false);
    }
  };

  const setMockData = () => {
    const mockPositions: PortfolioPosition[] = [
      {
        symbol: '000001',
        name: '平安银行',
        quantity: 1000,
        avgPrice: 12.50,
        currentPrice: 13.20,
        marketValue: 13200,
        unrealizedPnL: 700,
        unrealizedPnLPercent: 5.6,
        weight: 15.2,
        sector: '金融',
        riskLevel: 'medium'
      },
      {
        symbol: '000002',
        name: '万科A',
        quantity: 500,
        avgPrice: 18.80,
        currentPrice: 17.50,
        marketValue: 8750,
        unrealizedPnL: -650,
        unrealizedPnLPercent: -6.9,
        weight: 10.1,
        sector: '房地产',
        riskLevel: 'high'
      },
      {
        symbol: '600036',
        name: '招商银行',
        quantity: 800,
        avgPrice: 42.30,
        currentPrice: 45.60,
        marketValue: 36480,
        unrealizedPnL: 2640,
        unrealizedPnLPercent: 7.8,
        weight: 42.1,
        sector: '金融',
        riskLevel: 'medium'
      },
      {
        symbol: '000858',
        name: '五粮液',
        quantity: 200,
        avgPrice: 185.20,
        currentPrice: 178.90,
        marketValue: 35780,
        unrealizedPnL: -1260,
        unrealizedPnLPercent: -3.4,
        weight: 41.3,
        sector: '消费',
        riskLevel: 'medium'
      }
    ];

    const mockMetrics: RiskMetrics = {
      portfolioValue: 94210,
      totalPnL: 1430,
      totalPnLPercent: 1.54,
      beta: 1.12,
      sharpeRatio: 0.85,
      maxDrawdown: -8.5,
      volatility: 18.2,
      var95: -2850,
      var99: -4200,
      expectedShortfall: -5100,
      concentrationRisk: 83.4, // Top 2 positions
      sectorExposure: {
        '金融': 57.3,
        '消费': 41.3,
        '房地产': 10.1,
        '科技': 0,
        '医药': 0
      }
    };

    const mockAlerts: RiskAlert[] = [
      {
        id: '1',
        type: 'concentration',
        severity: 'high',
        title: '集中度风险过高',
        description: '前两大持仓占比超过80%，存在集中度风险',
        recommendation: '建议分散投资，降低单一持仓比例',
        timestamp: '2024-01-16 14:30:00'
      },
      {
        id: '2',
        type: 'position',
        severity: 'medium',
        title: '万科A亏损较大',
        description: '万科A当前亏损6.9%，接近止损线',
        recommendation: '密切关注价格走势，考虑止损或减仓',
        timestamp: '2024-01-16 13:45:00'
      },
      {
        id: '3',
        type: 'market',
        severity: 'medium',
        title: '市场波动加剧',
        description: '近期市场波动率上升至18.2%',
        recommendation: '适当降低仓位，控制风险敞口',
        timestamp: '2024-01-16 12:15:00'
      }
    ];

    const mockLimits: RiskLimit[] = [
      {
        id: '1',
        name: '单一持仓限制',
        type: 'single_stock',
        limit: 30,
        current: 42.1,
        unit: '%',
        status: 'breach'
      },
      {
        id: '2',
        name: '行业集中度',
        type: 'sector_concentration',
        limit: 40,
        current: 57.3,
        unit: '%',
        status: 'breach'
      },
      {
        id: '3',
        name: '最大回撤',
        type: 'drawdown',
        limit: 10,
        current: 8.5,
        unit: '%',
        status: 'warning'
      },
      {
        id: '4',
        name: 'VaR 95%',
        type: 'var',
        limit: 5000,
        current: 2850,
        unit: 'amount',
        status: 'safe'
      }
    ];

    setPositions(mockPositions);
    setRiskMetrics(mockMetrics);
    setRiskAlerts(mockAlerts);
    setRiskLimits(mockLimits);
  };

  const refreshData = async () => {
    setRefreshing(true);
    await loadRiskData();
    setRefreshing(false);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'breach': return 'text-red-600 bg-red-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'safe': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-lg text-gray-600">加载风险数据...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">风险管理</h1>
              <p className="text-gray-600">监控投资组合风险，保护您的资产</p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={refreshData}
                disabled={refreshing}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                刷新数据
              </button>
              <button className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                <Settings className="w-4 h-4 mr-2" />
                风险设置
              </button>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        )}

        {/* Risk Alerts */}
        {riskAlerts.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">风险警报</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {riskAlerts.map((alert) => (
                <div key={alert.id} className={`p-4 rounded-lg border ${getSeverityColor(alert.severity)}`}>
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold">{alert.title}</h3>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(alert.severity)}`}>
                      {alert.severity === 'critical' ? '严重' : 
                       alert.severity === 'high' ? '高' :
                       alert.severity === 'medium' ? '中' : '低'}
                    </span>
                  </div>
                  <p className="text-sm mb-3">{alert.description}</p>
                  <p className="text-sm font-medium mb-2">建议: {alert.recommendation}</p>
                  <p className="text-xs text-gray-500">{alert.timestamp}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', name: '风险概览', icon: Shield },
                { id: 'positions', name: '持仓分析', icon: PieChart },
                { id: 'limits', name: '风险限额', icon: Target },
                { id: 'metrics', name: '风险指标', icon: BarChart3 }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="w-4 h-4 mr-2" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && riskMetrics && (
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium text-gray-500">组合价值</h3>
                  <TrendingUp className="w-5 h-5 text-blue-500" />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-2">¥{riskMetrics.portfolioValue.toLocaleString()}</div>
                <div className={`text-sm font-medium ${
                  riskMetrics.totalPnL >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {riskMetrics.totalPnL >= 0 ? '+' : ''}¥{riskMetrics.totalPnL.toLocaleString()} ({riskMetrics.totalPnLPercent.toFixed(2)}%)
                </div>
              </div>
              
              <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium text-gray-500">VaR (95%)</h3>
                  <AlertTriangle className="w-5 h-5 text-red-500" />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-2">¥{Math.abs(riskMetrics.var95).toLocaleString()}</div>
                <div className="text-sm text-gray-600">日风险价值</div>
              </div>
              
              <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium text-gray-500">最大回撤</h3>
                  <TrendingDown className="w-5 h-5 text-orange-500" />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-2">{riskMetrics.maxDrawdown.toFixed(1)}%</div>
                <div className="text-sm text-gray-600">历史最大损失</div>
              </div>
              
              <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium text-gray-500">夏普比率</h3>
                  <Target className="w-5 h-5 text-purple-500" />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-2">{riskMetrics.sharpeRatio.toFixed(2)}</div>
                <div className="text-sm text-gray-600">风险调整收益</div>
              </div>
            </div>

            {/* Sector Exposure */}
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">行业敞口分布</h3>
              <Chart
                type="bar"
                title="行业分布"
                data={{
                  labels: Object.keys(riskMetrics.sectorExposure),
                  datasets: [{
                    label: '行业占比 (%)',
                    data: Object.values(riskMetrics.sectorExposure),
                    backgroundColor: [
                      'rgba(59, 130, 246, 0.8)',
                      'rgba(16, 185, 129, 0.8)',
                      'rgba(245, 158, 11, 0.8)',
                      'rgba(239, 68, 68, 0.8)',
                      'rgba(139, 92, 246, 0.8)',
                      'rgba(236, 72, 153, 0.8)'
                    ],
                    borderColor: 'rgb(59, 130, 246)',
                    borderWidth: 2
                  }]
                }}
              />
            </div>
          </div>
        )}

        {/* Positions Tab */}
        {activeTab === 'positions' && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">持仓明细</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">股票</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成本价</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">现价</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">市值</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">盈亏</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">权重</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险等级</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {positions.map((position) => (
                      <tr key={position.symbol}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{position.symbol}</div>
                            <div className="text-sm text-gray-500">{position.name}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{position.quantity}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥{position.avgPrice.toFixed(2)}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥{position.currentPrice.toFixed(2)}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥{position.marketValue.toLocaleString()}</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className={`text-sm font-medium ${
                            position.unrealizedPnL >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {position.unrealizedPnL >= 0 ? '+' : ''}¥{position.unrealizedPnL.toLocaleString()}
                          </div>
                          <div className={`text-xs ${
                            position.unrealizedPnLPercent >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            ({position.unrealizedPnLPercent >= 0 ? '+' : ''}{position.unrealizedPnLPercent.toFixed(2)}%)
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{position.weight.toFixed(1)}%</td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getRiskLevelColor(position.riskLevel)}`}>
                            {position.riskLevel === 'high' ? '高风险' : 
                             position.riskLevel === 'medium' ? '中风险' : '低风险'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Risk Limits Tab */}
        {activeTab === 'limits' && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-100">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">风险限额监控</h3>
              <div className="space-y-4">
                {riskLimits.map((limit) => (
                  <div key={limit.id} className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{limit.name}</h4>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(limit.status)}`}>
                        {limit.status === 'breach' ? '超限' : 
                         limit.status === 'warning' ? '警告' : '正常'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex justify-between text-sm text-gray-600 mb-1">
                          <span>当前值: {limit.current}{limit.unit === '%' ? '%' : ''}</span>
                          <span>限额: {limit.limit}{limit.unit === '%' ? '%' : ''}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              limit.status === 'breach' ? 'bg-red-500' :
                              limit.status === 'warning' ? 'bg-yellow-500' : 'bg-green-500'
                            }`}
                            style={{ width: `${Math.min((limit.current / limit.limit) * 100, 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Risk Metrics Tab */}
        {activeTab === 'metrics' && riskMetrics && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">风险指标</h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Beta系数</span>
                    <span className="font-medium">{riskMetrics.beta.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">波动率</span>
                    <span className="font-medium">{riskMetrics.volatility.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">VaR 99%</span>
                    <span className="font-medium text-red-600">¥{Math.abs(riskMetrics.var99).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">预期损失</span>
                    <span className="font-medium text-red-600">¥{Math.abs(riskMetrics.expectedShortfall).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">集中度风险</span>
                    <span className={`font-medium ${
                      riskMetrics.concentrationRisk > 60 ? 'text-red-600' : 
                      riskMetrics.concentrationRisk > 40 ? 'text-yellow-600' : 'text-green-600'
                    }`}>
                      {riskMetrics.concentrationRisk.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">风险评估</h3>
                <div className="space-y-4">
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center mb-2">
                      <AlertTriangle className="w-4 h-4 text-yellow-600 mr-2" />
                      <span className="font-medium text-yellow-800">集中度风险</span>
                    </div>
                    <p className="text-sm text-yellow-700">前两大持仓占比过高，建议分散投资</p>
                  </div>
                  
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Shield className="w-4 h-4 text-blue-600 mr-2" />
                      <span className="font-medium text-blue-800">整体风险</span>
                    </div>
                    <p className="text-sm text-blue-700">组合整体风险水平适中，建议持续监控</p>
                  </div>
                  
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Target className="w-4 h-4 text-green-600 mr-2" />
                      <span className="font-medium text-green-800">风险控制</span>
                    </div>
                    <p className="text-sm text-green-700">VaR指标在可接受范围内</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}