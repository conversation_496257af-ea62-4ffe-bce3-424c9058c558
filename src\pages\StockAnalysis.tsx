import React, { useState, useEffect } from 'react';
import { Search, TrendingUp, AlertCircle, BarChart3 } from 'lucide-react';
import Chart from '@/components/Chart';

interface StockInfo {
  code: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
}

interface AnalysisResult {
  magicNineTurns: {
    signal: string;
    strength: number;
    description: string;
    count: number;
    direction: string;
  };
  macd: {
    signal: string;
    strength: number;
    description: string;
    divergence: boolean;
    trend: string;
  };
  combinedSignal: {
    action: string;
    strength: number;
    confidence: number;
    description: string;
  };
}

interface ChartData {
  dates: string[];
  prices: number[];
  volumes: number[];
  macdLine: number[];
  signalLine: number[];
  histogram: number[];
  magicNineCounts: number[];
}

export default function StockAnalysis() {
  const [stockCode, setStockCode] = useState('');
  const [stockInfo, setStockInfo] = useState<StockInfo | null>(null);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  


  useEffect(() => {
    // Get stock code from URL params
    const urlParams = new URLSearchParams(window.location.search);
    const stockParam = urlParams.get('stock');
    if (stockParam) {
      setStockCode(stockParam);
      handleAnalysis(stockParam);
    }
  }, []);



  const handleAnalysis = async (code?: string) => {
    const targetCode = code || stockCode;
    if (!targetCode.trim()) {
      setError('请输入股票代码');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      // Fetch stock info
      const infoResponse = await fetch(`/api/stock-info/${targetCode}`);
      if (!infoResponse.ok) {
        throw new Error('获取股票信息失败');
      }
      const infoData = await infoResponse.json();
      setStockInfo(infoData.stock);

      // Fetch analysis result
      const analysisResponse = await fetch(`/api/analysis/${targetCode}`);
      if (!analysisResponse.ok) {
        throw new Error('分析失败');
      }
      const analysisData = await analysisResponse.json();
      setAnalysisResult(analysisData.analysis);
      setChartData(analysisData.chartData);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '分析失败');
    } finally {
      setLoading(false);
    }
  };



  const getSignalColor = (signal: string) => {
    switch (signal.toLowerCase()) {
      case 'buy':
      case '买入':
        return 'text-green-600 bg-green-100';
      case 'sell':
      case '卖出':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-6">股票技术分析</h1>
          
          {/* Search Section */}
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <input
                type="text"
                value={stockCode}
                onChange={(e) => setStockCode(e.target.value.toUpperCase())}
                placeholder="请输入股票代码 (如: 000001.SZ, 600000.SH)"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                onKeyPress={(e) => e.key === 'Enter' && handleAnalysis()}
              />
            </div>
            <button
              onClick={() => handleAnalysis()}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-semibold transition-colors flex items-center gap-2"
            >
              <Search size={20} />
              {loading ? '分析中...' : '开始分析'}
            </button>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6 flex items-center gap-2">
              <AlertCircle size={20} />
              {error}
            </div>
          )}
        </div>

        {/* Stock Info */}
        {stockInfo && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800">{stockInfo.name}</h3>
                <p className="text-gray-600">{stockInfo.code}</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">¥{stockInfo.price.toFixed(2)}</p>
                <p className={`text-sm ${
                  stockInfo.change >= 0 ? 'text-red-600' : 'text-green-600'
                }`}>
                  {stockInfo.change >= 0 ? '+' : ''}{stockInfo.change.toFixed(2)} ({stockInfo.changePercent.toFixed(2)}%)
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">成交量</p>
                <p className="text-lg font-semibold">{(stockInfo.volume / 10000).toFixed(2)}万</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">市值</p>
                <p className="text-lg font-semibold">{(stockInfo.marketCap / 100000000).toFixed(2)}亿</p>
              </div>
            </div>
          </div>
        )}

        {/* Analysis Results */}
        {analysisResult && (
          <div className="grid lg:grid-cols-3 gap-6 mb-6">
            {/* Magic Nine Turns */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center gap-2 mb-4">
                <TrendingUp className="text-blue-600" size={24} />
                <h3 className="text-xl font-semibold text-gray-800">神奇九转</h3>
              </div>
              <div className={`inline-block px-3 py-1 rounded-full text-sm font-semibold mb-3 ${
                getSignalColor(analysisResult.magicNineTurns.signal)
              }`}>
                {analysisResult.magicNineTurns.signal}
              </div>
              <p className="text-gray-600 mb-2">{analysisResult.magicNineTurns.description}</p>
              <div className="text-sm text-gray-500">
                <p>计数: {analysisResult.magicNineTurns.count}</p>
                <p>方向: {analysisResult.magicNineTurns.direction}</p>
                <p>强度: {analysisResult.magicNineTurns.strength}/10</p>
              </div>
            </div>

            {/* MACD */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center gap-2 mb-4">
                <BarChart3 className="text-yellow-600" size={24} />
                <h3 className="text-xl font-semibold text-gray-800">MACD分析</h3>
              </div>
              <div className={`inline-block px-3 py-1 rounded-full text-sm font-semibold mb-3 ${
                getSignalColor(analysisResult.macd.signal)
              }`}>
                {analysisResult.macd.signal}
              </div>
              <p className="text-gray-600 mb-2">{analysisResult.macd.description}</p>
              <div className="text-sm text-gray-500">
                <p>背离: {analysisResult.macd.divergence ? '是' : '否'}</p>
                <p>趋势: {analysisResult.macd.trend}</p>
                <p>强度: {analysisResult.macd.strength}/10</p>
              </div>
            </div>

            {/* Combined Signal */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center gap-2 mb-4">
                <AlertCircle className="text-green-600" size={24} />
                <h3 className="text-xl font-semibold text-gray-800">综合信号</h3>
              </div>
              <div className={`inline-block px-3 py-1 rounded-full text-sm font-semibold mb-3 ${
                getSignalColor(analysisResult.combinedSignal.action)
              }`}>
                {analysisResult.combinedSignal.action}
              </div>
              <p className="text-gray-600 mb-2">{analysisResult.combinedSignal.description}</p>
              <div className="text-sm text-gray-500">
                <p>信心度: {analysisResult.combinedSignal.confidence}%</p>
                <p>强度: {analysisResult.combinedSignal.strength}/10</p>
              </div>
            </div>
          </div>
        )}

        {/* Charts */}
        {chartData && (
          <div className="space-y-6">
            <Chart
              type="line"
              title="股价走势图"
              height={400}
              data={{
                labels: chartData.dates,
                datasets: [
                  {
                    label: '股价',
                    data: chartData.prices,
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    fill: false,
                    yAxisID: 'y'
                  },
                  {
                    label: '成交量',
                    data: chartData.volumes,
                    borderColor: 'rgba(156, 163, 175, 0.8)',
                    backgroundColor: 'rgba(156, 163, 175, 0.5)',
                    yAxisID: 'y1'
                  }
                ]
              }}
            />
            
            <Chart
              type="line"
              title="MACD指标"
              height={300}
              data={{
                labels: chartData.dates,
                datasets: [
                  {
                    label: 'MACD线',
                    data: chartData.macdLine,
                    borderColor: 'rgb(239, 68, 68)',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    fill: false
                  },
                  {
                    label: '信号线',
                    data: chartData.signalLine,
                    borderColor: 'rgb(34, 197, 94)',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    fill: false
                  },
                  {
                    label: '柱状图',
                    data: chartData.histogram,
                    borderColor: 'rgba(75, 85, 99, 0.8)',
                    backgroundColor: chartData.histogram.map(val => 
                      val >= 0 ? 'rgba(34, 197, 94, 0.6)' : 'rgba(239, 68, 68, 0.6)'
                    )
                  }
                ]
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
}