import React, { useState, useEffect } from 'react';
import { Search, Filter, TrendingUp, BarChart3, Download } from 'lucide-react';

interface ScreeningFilters {
  marketCap: {
    min: number;
    max: number;
  };
  priceRange: {
    min: number;
    max: number;
  };
  changePercent: {
    min: number;
    max: number;
  };
  volume: {
    min: number;
  };
  magicNineTurns: {
    enabled: boolean;
    signal: string;
    minStrength: number;
  };
  macd: {
    enabled: boolean;
    signal: string;
    requireDivergence: boolean;
    minStrength: number;
  };
  combinedSignal: {
    enabled: boolean;
    minConfidence: number;
  };
}

interface ScreeningResult {
  code: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
  magicNineTurns: {
    signal: string;
    strength: number;
    count: number;
  };
  macd: {
    signal: string;
    strength: number;
    divergence: boolean;
  };
  combinedSignal: {
    action: string;
    confidence: number;
    strength: number;
  };
}

export default function StockScreening() {
  const [filters, setFilters] = useState<ScreeningFilters>({
    marketCap: { min: 0, max: 10000 },
    priceRange: { min: 0, max: 1000 },
    changePercent: { min: -20, max: 20 },
    volume: { min: 0 },
    magicNineTurns: {
      enabled: false,
      signal: 'any',
      minStrength: 5
    },
    macd: {
      enabled: false,
      signal: 'any',
      requireDivergence: false,
      minStrength: 5
    },
    combinedSignal: {
      enabled: false,
      minConfidence: 60
    }
  });

  const [results, setResults] = useState<ScreeningResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [sortBy, setSortBy] = useState('marketCap');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const handleScreening = async () => {
    setLoading(true);
    setError('');
    
    try {
      const response = await fetch('/api/screening', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ filters })
      });
      
      if (!response.ok) {
        throw new Error('筛选失败');
      }
      
      const data = await response.json();
      setResults(data.results || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : '筛选失败');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (category: string, field: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof ScreeningFilters],
        [field]: value
      }
    }));
  };

  const sortResults = (results: ScreeningResult[]) => {
    return [...results].sort((a, b) => {
      let aValue: number;
      let bValue: number;
      
      switch (sortBy) {
        case 'marketCap':
          aValue = a.marketCap;
          bValue = b.marketCap;
          break;
        case 'changePercent':
          aValue = a.changePercent;
          bValue = b.changePercent;
          break;
        case 'volume':
          aValue = a.volume;
          bValue = b.volume;
          break;
        case 'combinedConfidence':
          aValue = a.combinedSignal.confidence;
          bValue = b.combinedSignal.confidence;
          break;
        default:
          aValue = a.price;
          bValue = b.price;
      }
      
      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });
  };

  const exportResults = () => {
    if (results.length === 0) return;
    
    const csvContent = [
      ['股票代码', '股票名称', '价格', '涨跌幅(%)', '成交量', '市值(亿)', '神奇九转信号', '神奇九转强度', 'MACD信号', 'MACD强度', '综合信号', '信心度(%)'].join(','),
      ...results.map(stock => [
        stock.code,
        stock.name,
        stock.price.toFixed(2),
        stock.changePercent.toFixed(2),
        (stock.volume / 10000).toFixed(0),
        (stock.marketCap / 100000000).toFixed(2),
        stock.magicNineTurns.signal,
        stock.magicNineTurns.strength,
        stock.macd.signal,
        stock.macd.strength,
        stock.combinedSignal.action,
        stock.combinedSignal.confidence
      ].join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `stock_screening_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const getSignalColor = (signal: string) => {
    switch (signal.toLowerCase()) {
      case 'buy':
      case '买入':
        return 'text-green-600 bg-green-100';
      case 'sell':
      case '卖出':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-6">股票筛选</h1>
          
          {/* Filters */}
          <div className="grid lg:grid-cols-2 gap-6">
            {/* Basic Filters */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <Filter size={20} />
                基础筛选条件
              </h3>
              
              {/* Market Cap */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">市值范围 (亿元)</label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="最小值"
                    value={filters.marketCap.min}
                    onChange={(e) => handleFilterChange('marketCap', 'min', Number(e.target.value))}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="number"
                    placeholder="最大值"
                    value={filters.marketCap.max}
                    onChange={(e) => handleFilterChange('marketCap', 'max', Number(e.target.value))}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              
              {/* Price Range */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">价格范围 (元)</label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="最小值"
                    value={filters.priceRange.min}
                    onChange={(e) => handleFilterChange('priceRange', 'min', Number(e.target.value))}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="number"
                    placeholder="最大值"
                    value={filters.priceRange.max}
                    onChange={(e) => handleFilterChange('priceRange', 'max', Number(e.target.value))}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              
              {/* Change Percent */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">涨跌幅范围 (%)</label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="最小值"
                    value={filters.changePercent.min}
                    onChange={(e) => handleFilterChange('changePercent', 'min', Number(e.target.value))}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="number"
                    placeholder="最大值"
                    value={filters.changePercent.max}
                    onChange={(e) => handleFilterChange('changePercent', 'max', Number(e.target.value))}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
            
            {/* Technical Filters */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <TrendingUp size={20} />
                技术指标筛选
              </h3>
              
              {/* Magic Nine Turns */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <input
                    type="checkbox"
                    checked={filters.magicNineTurns.enabled}
                    onChange={(e) => handleFilterChange('magicNineTurns', 'enabled', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label className="font-medium text-gray-700">神奇九转筛选</label>
                </div>
                {filters.magicNineTurns.enabled && (
                  <div className="space-y-2">
                    <select
                      value={filters.magicNineTurns.signal}
                      onChange={(e) => handleFilterChange('magicNineTurns', 'signal', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="any">任意信号</option>
                      <option value="buy">买入信号</option>
                      <option value="sell">卖出信号</option>
                    </select>
                    <div>
                      <label className="block text-sm text-gray-600 mb-1">最小强度: {filters.magicNineTurns.minStrength}</label>
                      <input
                        type="range"
                        min="1"
                        max="10"
                        value={filters.magicNineTurns.minStrength}
                        onChange={(e) => handleFilterChange('magicNineTurns', 'minStrength', Number(e.target.value))}
                        className="w-full"
                      />
                    </div>
                  </div>
                )}
              </div>
              
              {/* MACD */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <input
                    type="checkbox"
                    checked={filters.macd.enabled}
                    onChange={(e) => handleFilterChange('macd', 'enabled', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label className="font-medium text-gray-700">MACD筛选</label>
                </div>
                {filters.macd.enabled && (
                  <div className="space-y-2">
                    <select
                      value={filters.macd.signal}
                      onChange={(e) => handleFilterChange('macd', 'signal', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="any">任意信号</option>
                      <option value="buy">买入信号</option>
                      <option value="sell">卖出信号</option>
                    </select>
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={filters.macd.requireDivergence}
                        onChange={(e) => handleFilterChange('macd', 'requireDivergence', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label className="text-sm text-gray-700">要求背离</label>
                    </div>
                    <div>
                      <label className="block text-sm text-gray-600 mb-1">最小强度: {filters.macd.minStrength}</label>
                      <input
                        type="range"
                        min="1"
                        max="10"
                        value={filters.macd.minStrength}
                        onChange={(e) => handleFilterChange('macd', 'minStrength', Number(e.target.value))}
                        className="w-full"
                      />
                    </div>
                  </div>
                )}
              </div>
              
              {/* Combined Signal */}
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <input
                    type="checkbox"
                    checked={filters.combinedSignal.enabled}
                    onChange={(e) => handleFilterChange('combinedSignal', 'enabled', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label className="font-medium text-gray-700">综合信号筛选</label>
                </div>
                {filters.combinedSignal.enabled && (
                  <div>
                    <label className="block text-sm text-gray-600 mb-1">最小信心度: {filters.combinedSignal.minConfidence}%</label>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={filters.combinedSignal.minConfidence}
                      onChange={(e) => handleFilterChange('combinedSignal', 'minConfidence', Number(e.target.value))}
                      className="w-full"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-4 mt-6">
            <button
              onClick={handleScreening}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-semibold transition-colors flex items-center gap-2"
            >
              <Search size={20} />
              {loading ? '筛选中...' : '开始筛选'}
            </button>
            
            {results.length > 0 && (
              <button
                onClick={exportResults}
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors flex items-center gap-2"
              >
                <Download size={20} />
                导出结果
              </button>
            )}
          </div>
          
          {/* Error Message */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-4">
              {error}
            </div>
          )}
        </div>
        
        {/* Results */}
        {results.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold text-gray-800">筛选结果 ({results.length})</h2>
              
              {/* Sort Controls */}
              <div className="flex gap-2">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                >
                  <option value="marketCap">按市值排序</option>
                  <option value="changePercent">按涨跌幅排序</option>
                  <option value="volume">按成交量排序</option>
                  <option value="combinedConfidence">按信心度排序</option>
                </select>
                <button
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  {sortOrder === 'asc' ? '↑' : '↓'}
                </button>
              </div>
            </div>
            
            {/* Results Table */}
            <div className="overflow-x-auto">
              <table className="w-full table-auto">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">股票</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">价格</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">涨跌幅</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">成交量</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">市值</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">神奇九转</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">MACD</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">综合信号</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {sortResults(results).map((stock) => (
                    <tr key={stock.code} className="border-t border-gray-200 hover:bg-gray-50">
                      <td className="px-4 py-3">
                        <div>
                          <div className="font-semibold text-gray-800">{stock.name}</div>
                          <div className="text-sm text-gray-500">{stock.code}</div>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="font-semibold">¥{stock.price.toFixed(2)}</div>
                      </td>
                      <td className="px-4 py-3">
                        <div className={`font-semibold ${
                          stock.changePercent >= 0 ? 'text-red-600' : 'text-green-600'
                        }`}>
                          {stock.changePercent >= 0 ? '+' : ''}{stock.changePercent.toFixed(2)}%
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div>{(stock.volume / 10000).toFixed(0)}万</div>
                      </td>
                      <td className="px-4 py-3">
                        <div>{(stock.marketCap / 100000000).toFixed(2)}亿</div>
                      </td>
                      <td className="px-4 py-3">
                        <div className={`inline-block px-2 py-1 rounded text-xs font-semibold ${
                          getSignalColor(stock.magicNineTurns.signal)
                        }`}>
                          {stock.magicNineTurns.signal}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          强度: {stock.magicNineTurns.strength}/10
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className={`inline-block px-2 py-1 rounded text-xs font-semibold ${
                          getSignalColor(stock.macd.signal)
                        }`}>
                          {stock.macd.signal}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          强度: {stock.macd.strength}/10
                          {stock.macd.divergence && <span className="ml-1 text-orange-600">背离</span>}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className={`inline-block px-2 py-1 rounded text-xs font-semibold ${
                          getSignalColor(stock.combinedSignal.action)
                        }`}>
                          {stock.combinedSignal.action}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          信心度: {stock.combinedSignal.confidence}%
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <button
                          onClick={() => window.location.href = `/analysis?stock=${stock.code}`}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          详细分析
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}