import React, { useState, useEffect } from 'react';
import { Plus, Play, Save, Download, Upload, Settings, TrendingUp, Bar<PERSON>hart3, Target, AlertTriangle } from 'lucide-react';
import Chart from '../components/Chart';

interface StrategyCondition {
  id: string;
  type: 'technical' | 'fundamental' | 'sentiment';
  indicator: string;
  operator: '>' | '<' | '=' | '>=' | '<=' | 'cross_above' | 'cross_below';
  value: number | string;
  timeframe: string;
}

interface Strategy {
  id: string;
  name: string;
  description: string;
  conditions: StrategyCondition[];
  entryRules: StrategyCondition[];
  exitRules: StrategyCondition[];
  riskManagement: {
    stopLoss: number;
    takeProfit: number;
    positionSize: number;
    maxPositions: number;
  };
  created: string;
  lastModified: string;
}

interface BacktestResult {
  totalReturn: number;
  annualizedReturn: number;
  sharpeRatio: number;
  maxDrawdown: number;
  winRate: number;
  totalTrades: number;
  profitFactor: number;
  volatility: number;
  equityCurve: { date: string; value: number }[];
  trades: {
    date: string;
    symbol: string;
    action: 'buy' | 'sell';
    price: number;
    quantity: number;
    pnl: number;
  }[];
}

interface IndicatorOption {
  value: string;
  label: string;
  category: 'technical' | 'fundamental' | 'sentiment';
}

export default function StrategyBuilder() {
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [currentStrategy, setCurrentStrategy] = useState<Strategy | null>(null);
  const [backtestResult, setBacktestResult] = useState<BacktestResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('builder');
  const [showConditionModal, setShowConditionModal] = useState(false);
  const [editingCondition, setEditingCondition] = useState<StrategyCondition | null>(null);

  const indicatorOptions: IndicatorOption[] = [
    // Technical Indicators
    { value: 'sma_20', label: 'SMA(20)', category: 'technical' },
    { value: 'ema_12', label: 'EMA(12)', category: 'technical' },
    { value: 'rsi_14', label: 'RSI(14)', category: 'technical' },
    { value: 'macd_signal', label: 'MACD信号线', category: 'technical' },
    { value: 'kdj_k', label: 'KDJ-K值', category: 'technical' },
    { value: 'bollinger_upper', label: '布林上轨', category: 'technical' },
    { value: 'bollinger_lower', label: '布林下轨', category: 'technical' },
    { value: 'volume_ma', label: '成交量均线', category: 'technical' },
    // Fundamental Indicators
    { value: 'pe_ratio', label: '市盈率', category: 'fundamental' },
    { value: 'pb_ratio', label: '市净率', category: 'fundamental' },
    { value: 'roe', label: 'ROE', category: 'fundamental' },
    { value: 'debt_ratio', label: '资产负债率', category: 'fundamental' },
    { value: 'revenue_growth', label: '营收增长率', category: 'fundamental' },
    // Sentiment Indicators
    { value: 'capital_flow', label: '资金流向', category: 'sentiment' },
    { value: 'sentiment_score', label: '情绪得分', category: 'sentiment' },
    { value: 'fear_greed_index', label: '恐慌贪婪指数', category: 'sentiment' }
  ];

  useEffect(() => {
    loadStrategies();
  }, []);

  const loadStrategies = async () => {
    try {
      const response = await fetch('/api/strategy/strategies');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          const strategies = result.data.strategies.map((s: any) => ({
            id: s.id,
            name: s.name,
            description: s.description,
            conditions: [],
            entryRules: s.entry_rules || [],
            exitRules: s.exit_rules || [],
            riskManagement: s.risk_management || {
              stopLoss: 5,
              takeProfit: 10,
              positionSize: 10,
              maxPositions: 5
            },
            created: s.created_at,
            lastModified: s.updated_at || s.created_at
          }));
          setStrategies(strategies);
        } else {
          setMockStrategies();
        }
      } else {
        // Mock data for development
        setMockStrategies();
      }
    } catch (err) {
      setMockStrategies();
    }
  };

  const setMockStrategies = () => {
    const mockStrategies: Strategy[] = [
      {
        id: '1',
        name: '均线突破策略',
        description: '基于移动平均线的突破交易策略',
        conditions: [],
        entryRules: [
          {
            id: '1',
            type: 'technical',
            indicator: 'sma_20',
            operator: 'cross_above',
            value: 'sma_50',
            timeframe: '1d'
          }
        ],
        exitRules: [
          {
            id: '2',
            type: 'technical',
            indicator: 'sma_20',
            operator: 'cross_below',
            value: 'sma_50',
            timeframe: '1d'
          }
        ],
        riskManagement: {
          stopLoss: 5,
          takeProfit: 10,
          positionSize: 10,
          maxPositions: 5
        },
        created: '2024-01-15',
        lastModified: '2024-01-16'
      }
    ];
    setStrategies(mockStrategies);
  };

  const createNewStrategy = () => {
    const newStrategy: Strategy = {
      id: Date.now().toString(),
      name: '新策略',
      description: '',
      conditions: [],
      entryRules: [],
      exitRules: [],
      riskManagement: {
        stopLoss: 5,
        takeProfit: 10,
        positionSize: 10,
        maxPositions: 5
      },
      created: new Date().toISOString().split('T')[0],
      lastModified: new Date().toISOString().split('T')[0]
    };
    setCurrentStrategy(newStrategy);
    setActiveTab('builder');
  };

  const saveStrategy = async () => {
    if (!currentStrategy) return;
    
    setLoading(true);
    try {
      const strategyData = {
        name: currentStrategy.name,
        description: currentStrategy.description,
        entry_rules: currentStrategy.entryRules,
        exit_rules: currentStrategy.exitRules,
        risk_management: currentStrategy.riskManagement
      };
      
      const response = await fetch('/api/strategy/strategies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(strategyData)
      });
      
      if (!response.ok) {
        throw new Error('Failed to save strategy');
      }
      
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to save strategy');
      }
      
      const savedStrategy = result.data.strategy;
      const updatedStrategies = [...strategies];
      const existingIndex = updatedStrategies.findIndex(s => s.id === savedStrategy.id);
      
      const strategyToAdd = {
        id: savedStrategy.id,
        name: savedStrategy.name,
        description: savedStrategy.description,
        conditions: [],
        entryRules: savedStrategy.entry_rules,
        exitRules: savedStrategy.exit_rules,
        riskManagement: savedStrategy.risk_management,
        created: savedStrategy.created_at,
        lastModified: savedStrategy.updated_at || savedStrategy.created_at
      };
      
      if (existingIndex >= 0) {
        updatedStrategies[existingIndex] = strategyToAdd;
      } else {
        updatedStrategies.push(strategyToAdd);
      }
      setStrategies(updatedStrategies);
      alert('策略保存成功！');
    } catch (err) {
      setError(err instanceof Error ? err.message : '保存策略失败');
    } finally {
      setLoading(false);
    }
  };

  const runBacktest = async () => {
    if (!currentStrategy) return;
    
    setLoading(true);
    setError('');
    
    try {
      // First save the strategy if it doesn't have an ID
      let strategyId = currentStrategy.id;
      if (!strategyId) {
        const saveResponse = await fetch('/api/strategy/strategies', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(currentStrategy)
        });
        
        if (!saveResponse.ok) {
          throw new Error('Failed to save strategy');
        }
        
        const saveResult = await saveResponse.json();
        if (!saveResult.success) {
          throw new Error(saveResult.error || 'Failed to save strategy');
        }
        
        strategyId = saveResult.data.strategy.id;
        setCurrentStrategy(prev => ({ ...prev, id: strategyId }));
      }
      
      // Run backtest
      const response = await fetch(`/api/strategy/strategies/${strategyId}/backtest`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          start_date: '2023-01-01',
          end_date: '2024-01-01',
          initial_capital: 100000,
          stocks: ['000001.SZ'] // Default to Ping An Bank
        })
      });
      
      if (!response.ok) {
        throw new Error('Backtest failed');
      }
      
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Backtest failed');
      }
      
      setBacktestResult(result.data.backtest_result);
      setActiveTab('results');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      // For development, use mock data
      setMockBacktestResult();
      setActiveTab('results');
    } finally {
      setLoading(false);
    }
  };

  const setMockBacktestResult = () => {
    const mockResult: BacktestResult = {
      totalReturn: 25.6,
      annualizedReturn: 18.2,
      sharpeRatio: 1.45,
      maxDrawdown: -8.3,
      winRate: 62.5,
      totalTrades: 48,
      profitFactor: 1.8,
      volatility: 12.5,
      equityCurve: [
        { date: '2023-01-01', value: 100000 },
        { date: '2023-03-01', value: 105000 },
        { date: '2023-06-01', value: 112000 },
        { date: '2023-09-01', value: 118000 },
        { date: '2023-12-01', value: 125600 }
      ],
      trades: [
        { date: '2023-01-15', symbol: '000001', action: 'buy', price: 12.50, quantity: 800, pnl: 0 },
        { date: '2023-01-25', symbol: '000001', action: 'sell', price: 13.20, quantity: 800, pnl: 560 }
      ]
    };
    setBacktestResult(mockResult);
  };

  const addCondition = (type: 'entry' | 'exit') => {
    const newCondition: StrategyCondition = {
      id: Date.now().toString(),
      type: 'technical',
      indicator: 'sma_20',
      operator: '>',
      value: 0,
      timeframe: '1d'
    };
    
    if (currentStrategy) {
      const updatedStrategy = { ...currentStrategy };
      if (type === 'entry') {
        updatedStrategy.entryRules.push(newCondition);
      } else {
        updatedStrategy.exitRules.push(newCondition);
      }
      setCurrentStrategy(updatedStrategy);
    }
  };

  const removeCondition = (conditionId: string, type: 'entry' | 'exit') => {
    if (currentStrategy) {
      const updatedStrategy = { ...currentStrategy };
      if (type === 'entry') {
        updatedStrategy.entryRules = updatedStrategy.entryRules.filter(c => c.id !== conditionId);
      } else {
        updatedStrategy.exitRules = updatedStrategy.exitRules.filter(c => c.id !== conditionId);
      }
      setCurrentStrategy(updatedStrategy);
    }
  };

  const updateCondition = (conditionId: string, field: keyof StrategyCondition, value: any, type: 'entry' | 'exit') => {
    if (currentStrategy) {
      const updatedStrategy = { ...currentStrategy };
      const rules = type === 'entry' ? updatedStrategy.entryRules : updatedStrategy.exitRules;
      const conditionIndex = rules.findIndex(c => c.id === conditionId);
      if (conditionIndex >= 0) {
        rules[conditionIndex] = { ...rules[conditionIndex], [field]: value };
        setCurrentStrategy(updatedStrategy);
      }
    }
  };

  const renderConditionEditor = (condition: StrategyCondition, type: 'entry' | 'exit') => (
    <div key={condition.id} className="p-4 border border-gray-200 rounded-lg bg-gray-50">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">指标</label>
          <select
            value={condition.indicator}
            onChange={(e) => updateCondition(condition.id, 'indicator', e.target.value, type)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {indicatorOptions.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">操作符</label>
          <select
            value={condition.operator}
            onChange={(e) => updateCondition(condition.id, 'operator', e.target.value as any, type)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value=">">大于</option>
            <option value="<">小于</option>
            <option value=">=">大于等于</option>
            <option value="<=">小于等于</option>
            <option value="=">等于</option>
            <option value="cross_above">向上突破</option>
            <option value="cross_below">向下突破</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">数值</label>
          <input
            type="number"
            value={condition.value}
            onChange={(e) => updateCondition(condition.id, 'value', parseFloat(e.target.value), type)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">时间周期</label>
          <select
            value={condition.timeframe}
            onChange={(e) => updateCondition(condition.id, 'timeframe', e.target.value, type)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="1m">1分钟</option>
            <option value="5m">5分钟</option>
            <option value="15m">15分钟</option>
            <option value="1h">1小时</option>
            <option value="1d">日线</option>
            <option value="1w">周线</option>
          </select>
        </div>
        
        <div className="flex items-end">
          <button
            onClick={() => removeCondition(condition.id, type)}
            className="px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
          >
            删除
          </button>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-lg text-gray-600">处理中...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">策略构建器</h1>
              <p className="text-gray-600">构建、测试和优化您的交易策略</p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={createNewStrategy}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                新建策略
              </button>
              {currentStrategy && (
                <>
                  <button
                    onClick={saveStrategy}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    保存策略
                  </button>
                  <button
                    onClick={runBacktest}
                    className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    运行回测
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Strategy List */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">我的策略</h3>
              <div className="space-y-3">
                {strategies.map((strategy) => (
                  <div
                    key={strategy.id}
                    onClick={() => setCurrentStrategy(strategy)}
                    className={`p-3 rounded-lg cursor-pointer transition-colors ${
                      currentStrategy?.id === strategy.id
                        ? 'bg-blue-50 border-blue-200 border'
                        : 'bg-gray-50 hover:bg-gray-100'
                    }`}
                  >
                    <div className="font-medium text-gray-900">{strategy.name}</div>
                    <div className="text-sm text-gray-500 mt-1">{strategy.description}</div>
                    <div className="text-xs text-gray-400 mt-2">修改: {strategy.lastModified}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {currentStrategy ? (
              <>
                {/* Tab Navigation */}
                <div className="mb-6">
                  <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8">
                      {[
                        { id: 'builder', name: '策略构建', icon: Settings },
                        { id: 'results', name: '回测结果', icon: BarChart3 }
                      ].map((tab) => (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                            activeTab === tab.id
                              ? 'border-blue-500 text-blue-600'
                              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                          }`}
                        >
                          <tab.icon className="w-4 h-4 mr-2" />
                          {tab.name}
                        </button>
                      ))}
                    </nav>
                  </div>
                </div>

                {/* Strategy Builder Tab */}
                {activeTab === 'builder' && (
                  <div className="space-y-6">
                    {/* Basic Info */}
                    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">基本信息</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">策略名称</label>
                          <input
                            type="text"
                            value={currentStrategy.name}
                            onChange={(e) => setCurrentStrategy({ ...currentStrategy, name: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">策略描述</label>
                          <input
                            type="text"
                            value={currentStrategy.description}
                            onChange={(e) => setCurrentStrategy({ ...currentStrategy, description: e.target.value })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Entry Rules */}
                    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">入场条件</h3>
                        <button
                          onClick={() => addCondition('entry')}
                          className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                          <Plus className="w-4 h-4 mr-1" />
                          添加条件
                        </button>
                      </div>
                      <div className="space-y-4">
                        {currentStrategy.entryRules.map(condition => renderConditionEditor(condition, 'entry'))}
                        {currentStrategy.entryRules.length === 0 && (
                          <div className="text-center py-8 text-gray-500">
                            暂无入场条件，点击上方按钮添加
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Exit Rules */}
                    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">出场条件</h3>
                        <button
                          onClick={() => addCondition('exit')}
                          className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                          <Plus className="w-4 h-4 mr-1" />
                          添加条件
                        </button>
                      </div>
                      <div className="space-y-4">
                        {currentStrategy.exitRules.map(condition => renderConditionEditor(condition, 'exit'))}
                        {currentStrategy.exitRules.length === 0 && (
                          <div className="text-center py-8 text-gray-500">
                            暂无出场条件，点击上方按钮添加
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Risk Management */}
                    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">风险管理</h3>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">止损 (%)</label>
                          <input
                            type="number"
                            value={currentStrategy.riskManagement.stopLoss}
                            onChange={(e) => setCurrentStrategy({
                              ...currentStrategy,
                              riskManagement: {
                                ...currentStrategy.riskManagement,
                                stopLoss: parseFloat(e.target.value)
                              }
                            })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">止盈 (%)</label>
                          <input
                            type="number"
                            value={currentStrategy.riskManagement.takeProfit}
                            onChange={(e) => setCurrentStrategy({
                              ...currentStrategy,
                              riskManagement: {
                                ...currentStrategy.riskManagement,
                                takeProfit: parseFloat(e.target.value)
                              }
                            })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">仓位大小 (%)</label>
                          <input
                            type="number"
                            value={currentStrategy.riskManagement.positionSize}
                            onChange={(e) => setCurrentStrategy({
                              ...currentStrategy,
                              riskManagement: {
                                ...currentStrategy.riskManagement,
                                positionSize: parseFloat(e.target.value)
                              }
                            })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">最大持仓数</label>
                          <input
                            type="number"
                            value={currentStrategy.riskManagement.maxPositions}
                            onChange={(e) => setCurrentStrategy({
                              ...currentStrategy,
                              riskManagement: {
                                ...currentStrategy.riskManagement,
                                maxPositions: parseInt(e.target.value)
                              }
                            })}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Backtest Results Tab */}
                {activeTab === 'results' && backtestResult && (
                  <div className="space-y-6">
                    {/* Performance Metrics */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="text-sm font-medium text-gray-500">总收益率</h3>
                          <TrendingUp className="w-5 h-5 text-green-500" />
                        </div>
                        <div className="text-2xl font-bold text-gray-900 mb-2">{backtestResult.totalReturn.toFixed(2)}%</div>
                        <div className="text-sm text-gray-600">年化: {backtestResult.annualizedReturn.toFixed(2)}%</div>
                      </div>
                      
                      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="text-sm font-medium text-gray-500">夏普比率</h3>
                          <Target className="w-5 h-5 text-blue-500" />
                        </div>
                        <div className="text-2xl font-bold text-gray-900 mb-2">{backtestResult.sharpeRatio.toFixed(2)}</div>
                        <div className="text-sm text-gray-600">风险调整收益</div>
                      </div>
                      
                      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="text-sm font-medium text-gray-500">最大回撤</h3>
                          <AlertTriangle className="w-5 h-5 text-red-500" />
                        </div>
                        <div className="text-2xl font-bold text-gray-900 mb-2">{backtestResult.maxDrawdown.toFixed(2)}%</div>
                        <div className="text-sm text-gray-600">风险控制</div>
                      </div>
                      
                      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="text-sm font-medium text-gray-500">胜率</h3>
                          <BarChart3 className="w-5 h-5 text-purple-500" />
                        </div>
                        <div className="text-2xl font-bold text-gray-900 mb-2">{backtestResult.winRate.toFixed(1)}%</div>
                        <div className="text-sm text-gray-600">{backtestResult.totalTrades} 笔交易</div>
                      </div>
                    </div>

                    {/* Equity Curve */}
                    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">资金曲线</h3>
                      <Chart
                        type="line"
                        title="策略表现"
                        data={{
                          labels: backtestResult.equityCurve.map(point => point.date),
                          datasets: [
                            {
                              label: '账户价值',
                              data: backtestResult.equityCurve.map(point => point.value),
                              borderColor: 'rgb(59, 130, 246)',
                              backgroundColor: 'rgba(59, 130, 246, 0.1)',
                              fill: true
                            }
                          ]
                        }}
                      />
                    </div>

                    {/* Trade History */}
                    <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">交易记录</h3>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">股票</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价格</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">盈亏</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {backtestResult.trades.slice(0, 10).map((trade, index) => (
                              <tr key={index}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{trade.date}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{trade.symbol}</td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                    trade.action === 'buy' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                  }`}>
                                    {trade.action === 'buy' ? '买入' : '卖出'}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥{trade.price.toFixed(2)}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{trade.quantity}</td>
                                <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                                  trade.pnl >= 0 ? 'text-green-600' : 'text-red-600'
                                }`}>
                                  {trade.pnl >= 0 ? '+' : ''}¥{trade.pnl.toFixed(2)}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="bg-white rounded-xl shadow-sm p-12 border border-gray-100 text-center">
                <Settings className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">开始构建您的交易策略</h3>
                <p className="text-gray-600 mb-6">选择一个现有策略或创建新策略来开始</p>
                <button
                  onClick={createNewStrategy}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  创建新策略
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}