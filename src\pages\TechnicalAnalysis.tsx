import React, { useState, useEffect } from 'react';
import { Search, TrendingUp, BarChart3, Activity, Target, RefreshCw } from 'lucide-react';
import { TechnicalChart } from '../components/Chart';
import { useTechnicalIndicators, useStockInfo } from '../hooks/useApi';
import { handleApiError } from '../lib/api';

interface StockInfo {
  code: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
}

interface TechnicalIndicators {
  kdj: {
    k: number;
    d: number;
    j: number;
    signal: string;
    description: string;
  };
  rsi: {
    value: number;
    signal: string;
    overbought: boolean;
    oversold: boolean;
    description: string;
  };
  bollingerBands: {
    upper: number;
    middle: number;
    lower: number;
    percentB: number;
    signal: string;
    description: string;
  };
  macd: {
    macd: number;
    signal: number;
    histogram: number;
    divergence: boolean;
    trend: string;
    description: string;
  };
  magicNineTurns: {
    count: number;
    direction: string;
    signal: string;
    strength: number;
    description: string;
  };
}

interface CandlestickData {
  x: string | Date;
  o: number; // open
  h: number; // high
  l: number; // low
  c: number; // close
}

interface VolumeData {
  x: string | Date;
  y: number;
}

interface IndicatorData {
  time: string;
  [key: string]: number | string;
}

export default function TechnicalAnalysis() {
  const [stockCode, setStockCode] = useState('');
  const [stockInfo, setStockInfo] = useState<StockInfo | null>(null);
  const [indicators, setIndicators] = useState<TechnicalIndicators | null>(null);
  const [candlestickData, setCandlestickData] = useState<CandlestickData[]>([]);
  const [volumeData, setVolumeData] = useState<VolumeData[]>([]);
  const [indicatorData, setIndicatorData] = useState<{
    macd?: {
      macd: { x: string; y: number }[];
      signal: { x: string; y: number }[];
      histogram: { x: string; y: number }[];
    };
    rsi?: { x: string; y: number }[];
    kdj?: {
      k: { x: string; y: number }[];
      d: { x: string; y: number }[];
      j: { x: string; y: number }[];
    };
    bollingerBands?: {
      upper: { x: string; y: number }[];
      middle: { x: string; y: number }[];
      lower: { x: string; y: number }[];
    };
  }>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [timeframe, setTimeframe] = useState('1d');
  const [enabledIndicators, setEnabledIndicators] = useState([
    { name: 'MACD', enabled: true, color: '#ff7f0e', yAxisID: 'macd' },
    { name: 'RSI', enabled: true, color: '#9467bd', yAxisID: 'rsi' },
    { name: 'KDJ', enabled: true, color: '#8c564b', yAxisID: 'kdj' },
    { name: 'Bollinger Bands', enabled: true, color: '#bcbd22', yAxisID: 'price' },
  ]);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const stockParam = urlParams.get('stock');
    if (stockParam) {
      setStockCode(stockParam);
      handleAnalysis(stockParam);
    }
  }, []);

  const handleAnalysis = async (code?: string) => {
    const targetCode = code || stockCode;
    if (!targetCode.trim()) {
      setError('请输入股票代码');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      // Fetch stock info
      const infoResponse = await fetch(`/api/stock-info/${targetCode}`);
      if (!infoResponse.ok) {
        throw new Error('获取股票信息失败');
      }
      const infoData = await infoResponse.json();
      setStockInfo(infoData.stock);

      // Fetch technical indicators
      const indicatorsResponse = await fetch(`/api/technical/indicators/${targetCode}?timeframe=${timeframe}`);
      if (!indicatorsResponse.ok) {
        throw new Error('获取技术指标失败');
      }
      const indicatorsData = await indicatorsResponse.json();
      setIndicators(indicatorsData.indicators);
      setCandlestickData(indicatorsData.candlestickData || []);
      setVolumeData(indicatorsData.volumeData || []);
      setIndicatorData(indicatorsData.indicatorData || {});
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '分析失败');
    } finally {
      setLoading(false);
    }
  };

  const getSignalColor = (signal: string) => {
    switch (signal.toLowerCase()) {
      case 'buy':
      case '买入':
      case 'bullish':
        return 'text-green-600 bg-green-100';
      case 'sell':
      case '卖出':
      case 'bearish':
        return 'text-red-600 bg-red-100';
      case 'hold':
      case '持有':
      case 'neutral':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const toggleIndicator = (indicator: string) => {
    setEnabledIndicators(prev => 
      prev.map(ind => 
        ind.name === indicator ? { ...ind, enabled: !ind.enabled } : ind
      )
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-6">技术分析</h1>
          
          {/* Search and Controls */}
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            <div className="flex-1">
              <input
                type="text"
                value={stockCode}
                onChange={(e) => setStockCode(e.target.value.toUpperCase())}
                placeholder="请输入股票代码 (如: 000001.SZ, 600000.SH)"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                onKeyPress={(e) => e.key === 'Enter' && handleAnalysis()}
              />
            </div>
            <div className="flex gap-2">
              <select
                value={timeframe}
                onChange={(e) => setTimeframe(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="1d">日线</option>
                <option value="1w">周线</option>
                <option value="1m">月线</option>
                <option value="5m">5分钟</option>
                <option value="15m">15分钟</option>
                <option value="30m">30分钟</option>
                <option value="1h">1小时</option>
              </select>
              <button
                onClick={() => handleAnalysis()}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-semibold transition-colors flex items-center gap-2"
              >
                <Search size={20} />
                {loading ? '分析中...' : '开始分析'}
              </button>
            </div>
          </div>

          {/* Indicator Controls */}
          <div className="flex flex-wrap gap-2">
            {enabledIndicators.map((indicator) => (
              <button
                key={indicator.name}
                onClick={() => toggleIndicator(indicator.name)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  indicator.enabled
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                }`}
              >
                {indicator.name.toUpperCase()}
              </button>
            ))}
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-4 flex items-center gap-2">
              <Target size={20} />
              {error}
            </div>
          )}
        </div>

        {/* Stock Info */}
        {stockInfo && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-800">{stockInfo.name}</h3>
                <p className="text-gray-600">{stockInfo.code}</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-800">¥{stockInfo.price.toFixed(2)}</p>
                <p className={`text-sm ${
                  stockInfo.change >= 0 ? 'text-red-600' : 'text-green-600'
                }`}>
                  {stockInfo.change >= 0 ? '+' : ''}{stockInfo.change.toFixed(2)} ({stockInfo.changePercent.toFixed(2)}%)
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">成交量</p>
                <p className="text-lg font-semibold">{(stockInfo.volume / 10000).toFixed(2)}万</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">市值</p>
                <p className="text-lg font-semibold">{(stockInfo.marketCap / 100000000).toFixed(2)}亿</p>
              </div>
            </div>
          </div>
        )}

        {/* Technical Indicators Summary */}
        {indicators && (
          <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-6">
            {/* KDJ */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center gap-2 mb-4">
                <Activity className="text-purple-600" size={24} />
                <h3 className="text-xl font-semibold text-gray-800">KDJ指标</h3>
              </div>
              <div className={`inline-block px-3 py-1 rounded-full text-sm font-semibold mb-3 ${
                getSignalColor(indicators.kdj.signal)
              }`}>
                {indicators.kdj.signal}
              </div>
              <p className="text-gray-600 mb-2">{indicators.kdj.description}</p>
              <div className="text-sm text-gray-500 space-y-1">
                <p>K值: {indicators.kdj.k.toFixed(2)}</p>
                <p>D值: {indicators.kdj.d.toFixed(2)}</p>
                <p>J值: {indicators.kdj.j.toFixed(2)}</p>
              </div>
            </div>

            {/* RSI */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center gap-2 mb-4">
                <TrendingUp className="text-orange-600" size={24} />
                <h3 className="text-xl font-semibold text-gray-800">RSI指标</h3>
              </div>
              <div className={`inline-block px-3 py-1 rounded-full text-sm font-semibold mb-3 ${
                getSignalColor(indicators.rsi.signal)
              }`}>
                {indicators.rsi.signal}
              </div>
              <p className="text-gray-600 mb-2">{indicators.rsi.description}</p>
              <div className="text-sm text-gray-500 space-y-1">
                <p>RSI值: {indicators.rsi.value.toFixed(2)}</p>
                <p>超买: {indicators.rsi.overbought ? '是' : '否'}</p>
                <p>超卖: {indicators.rsi.oversold ? '是' : '否'}</p>
              </div>
            </div>

            {/* Bollinger Bands */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center gap-2 mb-4">
                <BarChart3 className="text-indigo-600" size={24} />
                <h3 className="text-xl font-semibold text-gray-800">布林带</h3>
              </div>
              <div className={`inline-block px-3 py-1 rounded-full text-sm font-semibold mb-3 ${
                getSignalColor(indicators.bollingerBands.signal)
              }`}>
                {indicators.bollingerBands.signal}
              </div>
              <p className="text-gray-600 mb-2">{indicators.bollingerBands.description}</p>
              <div className="text-sm text-gray-500 space-y-1">
                <p>上轨: {indicators.bollingerBands.upper.toFixed(2)}</p>
                <p>中轨: {indicators.bollingerBands.middle.toFixed(2)}</p>
                <p>下轨: {indicators.bollingerBands.lower.toFixed(2)}</p>
                <p>%B: {(indicators.bollingerBands.percentB * 100).toFixed(2)}%</p>
              </div>
            </div>

            {/* MACD */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center gap-2 mb-4">
                <BarChart3 className="text-blue-600" size={24} />
                <h3 className="text-xl font-semibold text-gray-800">MACD</h3>
              </div>
              <div className={`inline-block px-3 py-1 rounded-full text-sm font-semibold mb-3 ${
                getSignalColor(indicators.macd.trend)
              }`}>
                {indicators.macd.trend}
              </div>
              <p className="text-gray-600 mb-2">{indicators.macd.description}</p>
              <div className="text-sm text-gray-500 space-y-1">
                <p>MACD: {indicators.macd.macd.toFixed(4)}</p>
                <p>信号线: {indicators.macd.signal.toFixed(4)}</p>
                <p>柱状图: {indicators.macd.histogram.toFixed(4)}</p>
                <p>背离: {indicators.macd.divergence ? '是' : '否'}</p>
              </div>
            </div>

            {/* Magic Nine Turns */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center gap-2 mb-4">
                <Target className="text-green-600" size={24} />
                <h3 className="text-xl font-semibold text-gray-800">神奇九转</h3>
              </div>
              <div className={`inline-block px-3 py-1 rounded-full text-sm font-semibold mb-3 ${
                getSignalColor(indicators.magicNineTurns.signal)
              }`}>
                {indicators.magicNineTurns.signal}
              </div>
              <p className="text-gray-600 mb-2">{indicators.magicNineTurns.description}</p>
              <div className="text-sm text-gray-500 space-y-1">
                <p>计数: {indicators.magicNineTurns.count}</p>
                <p>方向: {indicators.magicNineTurns.direction}</p>
                <p>强度: {indicators.magicNineTurns.strength}/10</p>
              </div>
            </div>
          </div>
        )}

        {/* Charts */}
        {candlestickData.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <TechnicalChart
              stockCode={stockCode}
              candlestickData={candlestickData}
              volumeData={volumeData}
              indicators={indicatorData}
              enabledIndicators={enabledIndicators}
              height={600}
              onIndicatorToggle={toggleIndicator}
            />
          </div>
        )}
      </div>
    </div>
  );
}