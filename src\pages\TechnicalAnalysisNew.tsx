import React, { useState, useEffect } from 'react';
import { Search, TrendingUp, BarChart3, Activity, Target, RefreshCw } from 'lucide-react';
import { TechnicalChart } from '../components/Chart';
import { useTechnicalIndicators, useStockInfo } from '../hooks/useApi';
import { handleApiError } from '../lib/api';

interface EnabledIndicator {
  name: string;
  enabled: boolean;
  color: string;
  yAxisID: string;
}

export default function TechnicalAnalysis() {
  const [stockCode, setStockCode] = useState('');
  const [timeframe, setTimeframe] = useState('1d');
  const [enabledIndicators, setEnabledIndicators] = useState<EnabledIndicator[]>([
    { name: 'MACD', enabled: true, color: '#ff7f0e', yAxisID: 'macd' },
    { name: 'RSI', enabled: true, color: '#9467bd', yAxisID: 'rsi' },
    { name: 'KD<PERSON>', enabled: true, color: '#8c564b', yAxisID: 'kdj' },
    { name: 'Bollinger Bands', enabled: true, color: '#bcbd22', yAxisID: 'price' },
  ]);

  // Use custom hooks for API calls
  const {
    data: stockInfoData,
    loading: stockInfoLoading,
    error: stockInfoError,
    refetch: refetchStockInfo
  } = useStockInfo(stockCode, 'daily', 100, { immediate: false });

  const {
    data: indicatorsData,
    loading: indicatorsLoading,
    error: indicatorsError,
    refetch: refetchIndicators
  } = useTechnicalIndicators(stockCode, [], timeframe, { immediate: false });

  const loading = stockInfoLoading || indicatorsLoading;
  const error = stockInfoError || indicatorsError;

  // Handle URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const stockParam = urlParams.get('stock');
    if (stockParam) {
      setStockCode(stockParam);
    }
  }, []);

  // Fetch data when stock code changes
  useEffect(() => {
    if (stockCode.trim()) {
      refetchStockInfo();
      refetchIndicators();
    }
  }, [stockCode, timeframe, refetchStockInfo, refetchIndicators]);

  const handleAnalysis = async () => {
    if (!stockCode.trim()) {
      return;
    }
    
    await Promise.all([refetchStockInfo(), refetchIndicators()]);
  };

  const handleIndicatorToggle = (indicatorName: string) => {
    setEnabledIndicators(prev =>
      prev.map(indicator =>
        indicator.name === indicatorName
          ? { ...indicator, enabled: !indicator.enabled }
          : indicator
      )
    );
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAnalysis();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">技术分析</h1>
              <p className="text-gray-600">深度技术指标分析，精准把握交易时机</p>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={timeframe}
                onChange={(e) => setTimeframe(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="1d">日线</option>
                <option value="1w">周线</option>
                <option value="1M">月线</option>
              </select>
            </div>
          </div>

          {/* Search Bar */}
          <div className="flex gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={stockCode}
                onChange={(e) => setStockCode(e.target.value.toUpperCase())}
                onKeyPress={handleKeyPress}
                placeholder="输入股票代码 (如: 000001, 600036)"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button
              onClick={handleAnalysis}
              disabled={loading || !stockCode.trim()}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {loading ? (
                <RefreshCw className="w-5 h-5 animate-spin" />
              ) : (
                <BarChart3 className="w-5 h-5" />
              )}
              {loading ? '分析中...' : '开始分析'}
            </button>
          </div>

          {/* Indicator Controls */}
          <div className="flex flex-wrap gap-2">
            {enabledIndicators.map((indicator) => (
              <button
                key={indicator.name}
                onClick={() => handleIndicatorToggle(indicator.name)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  indicator.enabled
                    ? 'bg-blue-100 text-blue-800 border border-blue-200'
                    : 'bg-gray-100 text-gray-600 border border-gray-200'
                }`}
              >
                {indicator.name}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <div className="text-red-800">
                <strong>错误:</strong> {error}
              </div>
            </div>
          </div>
        )}

        {loading && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <p className="mt-4 text-gray-600">正在加载技术分析数据...</p>
          </div>
        )}

        {!loading && !error && stockInfoData && indicatorsData && (
          <div className="space-y-8">
            {/* Stock Info Card */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    {stockInfoData.name} ({stockInfoData.code})
                  </h2>
                  <p className="text-gray-600">最后更新: {new Date(stockInfoData.last_updated).toLocaleString()}</p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">
                    ¥{stockInfoData.price_data?.[0]?.close?.toFixed(2) || 'N/A'}
                  </div>
                </div>
              </div>
            </div>

            {/* Technical Chart */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">技术分析图表</h3>
              <TechnicalChart
                stockCode={stockCode}
                candlestickData={stockInfoData.price_data || []}
                volumeData={stockInfoData.price_data?.map(d => ({ date: d.date, volume: d.volume })) || []}
                indicators={indicatorsData.indicators || {}}
                enabledIndicators={enabledIndicators}
                height={600}
                onIndicatorToggle={handleIndicatorToggle}
              />
            </div>

            {/* Signals Summary */}
            {stockInfoData.signals && stockInfoData.signals.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">交易信号</h3>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {stockInfoData.signals.map((signal: any, index: number) => (
                    <div
                      key={index}
                      className={`p-4 rounded-lg border ${
                        signal.type === 'buy'
                          ? 'bg-green-50 border-green-200'
                          : signal.type === 'sell'
                          ? 'bg-red-50 border-red-200'
                          : 'bg-gray-50 border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className={`font-medium ${
                          signal.type === 'buy' ? 'text-green-800' :
                          signal.type === 'sell' ? 'text-red-800' : 'text-gray-800'
                        }`}>
                          {signal.type === 'buy' ? '买入信号' : 
                           signal.type === 'sell' ? '卖出信号' : '观望'}
                        </span>
                        <span className="text-sm text-gray-600">
                          强度: {(signal.strength * 100).toFixed(0)}%
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">{signal.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {!loading && !error && !stockInfoData && stockCode && (
          <div className="text-center py-12">
            <Activity className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">未找到数据</h3>
            <p className="text-gray-600">请检查股票代码是否正确</p>
          </div>
        )}

        {!stockCode && !loading && (
          <div className="text-center py-12">
            <Target className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">开始技术分析</h3>
            <p className="text-gray-600">输入股票代码开始深度技术分析</p>
          </div>
        )}
      </div>
    </div>
  );
}
