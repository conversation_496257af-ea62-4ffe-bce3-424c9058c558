import React, { useState, useEffect } from 'react';
import { User, Settings, History, Star, TrendingUp, Bar<PERSON>hart3, Bell, Shield, Download } from 'lucide-react';

interface UserProfile {
  id: string;
  username: string;
  email: string;
  membershipType: 'basic' | 'premium';
  joinDate: string;
  lastLogin: string;
}

interface AnalysisHistory {
  id: string;
  stockCode: string;
  stockName: string;
  analysisDate: string;
  magicNineTurns: {
    signal: string;
    strength: number;
  };
  macd: {
    signal: string;
    strength: number;
  };
  combinedSignal: {
    action: string;
    confidence: number;
  };
}

interface WatchlistItem {
  stockCode: string;
  stockName: string;
  currentPrice: number;
  changePercent: number;
  addedDate: string;
  alerts: {
    priceAlert: boolean;
    signalAlert: boolean;
  };
}

export default function UserCenter() {
  const [activeTab, setActiveTab] = useState('profile');
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [analysisHistory, setAnalysisHistory] = useState<AnalysisHistory[]>([]);
  const [watchlist, setWatchlist] = useState<WatchlistItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Mock data for demonstration
  useEffect(() => {
    // Simulate loading user data
    setUserProfile({
      id: 'user_001',
      username: '投资者',
      email: '<EMAIL>',
      membershipType: 'basic',
      joinDate: '2024-01-15',
      lastLogin: '2024-01-20'
    });

    setAnalysisHistory([
      {
        id: 'analysis_001',
        stockCode: '000001.SZ',
        stockName: '平安银行',
        analysisDate: '2024-01-20',
        magicNineTurns: { signal: '买入', strength: 8 },
        macd: { signal: '买入', strength: 7 },
        combinedSignal: { action: '买入', confidence: 85 }
      },
      {
        id: 'analysis_002',
        stockCode: '600000.SH',
        stockName: '浦发银行',
        analysisDate: '2024-01-19',
        magicNineTurns: { signal: '观望', strength: 5 },
        macd: { signal: '卖出', strength: 6 },
        combinedSignal: { action: '观望', confidence: 45 }
      }
    ]);

    setWatchlist([
      {
        stockCode: '000001.SZ',
        stockName: '平安银行',
        currentPrice: 12.45,
        changePercent: 2.3,
        addedDate: '2024-01-15',
        alerts: { priceAlert: true, signalAlert: true }
      },
      {
        stockCode: '600036.SH',
        stockName: '招商银行',
        currentPrice: 35.67,
        changePercent: -1.2,
        addedDate: '2024-01-18',
        alerts: { priceAlert: false, signalAlert: true }
      }
    ]);
  }, []);

  const handleUpgrade = () => {
    alert('升级功能开发中...');
  };

  const handleExportHistory = () => {
    if (analysisHistory.length === 0) return;
    
    const csvContent = [
      ['分析日期', '股票代码', '股票名称', '神奇九转信号', '神奇九转强度', 'MACD信号', 'MACD强度', '综合信号', '信心度'].join(','),
      ...analysisHistory.map(item => [
        item.analysisDate,
        item.stockCode,
        item.stockName,
        item.magicNineTurns.signal,
        item.magicNineTurns.strength,
        item.macd.signal,
        item.macd.strength,
        item.combinedSignal.action,
        item.combinedSignal.confidence
      ].join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `analysis_history_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const removeFromWatchlist = (stockCode: string) => {
    setWatchlist(prev => prev.filter(item => item.stockCode !== stockCode));
  };

  const toggleAlert = (stockCode: string, alertType: 'priceAlert' | 'signalAlert') => {
    setWatchlist(prev => prev.map(item => 
      item.stockCode === stockCode 
        ? { ...item, alerts: { ...item.alerts, [alertType]: !item.alerts[alertType] } }
        : item
    ));
  };

  const getSignalColor = (signal: string) => {
    switch (signal.toLowerCase()) {
      case 'buy':
      case '买入':
        return 'text-green-600 bg-green-100';
      case 'sell':
      case '卖出':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const tabs = [
    { id: 'profile', label: '个人资料', icon: User },
    { id: 'history', label: '分析历史', icon: History },
    { id: 'watchlist', label: '自选股', icon: Star },
    { id: 'settings', label: '设置', icon: Settings }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">用户中心</h1>
          {userProfile && (
            <p className="text-gray-600">欢迎回来，{userProfile.username}！</p>
          )}
        </div>

        <div className="grid lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-100 text-blue-700 font-semibold'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Icon size={20} />
                      {tab.label}
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-md p-6">
              {/* Profile Tab */}
              {activeTab === 'profile' && userProfile && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
                    <User size={24} />
                    个人资料
                  </h2>
                  
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input
                          type="text"
                          value={userProfile.username}
                          readOnly
                          className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                        <input
                          type="email"
                          value={userProfile.email}
                          readOnly
                          className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">注册日期</label>
                        <input
                          type="text"
                          value={userProfile.joinDate}
                          readOnly
                          className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <div className="border border-gray-200 rounded-lg p-4">
                        <h3 className="font-semibold text-gray-800 mb-2">会员状态</h3>
                        <div className="flex items-center justify-between mb-3">
                          <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                            userProfile.membershipType === 'premium'
                              ? 'bg-gold-100 text-gold-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {userProfile.membershipType === 'premium' ? '高级会员' : '基础会员'}
                          </span>
                        </div>
                        
                        {userProfile.membershipType === 'basic' && (
                          <div className="space-y-2">
                            <p className="text-sm text-gray-600">升级到高级会员享受更多功能：</p>
                            <ul className="text-sm text-gray-600 space-y-1">
                              <li>• 无限制股票筛选</li>
                              <li>• 高级技术指标</li>
                              <li>• 实时价格提醒</li>
                              <li>• 专业分析报告</li>
                            </ul>
                            <button
                              onClick={handleUpgrade}
                              className="w-full bg-gold-600 hover:bg-gold-700 text-white py-2 px-4 rounded-lg font-semibold transition-colors mt-3"
                            >
                              立即升级
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* History Tab */}
              {activeTab === 'history' && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
                      <History size={24} />
                      分析历史
                    </h2>
                    
                    {analysisHistory.length > 0 && (
                      <button
                        onClick={handleExportHistory}
                        className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center gap-2"
                      >
                        <Download size={16} />
                        导出历史
                      </button>
                    )}
                  </div>
                  
                  {analysisHistory.length === 0 ? (
                    <div className="text-center py-12">
                      <BarChart3 size={48} className="mx-auto text-gray-400 mb-4" />
                      <p className="text-gray-500">暂无分析历史</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="w-full table-auto">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">股票</th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">分析日期</th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">神奇九转</th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">MACD</th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">综合信号</th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">操作</th>
                          </tr>
                        </thead>
                        <tbody>
                          {analysisHistory.map((item) => (
                            <tr key={item.id} className="border-t border-gray-200 hover:bg-gray-50">
                              <td className="px-4 py-3">
                                <div>
                                  <div className="font-semibold text-gray-800">{item.stockName}</div>
                                  <div className="text-sm text-gray-500">{item.stockCode}</div>
                                </div>
                              </td>
                              <td className="px-4 py-3">
                                <div className="text-sm">{item.analysisDate}</div>
                              </td>
                              <td className="px-4 py-3">
                                <div className={`inline-block px-2 py-1 rounded text-xs font-semibold ${
                                  getSignalColor(item.magicNineTurns.signal)
                                }`}>
                                  {item.magicNineTurns.signal}
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                  强度: {item.magicNineTurns.strength}/10
                                </div>
                              </td>
                              <td className="px-4 py-3">
                                <div className={`inline-block px-2 py-1 rounded text-xs font-semibold ${
                                  getSignalColor(item.macd.signal)
                                }`}>
                                  {item.macd.signal}
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                  强度: {item.macd.strength}/10
                                </div>
                              </td>
                              <td className="px-4 py-3">
                                <div className={`inline-block px-2 py-1 rounded text-xs font-semibold ${
                                  getSignalColor(item.combinedSignal.action)
                                }`}>
                                  {item.combinedSignal.action}
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                  信心度: {item.combinedSignal.confidence}%
                                </div>
                              </td>
                              <td className="px-4 py-3">
                                <button
                                  onClick={() => window.location.href = `/analysis?stock=${item.stockCode}`}
                                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                >
                                  重新分析
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              )}

              {/* Watchlist Tab */}
              {activeTab === 'watchlist' && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
                    <Star size={24} />
                    自选股 ({watchlist.length})
                  </h2>
                  
                  {watchlist.length === 0 ? (
                    <div className="text-center py-12">
                      <Star size={48} className="mx-auto text-gray-400 mb-4" />
                      <p className="text-gray-500">暂无自选股</p>
                      <p className="text-sm text-gray-400 mt-2">在股票分析页面添加感兴趣的股票到自选股</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {watchlist.map((item) => (
                        <div key={item.stockCode} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="flex items-center gap-4 mb-2">
                                <div>
                                  <h3 className="font-semibold text-gray-800">{item.stockName}</h3>
                                  <p className="text-sm text-gray-500">{item.stockCode}</p>
                                </div>
                                <div className="text-right">
                                  <div className="font-semibold text-lg">¥{item.currentPrice.toFixed(2)}</div>
                                  <div className={`text-sm font-semibold ${
                                    item.changePercent >= 0 ? 'text-red-600' : 'text-green-600'
                                  }`}>
                                    {item.changePercent >= 0 ? '+' : ''}{item.changePercent.toFixed(2)}%
                                  </div>
                                </div>
                              </div>
                              
                              <div className="flex items-center gap-4 text-sm text-gray-600">
                                <span>添加日期: {item.addedDate}</span>
                                <div className="flex items-center gap-2">
                                  <Bell size={14} />
                                  <label className="flex items-center gap-1">
                                    <input
                                      type="checkbox"
                                      checked={item.alerts.priceAlert}
                                      onChange={() => toggleAlert(item.stockCode, 'priceAlert')}
                                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    />
                                    价格提醒
                                  </label>
                                  <label className="flex items-center gap-1">
                                    <input
                                      type="checkbox"
                                      checked={item.alerts.signalAlert}
                                      onChange={() => toggleAlert(item.stockCode, 'signalAlert')}
                                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    />
                                    信号提醒
                                  </label>
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex gap-2">
                              <button
                                onClick={() => window.location.href = `/analysis?stock=${item.stockCode}`}
                                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                              >
                                分析
                              </button>
                              <button
                                onClick={() => removeFromWatchlist(item.stockCode)}
                                className="text-red-600 hover:text-red-800 text-sm font-medium"
                              >
                                移除
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Settings Tab */}
              {activeTab === 'settings' && (
                <div className="space-y-6">
                  <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
                    <Settings size={24} />
                    设置
                  </h2>
                  
                  <div className="space-y-6">
                    {/* Notification Settings */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-800 mb-4 flex items-center gap-2">
                        <Bell size={20} />
                        通知设置
                      </h3>
                      
                      <div className="space-y-3">
                        <label className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            defaultChecked
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-gray-700">接收买卖信号通知</span>
                        </label>
                        
                        <label className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            defaultChecked
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-gray-700">接收价格提醒</span>
                        </label>
                        
                        <label className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                          <span className="text-gray-700">接收市场资讯</span>
                        </label>
                      </div>
                    </div>
                    
                    {/* Security Settings */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-800 mb-4 flex items-center gap-2">
                        <Shield size={20} />
                        安全设置
                      </h3>
                      
                      <div className="space-y-3">
                        <button className="w-full text-left px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                          修改密码
                        </button>
                        
                        <button className="w-full text-left px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                          绑定手机号
                        </button>
                        
                        <button className="w-full text-left px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                          两步验证
                        </button>
                      </div>
                    </div>
                    
                    {/* Data Settings */}
                    <div className="border border-gray-200 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-800 mb-4">数据设置</h3>
                      
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-700">清除分析历史</span>
                          <button className="text-red-600 hover:text-red-800 text-sm font-medium">
                            清除
                          </button>
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <span className="text-gray-700">导出个人数据</span>
                          <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            导出
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}