/**
 * Global application state management using Zustand
 * Provides centralized state for trading agent application
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { StockInfo, TechnicalIndicators, CandlestickData } from '@/lib/api';

// Types for state management
export interface StockData {
  info: StockInfo | null;
  priceData: CandlestickData[];
  indicators: TechnicalIndicators | null;
  signals: any[];
  lastUpdated: string | null;
}

export interface MarketData {
  overview: any | null;
  sentiment: any | null;
  capitalFlow: any | null;
  popularStocks: StockInfo[];
  lastUpdated: string | null;
}

export interface UserPreferences {
  theme: 'light' | 'dark';
  defaultTimeframe: string;
  enabledIndicators: string[];
  favoriteStocks: string[];
  language: 'zh' | 'en';
}

export interface AppState {
  // Current stock analysis state
  currentStock: {
    code: string;
    data: StockData;
    loading: boolean;
    error: string | null;
  };
  
  // Market data state
  market: {
    data: MarketData;
    loading: boolean;
    error: string | null;
  };
  
  // User preferences
  preferences: UserPreferences;
  
  // UI state
  ui: {
    sidebarOpen: boolean;
    activeTab: string;
    notifications: Array<{
      id: string;
      type: 'success' | 'error' | 'warning' | 'info';
      message: string;
      timestamp: number;
    }>;
  };
  
  // Actions
  setCurrentStock: (code: string) => void;
  setStockData: (data: Partial<StockData>) => void;
  setStockLoading: (loading: boolean) => void;
  setStockError: (error: string | null) => void;
  clearStockData: () => void;
  
  setMarketData: (data: Partial<MarketData>) => void;
  setMarketLoading: (loading: boolean) => void;
  setMarketError: (error: string | null) => void;
  
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  addFavoriteStock: (code: string) => void;
  removeFavoriteStock: (code: string) => void;
  
  setSidebarOpen: (open: boolean) => void;
  setActiveTab: (tab: string) => void;
  addNotification: (notification: Omit<AppState['ui']['notifications'][0], 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

// Default state values
const defaultStockData: StockData = {
  info: null,
  priceData: [],
  indicators: null,
  signals: [],
  lastUpdated: null,
};

const defaultMarketData: MarketData = {
  overview: null,
  sentiment: null,
  capitalFlow: null,
  popularStocks: [],
  lastUpdated: null,
};

const defaultPreferences: UserPreferences = {
  theme: 'light',
  defaultTimeframe: '1d',
  enabledIndicators: ['MACD', 'RSI', 'KDJ', 'Bollinger Bands'],
  favoriteStocks: [],
  language: 'zh',
};

// Create the store
export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        currentStock: {
          code: '',
          data: defaultStockData,
          loading: false,
          error: null,
        },
        
        market: {
          data: defaultMarketData,
          loading: false,
          error: null,
        },
        
        preferences: defaultPreferences,
        
        ui: {
          sidebarOpen: false,
          activeTab: 'analysis',
          notifications: [],
        },
        
        // Stock actions
        setCurrentStock: (code: string) => {
          set((state) => ({
            currentStock: {
              ...state.currentStock,
              code,
              error: null,
            },
          }));
        },
        
        setStockData: (data: Partial<StockData>) => {
          set((state) => ({
            currentStock: {
              ...state.currentStock,
              data: {
                ...state.currentStock.data,
                ...data,
                lastUpdated: new Date().toISOString(),
              },
            },
          }));
        },
        
        setStockLoading: (loading: boolean) => {
          set((state) => ({
            currentStock: {
              ...state.currentStock,
              loading,
            },
          }));
        },
        
        setStockError: (error: string | null) => {
          set((state) => ({
            currentStock: {
              ...state.currentStock,
              error,
              loading: false,
            },
          }));
        },
        
        clearStockData: () => {
          set((state) => ({
            currentStock: {
              ...state.currentStock,
              data: defaultStockData,
              error: null,
            },
          }));
        },
        
        // Market actions
        setMarketData: (data: Partial<MarketData>) => {
          set((state) => ({
            market: {
              ...state.market,
              data: {
                ...state.market.data,
                ...data,
                lastUpdated: new Date().toISOString(),
              },
            },
          }));
        },
        
        setMarketLoading: (loading: boolean) => {
          set((state) => ({
            market: {
              ...state.market,
              loading,
            },
          }));
        },
        
        setMarketError: (error: string | null) => {
          set((state) => ({
            market: {
              ...state.market,
              error,
              loading: false,
            },
          }));
        },
        
        // Preferences actions
        updatePreferences: (preferences: Partial<UserPreferences>) => {
          set((state) => ({
            preferences: {
              ...state.preferences,
              ...preferences,
            },
          }));
        },
        
        addFavoriteStock: (code: string) => {
          set((state) => ({
            preferences: {
              ...state.preferences,
              favoriteStocks: [...new Set([...state.preferences.favoriteStocks, code])],
            },
          }));
        },
        
        removeFavoriteStock: (code: string) => {
          set((state) => ({
            preferences: {
              ...state.preferences,
              favoriteStocks: state.preferences.favoriteStocks.filter(stock => stock !== code),
            },
          }));
        },
        
        // UI actions
        setSidebarOpen: (open: boolean) => {
          set((state) => ({
            ui: {
              ...state.ui,
              sidebarOpen: open,
            },
          }));
        },
        
        setActiveTab: (tab: string) => {
          set((state) => ({
            ui: {
              ...state.ui,
              activeTab: tab,
            },
          }));
        },
        
        addNotification: (notification) => {
          const id = Math.random().toString(36).substr(2, 9);
          const timestamp = Date.now();
          
          set((state) => ({
            ui: {
              ...state.ui,
              notifications: [
                ...state.ui.notifications,
                { ...notification, id, timestamp },
              ],
            },
          }));
          
          // Auto-remove notification after 5 seconds
          setTimeout(() => {
            get().removeNotification(id);
          }, 5000);
        },
        
        removeNotification: (id: string) => {
          set((state) => ({
            ui: {
              ...state.ui,
              notifications: state.ui.notifications.filter(n => n.id !== id),
            },
          }));
        },
        
        clearNotifications: () => {
          set((state) => ({
            ui: {
              ...state.ui,
              notifications: [],
            },
          }));
        },
      }),
      {
        name: 'trading-agent-store',
        partialize: (state) => ({
          preferences: state.preferences,
          ui: {
            activeTab: state.ui.activeTab,
          },
        }),
      }
    ),
    {
      name: 'trading-agent-store',
    }
  )
);

// Selector hooks for better performance
export const useCurrentStock = () => useAppStore((state) => state.currentStock);
export const useMarketData = () => useAppStore((state) => state.market);
export const usePreferences = () => useAppStore((state) => state.preferences);
export const useUI = () => useAppStore((state) => state.ui);

// Action hooks
export const useStockActions = () => useAppStore((state) => ({
  setCurrentStock: state.setCurrentStock,
  setStockData: state.setStockData,
  setStockLoading: state.setStockLoading,
  setStockError: state.setStockError,
  clearStockData: state.clearStockData,
}));

export const useMarketActions = () => useAppStore((state) => ({
  setMarketData: state.setMarketData,
  setMarketLoading: state.setMarketLoading,
  setMarketError: state.setMarketError,
}));

export const usePreferenceActions = () => useAppStore((state) => ({
  updatePreferences: state.updatePreferences,
  addFavoriteStock: state.addFavoriteStock,
  removeFavoriteStock: state.removeFavoriteStock,
}));

export const useUIActions = () => useAppStore((state) => ({
  setSidebarOpen: state.setSidebarOpen,
  setActiveTab: state.setActiveTab,
  addNotification: state.addNotification,
  removeNotification: state.removeNotification,
  clearNotifications: state.clearNotifications,
}));
